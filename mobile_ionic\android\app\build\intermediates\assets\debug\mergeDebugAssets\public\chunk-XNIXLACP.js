import{a as ot}from"./chunk-GEGX2RE6.js";import{a as nt,c as rt}from"./chunk-YZWAW4SM.js";import{a as at}from"./chunk-QZ5PEPJK.js";import{b as tt}from"./chunk-I4SN7ED3.js";import{a as et}from"./chunk-LSM7X32V.js";import"./chunk-3J7GGTVR.js";import{a as Z}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{B as s,Eb as V,F as P,G as f,I as y,Ib as G,J as a,Jb as B,K as i,L as _,O as E,P as C,Q as m,X as u,Y as v,Z as T,Zb as W,bc as j,ca as S,d as I,dc as J,fc as Y,gc as X,ib as K,l as x,m as L,ma as q,na as D,pa as $,r as w,ra as z,s as b,wa as U,wb as H,xb as Q,ya as F}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as N,b as R,f as it,g as p}from"./chunk-2R6CW7ES.js";var d=it(rt());function st(r,M){if(r&1){let t=E();a(0,"app-real-time-navigation",26),C("routeUpdated",function(e){w(t);let o=m();return b(o.onNavigationRouteUpdated(e))})("navigationStopped",function(){w(t);let e=m();return b(e.onNavigationStopped())}),i()}if(r&2){let t=m();f("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function ct(r,M){if(r&1&&(a(0,"span",35),u(1),i()),r&2){let t=m().$implicit,n=m();s(),T(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function lt(r,M){if(r&1){let t=E();a(0,"div",27),C("click",function(){let e=w(t).$implicit,o=m();return b(o.selectCenterFromList(e))}),a(1,"div",28)(2,"h4"),u(3),i(),a(4,"p",29),u(5),i(),a(6,"div",30),P(7,ct,2,1,"span",31),a(8,"span",32),u(9),i()()(),a(10,"div",33),_(11,"ion-icon",34),i()()}if(r&2){let t=M.$implicit,n=m();s(3),v(t.name),s(2),v(t.address),s(2),f("ngIf",n.userLocation),s(2),T("\u{1F465} ",t.capacity||"N/A"," capacity")}}function gt(r,M){if(r&1&&(a(0,"div",28)(1,"h3"),u(2),i(),a(3,"p"),u(4),i()()),r&2){let t=m();s(2),v(t.selectedCenter.name),s(2),v(t.selectedCenter.address)}}function ut(r,M){if(r&1&&(a(0,"div",46)(1,"span",47),u(2),i(),a(3,"span",35),u(4),i()()),r&2){let t=m(2);s(2),v(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),v(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function pt(r,M){if(r&1&&(a(0,"div",46)(1,"span",47),u(2),i(),a(3,"span",35),u(4),i()()),r&2){let t=m(2);s(2),v(t.formatTime(t.routeInfo.cycling==null?null:t.routeInfo.cycling.duration)),s(2),v(t.formatDistance(t.routeInfo.cycling==null?null:t.routeInfo.cycling.distance))}}function dt(r,M){if(r&1&&(a(0,"div",46)(1,"span",47),u(2),i(),a(3,"span",35),u(4),i()()),r&2){let t=m(2);s(2),v(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),v(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function ht(r,M){if(r&1){let t=E();a(0,"button",48),C("click",function(){w(t);let e=m(2);return b(e.startRealTimeNavigation(e.selectedCenter))}),_(1,"ion-icon",49),a(2,"span"),u(3,"Start Real-time Navigation"),i()()}}function mt(r,M){if(r&1){let t=E();a(0,"div",36)(1,"div",37),_(2,"ion-icon",38),a(3,"span"),u(4,"Choose Transportation"),i()(),a(5,"div",39)(6,"button",40),C("click",function(){w(t);let e=m();return b(e.selectTransportMode("walking"))}),_(7,"ion-icon",41),a(8,"span"),u(9,"Walk"),i(),P(10,ut,5,2,"div",42),i(),a(11,"button",40),C("click",function(){w(t);let e=m();return b(e.selectTransportMode("cycling"))}),_(12,"ion-icon",43),a(13,"span"),u(14,"Cycle"),i(),P(15,pt,5,2,"div",42),i(),a(16,"button",40),C("click",function(){w(t);let e=m();return b(e.selectTransportMode("driving"))}),_(17,"ion-icon",44),a(18,"span"),u(19,"Drive"),i(),P(20,dt,5,2,"div",42),i()(),P(21,ht,4,0,"button",45),i()}if(r&2){let t=m();s(6),y("active",t.selectedTransportMode==="walking"),s(4),f("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),y("active",t.selectedTransportMode==="cycling"),s(4),f("ngIf",t.routeInfo&&t.selectedTransportMode==="cycling"),s(),y("active",t.selectedTransportMode==="driving"),s(4),f("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),f("ngIf",t.selectedTransportMode&&t.routeInfo&&t.selectedCenter)}}function ft(r,M){if(r&1&&(a(0,"span",47),u(1),i()),r&2){let t=m(2);s(),T(" ",t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)," ")}}function _t(r,M){if(r&1&&(a(0,"span",35),u(1),i()),r&2){let t=m(2);s(),T(" (",t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance),") ")}}function Ct(r,M){if(r&1){let t=E();a(0,"div",50)(1,"div",51)(2,"div",52),_(3,"ion-icon",53),i(),a(4,"div",54)(5,"div",55),u(6),i(),a(7,"div",56),P(8,ft,2,1,"span",57)(9,_t,2,1,"span",31),i()()(),a(10,"div",58)(11,"ion-button",59),C("click",function(){w(t);let e=m();return b(e.startRealTimeNavigation(e.selectedCenter))}),_(12,"ion-icon",60),u(13," Start "),i()()()}if(r&2){let t=m();s(3),f("name",t.selectedTransportMode==="walking"?"walk-outline":t.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),v(t.selectedCenter.name),s(2),f("ngIf",t.routeInfo[t.selectedTransportMode]),s(),f("ngIf",t.routeInfo[t.selectedTransportMode])}}var qt=(()=>{class r{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.shouldAutoRouteEmergency=!1,this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(J),this.toastCtrl=x(Y),this.alertCtrl=x(j),this.http=x(z),this.router=x(F),this.route=x(U),this.osmRouting=x(et),this.mapboxRouting=x(nt),this.enhancedDownload=x(ot)}ngOnInit(){this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null),t.emergency==="true"&&t.autoRoute==="true"&&(console.log("\u{1F6A8} Emergency navigation triggered for earthquake map"),this.shouldAutoRouteEmergency=!0)})}ngAfterViewInit(){return p(this,null,function*(){setTimeout(()=>p(this,null,function*(){yield this.loadEarthquakeMap()}),100)})}loadEarthquakeMap(){return p(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading earthquake evacuation centers...",spinner:"crescent"});yield t.present();try{console.log("\u{1F7E0} EARTHQUAKE MAP: Starting to load map...");let n=setTimeout(()=>p(this,null,function*(){yield t.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Loading timeout after 30 seconds"),yield(yield this.alertCtrl.create({header:"Loading Timeout",message:"The map is taking too long to load. This might be due to network issues or GPS problems.",buttons:[{text:"Try Offline Mode",handler:()=>this.loadOfflineMode()},{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}),3e4);console.log("\u{1F7E0} EARTHQUAKE MAP: Getting user location...");let e=yield Promise.race([tt.getCurrentPosition({enableHighAccuracy:!0,timeout:15e3}),new Promise((g,h)=>setTimeout(()=>h(new Error("GPS timeout")),15e3))]),o=e.coords.latitude,c=e.coords.longitude;console.log(`\u{1F7E0} EARTHQUAKE MAP: Got location [${o}, ${c}]`),this.userLocation={lat:o,lng:c},console.log("\u{1F7E0} EARTHQUAKE MAP: Initializing map..."),this.initializeMap(o,c),console.log("\u{1F7E0} EARTHQUAKE MAP: Loading evacuation centers..."),yield this.loadEarthquakeCenters(o,c),clearTimeout(n),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Showing ${this.evacuationCenters.length} earthquake evacuation centers`,duration:3e3,color:"warning",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading map",n);let e="Unable to load earthquake map.",o=[];n instanceof Error?n.message.includes("GPS")||n.message.includes("location")||n.message.includes("timeout")?(e="Unable to get your location. Please enable GPS and try again.",o=[{text:"Use Default Location",handler:()=>this.loadWithDefaultLocation()},{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]):(e="Network connection issue. Try offline mode or check your connection.",o=[{text:"Try Offline",handler:()=>this.loadOfflineMode()},{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]):o=[{text:"Retry",handler:()=>this.loadEarthquakeMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}],yield(yield this.alertCtrl.create({header:"Loading Error",message:e,buttons:o})).present()}})}initializeMap(t,n){if(!document.getElementById("earthquake-map"))throw console.error("\u{1F7E0} EARTHQUAKE MAP: Container #earthquake-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=d.map("earthquake-map").setView([t,n],13),d.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=d.marker([t,n],{icon:d.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadWithDefaultLocation(){return p(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE MAP: Using default Cebu City location");let t=10.3157,n=123.8854;this.userLocation={lat:t,lng:n},this.initializeMap(t,n),yield this.loadEarthquakeCenters(t,n),yield(yield this.toastCtrl.create({message:"\u{1F4CD} Using default location (Cebu City). Enable GPS for accurate location.",duration:4e3,color:"warning"})).present()})}loadOfflineMode(){return p(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE MAP: Loading in offline mode");let t=yield this.loadingCtrl.create({message:"Loading offline earthquake data...",spinner:"crescent"});yield t.present();try{this.userLocation={lat:10.3157,lng:123.8854},this.initializeMap(10.3157,123.8854),yield this.loadEarthquakeCenters(10.3157,123.8854),yield t.dismiss()}catch(n){yield t.dismiss(),console.error("\u{1F7E0} EARTHQUAKE MAP: Offline mode failed",n),yield(yield this.toastCtrl.create({message:"Failed to load offline data. Please try again.",duration:3e3,color:"danger"})).present()}})}loadEarthquakeCenters(t,n){return p(this,null,function*(){try{let e=[];try{console.log("\u{1F7E0} EARTHQUAKE MAP: Fetching from API..."),e=(yield Promise.race([I(this.http.get(`${Z.apiUrl}/evacuation-centers`)),new Promise((c,l)=>setTimeout(()=>l(new Error("API timeout")),15e3))])).data||[],console.log(`\u{1F7E0} EARTHQUAKE MAP: API returned ${e?.length||0} centers`)}catch(o){console.error("\u274C API failed:",o),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:[{text:"OK",handler:()=>this.router.navigate(["/tabs/home"])}]})).present();return}if(this.evacuationCenters=e.filter(o=>Array.isArray(o.disaster_type)?o.disaster_type.some(c=>c==="Earthquake"):o.disaster_type==="Earthquake"),console.log(`\u{1F7E0} EARTHQUAKE MAP: Filtered to ${this.evacuationCenters.length} earthquake centers`),console.log("\u{1F7E0} EARTHQUAKE MAP: Filtered centers:",this.evacuationCenters.map(o=>`${o.name} (${JSON.stringify(o.disaster_type)})`)),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Earthquake Centers",message:"No earthquake evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(t,n)}catch(e){console.error("\u{1F7E0} EARTHQUAKE MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading earthquake centers. Please check your internet connection.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(t,n){return p(this,null,function*(){if(this.evacuationCenters.forEach(e=>{let o=Number(e.latitude),c=Number(e.longitude);if(!isNaN(o)&&!isNaN(c)){let l=d.marker([o,c],{icon:d.icon({iconUrl:"assets/forEarthquake.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),g=this.calculateDistance(t,n,o,c);l.on("click",()=>{console.log("\u{1F7E0} EARTHQUAKE: Marker clicked for center:",e.name),this.showNavigationPanel(e)});let h=this.newCenterId&&e.id.toString()===this.newCenterId;l.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F7E0} ${e.name} ${h?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Earthquake Center</p>
            <p><strong>Distance:</strong> ${(g/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
            <p><em>Click marker for route options</em></p>
            ${h?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),h&&(l.openPopup(),this.map.setView([o,c],15),this.toastCtrl.create({message:`\u{1F195} New earthquake evacuation center: ${e.name}`,duration:5e3,color:"warning",position:"top"}).then(O=>O.present())),l.addTo(this.map)}}),this.shouldAutoRouteEmergency&&(console.log("\u{1F6A8} Performing emergency auto-routing to nearest earthquake evacuation centers"),yield this.performEmergencyRouting(),this.shouldAutoRouteEmergency=!1),this.evacuationCenters.length>0){let e=d.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(o=>{e.extend([Number(o.latitude),Number(o.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}})}calculateDistance(t,n,e,o){let l=t*Math.PI/180,g=e*Math.PI/180,h=(e-t)*Math.PI/180,O=(o-n)*Math.PI/180,k=Math.sin(h/2)*Math.sin(h/2)+Math.cos(l)*Math.cos(g)*Math.sin(O/2)*Math.sin(O/2);return 6371e3*(2*Math.atan2(Math.sqrt(k),Math.sqrt(1-k)))}performEmergencyRouting(){return p(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.warn("Cannot perform emergency routing: missing user location or evacuation centers");return}try{console.log("\u{1F6A8} Starting emergency routing to nearest earthquake evacuation centers"),yield(yield this.toastCtrl.create({message:"\u{1F6A8} EMERGENCY: Routing to nearest earthquake evacuation centers",duration:5e3,color:"danger",position:"top",cssClass:"emergency-toast"})).present(),yield this.routeToTwoNearestCenters(),yield(yield this.toastCtrl.create({message:"\u2705 Emergency routes calculated. Follow the highlighted paths to safety.",duration:7e3,color:"success",position:"bottom"})).present()}catch(t){console.error("Error in emergency routing:",t),yield(yield this.toastCtrl.create({message:"\u26A0\uFE0F Emergency routing failed. Please manually navigate to nearest evacuation center.",duration:5e3,color:"warning",position:"top"})).present()}})}routeToTwoNearestCenters(){return p(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E0} EARTHQUAKE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E0} EARTHQUAKE MAP: Finding 2 nearest earthquake centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No earthquake evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Showing routes to ${t.length} nearest earthquake centers`,duration:4e3,color:"warning"})).present()}catch(t){console.error("\u{1F7E0} EARTHQUAKE MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(o=>R(N({},o),{distance:this.calculateDistance(t,n,Number(o.latitude),Number(o.longitude))})).sort((o,c)=>o.distance-c.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let o=Number(n.latitude),c=Number(n.longitude);if(!isNaN(o)&&!isNaN(c)){let l=d.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #ff9500"></div>
              <img src="assets/forEarthquake.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),g=d.marker([o,c],{icon:l});g.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Earthquake</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),g.addTo(this.map),this.nearestMarkers.push(g)}})}calculateRoutes(t){return p(this,null,function*(){if(this.userLocation){this.routeLayer=d.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n],o=Number(e.latitude),c=Number(e.longitude);if(!isNaN(o)&&!isNaN(c))try{console.log(`\u{1F7E0} EARTHQUAKE MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let l=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),g=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,c,o,l);if(g&&g.routes&&g.routes.length>0){let h=g.routes[0];d.polyline(h.geometry.coordinates.map(k=>[k[1],k[0]]),{color:"#ff9500",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=h.duration,this.routeDistance=h.distance),console.log(`\u2705 EARTHQUAKE MAP: Added Mapbox route to ${e.name} (${(h.distance/1e3).toFixed(2)}km, ${(h.duration/60).toFixed(0)}min)`)}}catch(l){console.error(`\u{1F7E0} Error calculating Mapbox route to center ${n+1}:`,l)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[],this.map.eachLayer(t=>{t.options&&(t.options.color==="#ff9500"||t.isRouteLayer)&&this.map.removeLayer(t)})}openInExternalMaps(t){return p(this,null,function*(){let n=Number(t.latitude),e=Number(t.longitude),o=`https://www.google.com/maps/dir/?api=1&destination=${n},${e}&travelmode=walking`;try{window.open(o,"_system")}catch(c){console.error("Error opening external maps:",c),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}showNavigationPanel(t){return p(this,null,function*(){console.log("\u{1F7E0} EARTHQUAKE: showNavigationPanel called for:",t.name),console.log("\u{1F7E0} EARTHQUAKE: Setting selectedCenter to:",t),this.selectedCenter=t,this.selectedTransportMode=null,this.routeInfo={},console.log("\u{1F7E0} EARTHQUAKE: selectedCenter is now:",this.selectedCenter),yield this.calculateAllRoutes(t)})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1}selectTransportMode(t){return p(this,null,function*(){this.selectedTransportMode=t,this.selectedCenter&&this.routeInfo[t]&&(yield this.routeToCenter(this.selectedCenter,t))})}calculateAllRoutes(t){return p(this,null,function*(){if(!this.userLocation)return;let n=Number(t.latitude),e=Number(t.longitude);if(isNaN(n)||isNaN(e))return;let o=["walking","cycling","driving"];for(let c of o)try{let l=this.mapboxRouting.convertTravelModeToProfile(c),g=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,n,l);if(g&&g.routes&&g.routes.length>0){let h=g.routes[0];this.routeInfo[c]={duration:h.duration,distance:h.distance},console.log(`\u{1F7E0} EARTHQUAKE: ${c} route calculated - ${(h.distance/1e3).toFixed(2)}km, ${Math.round(h.duration/60)}min`)}}catch(l){console.error(`\u{1F7E0} EARTHQUAKE: Error calculating ${c} route:`,l)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let n=t/1e3;return n<1?`${Math.round(t)} m`:`${n.toFixed(1)} km`}startNavigation(){return p(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"warning",position:"top"})).present()})}routeToCenter(t,n){return p(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let e=Number(t.latitude),o=Number(t.longitude);if(!isNaN(e)&&!isNaN(o)){console.log(`\u{1F7E0} EARTHQUAKE: Creating Mapbox route to ${t.name} via ${n}`);let c=this.mapboxRouting.convertTravelModeToProfile(n),l=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,o,e,c);if(l&&l.routes&&l.routes.length>0){let g=l.routes[0],h="#ff9500";this.routeLayer=d.layerGroup().addTo(this.map);let O=d.polyline(g.geometry.coordinates.map(A=>[A[1],A[0]]),{color:h,weight:5,opacity:.8});O.isRouteLayer=!0,O.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F7E0} Route: ${(g.distance/1e3).toFixed(2)}km, ${(g.duration/60).toFixed(0)}min via ${n}`,duration:4e3,color:"warning"})).present(),this.map.fitBounds(O.getBounds(),{padding:[50,50]}),console.log(`\u2705 EARTHQUAKE: Successfully created route with ${g.geometry.coordinates.length} points`)}}}catch(e){console.error("\u{1F7E0} Error routing to earthquake center:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return p(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F7E0} Travel mode changed to ${t}`,duration:2e3,color:"warning"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showAllCenters(){this.showAllCentersPanel=!0,this.selectedCenter=null}closeAllCentersPanel(){this.showAllCentersPanel=!1}selectCenterFromList(t){return p(this,null,function*(){this.showAllCentersPanel=!1,yield this.showNavigationPanel(t);let n=Number(t.latitude),e=Number(t.longitude);!isNaN(n)&&!isNaN(e)&&this.map.setView([n,e],16)})}calculateDistanceInKm(t){if(!this.userLocation)return"N/A";let n=Number(t.latitude),e=Number(t.longitude);return isNaN(n)||isNaN(e)?"N/A":(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,n,e)/1e3).toFixed(1)}routeToNearestCenters(){return p(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{let t=yield this.loadingCtrl.create({message:"Calculating routes to nearest centers...",spinner:"crescent"});yield t.present(),yield this.routeToTwoNearestCenters(),yield t.dismiss(),yield(yield this.toastCtrl.create({message:"\u{1F9ED} Routes calculated to 2 nearest earthquake evacuation centers",duration:4e3,color:"warning",position:"top"})).present()}catch(t){console.error("Error routing to nearest centers:",t),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}downloadMap(){return p(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("earthquake-map",this.map,"Earthquake",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){return p(this,null,function*(){if(console.log("\u{1F9ED} Starting real-time navigation to:",t.name),!this.selectedTransportMode){console.error("\u274C No transport mode selected");return}yield this.routeToCenter(t,this.selectedTransportMode),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.closeNavigationPanel(),this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name} via ${this.selectedTransportMode}`,duration:3e3,color:"primary"}).then(n=>n.present()),console.log("\u2705 Real-time navigation setup complete")})}onNavigationRouteUpdated(t){console.log("\u{1F504} Navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=d.geoJSON(n,{style:{color:"#007bff",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275cmp=L({type:r,selectors:[["app-earthquake-map"]],standalone:!0,features:[S],decls:36,vars:14,consts:[[3,"translucent"],["color","warning"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","earthquake-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForEarthquake.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForEarthquake.png","alt","Download",1,"control-icon"],["src","assets/compassForEarthquake.png","alt","Route to Nearest",1,"control-icon"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["fill","clear","size","small",3,"click"],["name","close-outline"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],["class","footer-content",4,"ngIf"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["name","walk-outline"],["class","route-info",4,"ngIf"],["name","bicycle-outline"],["name","car-outline"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"route-info"],[1,"time"],[1,"start-navigation-btn",3,"click"],["name","navigate"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],[1,"route-info-footer"],["class","time",4,"ngIf"],[1,"footer-actions"],["fill","solid","color","warning","size","small",3,"click"],["name","navigate","slot","start"]],template:function(n,e){n&1&&(a(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),C("click",function(){return e.goBack()}),_(4,"ion-icon",4),i()()()(),a(5,"ion-content",5),_(6,"div",6),a(7,"div",7)(8,"ion-button",8),C("click",function(){return e.showAllCenters()}),_(9,"img",9),i(),a(10,"ion-button",8),C("click",function(){return e.downloadMap()}),_(11,"img",10),i(),a(12,"ion-button",8),C("click",function(){return e.routeToNearestCenters()}),_(13,"img",11),i()(),P(14,st,1,3,"app-real-time-navigation",12),a(15,"div",13)(16,"div",14)(17,"div",15)(18,"div",16)(19,"h3"),u(20,"\u{1F7E0} All Earthquake Centers"),i(),a(21,"p"),u(22),i()(),a(23,"ion-button",17),C("click",function(){return e.closeAllCentersPanel()}),_(24,"ion-icon",18),i()(),a(25,"div",19),P(26,lt,12,4,"div",20),i()()(),a(27,"div",21)(28,"div",14)(29,"div",15),P(30,gt,5,2,"div",22),a(31,"ion-button",17),C("click",function(){return e.closeNavigationPanel()}),_(32,"ion-icon",18),i()(),P(33,mt,22,10,"div",23),i()(),a(34,"div",24),P(35,Ct,14,4,"div",25),i()()),n&2&&(f("translucent",!0),s(5),f("fullscreen",!0),s(9),f("ngIf",e.navigationDestination),s(),y("show",e.showAllCentersPanel),s(7),T("",e.evacuationCenters.length," centers available"),s(4),f("ngForOf",e.evacuationCenters),s(),y("show",e.selectedCenter),s(3),f("ngIf",e.selectedCenter),s(3),f("ngIf",e.selectedCenter),s(),y("show",e.selectedCenter&&e.selectedTransportMode),s(),f("ngIf",e.selectedCenter&&e.selectedTransportMode))},dependencies:[X,H,Q,V,G,B,W,$,q,D,K,at],styles:["#earthquake-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #ff9500;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,149,0,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(255, 149, 0, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-warning);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #ff9500;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-warning-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-warning)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--ion-color-warning)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;height:36px;font-weight:600}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:350px;height:100vh;background:#fff;z-index:2100;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-warning-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-warning);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 8px #0000000d}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-warning-tint);background:var(--ion-color-warning-tint);transform:translateY(-2px);box-shadow:0 4px 12px #ff950026}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:var(--ion-color-warning)}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-warning)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-warning-tint);background:var(--ion-color-warning-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-warning);background:var(--ion-color-warning-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-warning)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-warning)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-warning);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-warning-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-warning);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}"]})}}return r})();export{qt as EarthquakeMapPage};
