<?php
namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\DeviceToken;
use App\Models\Barangay;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class NotificationController extends Controller
{
    protected $fcmService;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }
    // List all notifications
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedBarangay = $request->input('barangay');
        $searchQuery = $request->input('search');

        // Get list of all barangays for super_admin filter
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }

        // Base query
        $query = Notification::query();

        // Apply role-based filtering
        if ($user->hasRole('super_admin')) {
            // Super admin can see all notifications
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            }
        } else {
            // Barangay users can only see their barangay's notifications
            $query->where('barangay', $user->barangay);
        }

        // Apply search filter if provided
        if ($searchQuery) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('title', 'like', "%{$searchQuery}%")
                  ->orWhere('message', 'like', "%{$searchQuery}%")
                  ->orWhere('barangay', 'like', "%{$searchQuery}%");
            });
        }

        $notifications = $query->orderBy('created_at', 'desc')->paginate(10);
        
        // Group by month and category for the filtered notifications
        // Apply barangay filter if not admin/super_admin or if a barangay is selected by admin
        $monthlyCategoryCountsQuery = Notification::selectRaw('
            DATE_FORMAT(created_at, "%Y-%m") as month,
            category,
            COUNT(*) as count
        ')
        ->whereNotIn('category', ['Fire'])
        ->groupBy('month', 'category')
        ->orderBy('month', 'asc');

        if (!$user->hasRole('admin') && !$user->hasRole('super_admin')) {
             $monthlyCategoryCountsQuery->where('barangay', $user->barangay);
        } else if ($selectedBarangay) {
             $monthlyCategoryCountsQuery->where('barangay', $selectedBarangay);
        }

        $monthlyCategoryCounts = $monthlyCategoryCountsQuery->get();

        // Get recent alerts for the sidebar
        // Apply barangay filter if not admin/super_admin or if a barangay is selected by admin
        $recentAlertsQuery = Notification::orderBy('created_at', 'desc')
            ->take(3);

         if (!$user->hasRole('admin') && !$user->hasRole('super_admin')) {
             $recentAlertsQuery->where('barangay', $user->barangay);
         } else if ($selectedBarangay) { // Apply filter if selected by admin
              $recentAlertsQuery->where('barangay', $selectedBarangay);
         }

        $recentAlerts = $recentAlertsQuery->get()
            ->map(function ($notification) {
                return (object) [
                    'type' => $notification->category,
                    'message' => $notification->title,
                    'location' => $notification->barangay,
                    'time_ago' => $notification->created_at->diffForHumans(),
                ];
            });

        // Pass $selectedBarangay to the view
        return view('components.notification.index', compact('notifications', 'monthlyCategoryCounts', 'recentAlerts', 'barangays', 'selectedBarangay', 'searchQuery'));
    }

    public function view($id)
    {
        $notification = Notification::find($id);
        return view('components.notification.create', compact('notification'));
    }

    // Show the form to create a new notification
    public function create()
    {
        $user = Auth::user();
        
        // Get list of all barangays for super_admin
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }
        
        return view('components.notification.create', compact('barangays'));
    }

    // Store a new notification
    public function store(Request $request)
    {
        $user = Auth::user();

        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $request->merge(['barangay' => $user->barangay ?? 'All Areas']);
        }

        // Validate form
        $request->validate([
            'title' => 'required|string|max:255',
            'category' => 'required|string',
            'message' => 'required|string',
            'severity' => 'required|string|in:low,medium,high',
            'barangay' => 'nullable|string', // Allow nullable but we'll provide default
            'send_push_notification' => 'required|in:1', // Always send push notifications
            'target_devices' => 'required|string|in:all', // Always send to all devices
        ]);

        // Ensure barangay has a value - use 'All Areas' as default for super_admin
        $barangay = $request->barangay ?? 'All Areas';

        // Create notification in database
        $notification = Notification::create([
            'title' => $request->title,
            'category' => $request->category,
            'message' => $request->message,
            'severity' => $request->severity,
            'sent' => false,
            'barangay' => $barangay,
            'user_id' => $user->id // Save the user who created it
        ]);

        // Always send push notifications to all users
        $targetDevices = 'all'; // Always send to all devices

        try {
            // Get device tokens based on target devices only (send to ALL users regardless of barangay)
            $tokenQuery = DeviceToken::where('is_active', true);

            // Filter by device type if not 'all'
            if ($targetDevices !== 'all') {
                $tokenQuery->where('device_type', $targetDevices);
            }

            $tokens = $tokenQuery->pluck('token')->toArray();

            if (empty($tokens)) {
                return redirect()->route('components.notification.index')
                    ->with('warning', "Notification created but no active device tokens found. Please ensure mobile app users are connected.");
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return redirect()->route('components.notification.index')
                    ->with('warning', 'Notification created but ' . $result['message']);
            }

            // Mark notification as sent
            $notification->update(['sent' => true]);

            return redirect()->route('components.notification.index')
                ->with('success', "Notification created and sent to {$result['success_count']} users" .
                    ($result['failure_count'] > 0 ? " ({$result['failure_count']} failures)" : ""));
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
                'admin_barangay' => $user->barangay,
                'target_devices' => $targetDevices,
                'notification_id' => $notification->id
            ]);

            return redirect()->route('components.notification.index')
                ->with('error', 'Notification created but failed to send: ' . $e->getMessage());
        }
    }
}
