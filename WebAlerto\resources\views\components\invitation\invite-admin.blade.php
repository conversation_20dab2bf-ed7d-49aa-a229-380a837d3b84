@extends('layout.app')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 to-sky-100 py-8">
    <div class="bg-white/90 rounded-2xl shadow-xl border border-sky-200 p-8 w-full max-w-lg">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
            <i class="fas fa-envelope-open-text text-emerald-600"></i>
            Send Admin Invitation
        </h2>
        <p class="text-gray-600 mb-6">Invite a new admin by email. The invited user will receive a secure link to set up their account.</p>
        <form id="adminInviteForm" class="space-y-4">
            @csrf
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                <input type="email" name="email" id="email" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
            </div>
            <div>
                <label for="position" class="block text-sm font-medium text-gray-700 mb-1">Position *</label>
                <input type="text" name="position" id="position" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
            </div>
            <div>
                <label for="barangay" class="block text-sm font-medium text-gray-700 mb-1">Barangay Assignment *</label>
                <select name="barangay" id="barangay" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
                    <option value="">Select Barangay</option>
                    <option value="City Hall">City Hall</option>
                    <option value="Lahug">Lahug</option>
                    <option value="Mabolo">Mabolo</option>
                    <option value="Luz">Luz</option>
                </select>
            </div>
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role *</label>
                <select name="role" id="role" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
                    <option value="">Select Role</option>
                    <option value="admin">City DRRMO Director (City-wide)</option>
                    <option value="chairman">BDRRMO Chairman (Barangay)</option>
                    <option value="officer">BDRRMO Officer (Barangay)</option>
                    <option value="assistant">BDRRMO Assistant (Barangay)</option>
                </select>
            </div>
            <div class="flex justify-end gap-3 pt-4">
                <button type="submit" class="px-6 py-2 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-paper-plane mr-2"></i>Send Invitation
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const inviteForm = document.getElementById('adminInviteForm');
    inviteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
        submitBtn.disabled = true;
        fetch('/admin-invitations/send-invite', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Invitation Sent!',
                    text: data.message,
                    confirmButtonColor: '#10b981'
                });
                inviteForm.reset();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Failed to Send',
                    text: data.message || 'An error occurred while sending the invitation.',
                    confirmButtonColor: '#ef4444'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Failed to Send',
                text: 'An unexpected error occurred. Please try again.',
                confirmButtonColor: '#ef4444'
            });
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
@endsection 