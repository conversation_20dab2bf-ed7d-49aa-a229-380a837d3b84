import{B as g,C as l,Fb as v,G as d,K as o,L as a,M as r,Y as c,da as u,ea as p,hc as O,jb as f,m as s,qa as m,vb as b,za as h}from"./chunk-N4Y2QYSK.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-2R6CW7ES.js";var w=()=>["OK"],x=(()=>{class i{constructor(e){this.router=e,this.isOnline=!1}ngOnInit(){console.log("LoadingPage ngOnInit");let e=window;e.appDebug&&e.appDebug("LoadingPage ngOnInit"),this.checkInternetConnection()}checkInternetConnection(){this.isOnline=navigator.onLine,console.log("LoadingPage checkInternetConnection, isOnline:",this.isOnline);let e=window;e.appDebug&&e.appDebug("LoadingPage checkInternetConnection, isOnline: "+this.isOnline);let n=localStorage.getItem("token"),t=localStorage.getItem("onboardingComplete");console.log("\u{1F50D} Loading page - Auth status check:",{hasToken:!!n,onboardingComplete:t==="true",isOnline:this.isOnline}),setTimeout(()=>{n&&t==="true"?(console.log("\u2705 User authenticated & onboarded \u2192 navigating to tabs/home"),e.appDebug&&e.appDebug("LoadingPage navigating to tabs/home (authenticated & onboarded)"),this.router.navigate(["/tabs/home"])):n&&t!=="true"?(console.log("\u2705 User authenticated but not onboarded \u2192 navigating to welcome"),e.appDebug&&e.appDebug("LoadingPage navigating to welcome (authenticated but not onboarded)"),this.router.navigate(["/welcome"])):!n&&t==="true"?(console.log("\u{1F511} User not authenticated but has onboarded before \u2192 navigating to login"),e.appDebug&&e.appDebug("LoadingPage navigating to login (not authenticated but onboarded)"),this.router.navigate(["/login"])):(console.log("\u{1F44B} New user \u2192 navigating to intro"),e.appDebug&&e.appDebug("LoadingPage navigating to intro (new user)"),this.router.navigate(["/intro"]))},1e3),setTimeout(()=>{console.log("\u26A0\uFE0F Loading page safety timeout triggered - checking if still on loading page"),this.router.url==="/loading"&&(console.log("\u{1F6A8} Still on loading page after 5 seconds - forcing navigation"),n?(console.log("\u{1F504} Forcing navigation to welcome page"),this.router.navigate(["/welcome"])):(console.log("\u{1F504} Forcing navigation to intro page"),this.router.navigate(["/intro"])))},5e3)}ionViewWillEnter(){window.addEventListener("online",this.updateOnlineStatus.bind(this)),window.addEventListener("offline",this.updateOnlineStatus.bind(this))}ionViewWillLeave(){window.removeEventListener("online",this.updateOnlineStatus.bind(this)),window.removeEventListener("offline",this.updateOnlineStatus.bind(this))}updateOnlineStatus(){this.isOnline=navigator.onLine;let e=window;e.appDebug&&e.appDebug("LoadingPage updateOnlineStatus, isOnline: "+this.isOnline),console.log("\u{1F310} Online status changed:",this.isOnline)}static{this.\u0275fac=function(n){return new(n||i)(l(h))}}static{this.\u0275cmp=s({type:i,selectors:[["app-loading"]],standalone:!0,features:[u],decls:6,vars:3,consts:[[1,"ion-padding"],[1,"loading-container"],[1,"loader"],["header","No Internet Connection","message","Please check your internet connection and try again.",3,"isOpen","buttons"]],template:function(n,t){n&1&&(o(0,"ion-content",0)(1,"div",1),r(2,"div",2),o(3,"h2"),c(4,"Loading..."),a(),r(5,"ion-alert",3),a()()),n&2&&(g(5),d("isOpen",!t.isOnline)("buttons",p(2,w)))},dependencies:[O,b,v,m,f],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100%;text-align:center}.loader[_ngcontent-%COMP%]{width:48px;height:48px;border:5px solid #FFF;border-bottom-color:#3880ff;border-radius:50%;display:inline-block;box-sizing:border-box;animation:_ngcontent-%COMP%_rotation 1s linear infinite;margin-bottom:20px}@keyframes _ngcontent-%COMP%_rotation{0%{transform:rotate(0)}to{transform:rotate(360deg)}}h2[_ngcontent-%COMP%]{color:#3880ff;font-size:24px;margin:0}"]})}}return i})();export{x as LoadingPage};
