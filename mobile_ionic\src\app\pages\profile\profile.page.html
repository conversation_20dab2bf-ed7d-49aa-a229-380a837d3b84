<ion-header [translucent]="true">
  <ion-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="profile-header">
    <div class="profile-background">
      <div style="display: flex; align-items: center; justify-content: center; height: 120px; width: 100%;">
      <img src="assets/ALERTO.png" style="width: 200px; height: 200px; object-fit: contain;" />
    </div>
    </div>
  </div>

  <ion-list lines="full" class="menu-list">
    <ion-item button (click)="openGuideModal()" style="padding-top: 10px;">
      <img src="assets/info.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Reference Guide for Map Symbols</ion-label>
    </ion-item>

    <ion-item button (click)="openUserGuideModal()" style="padding-top: 10px;">
      <img src="assets/system-icon.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">User Guide</ion-label>
    </ion-item>

    <ion-item button (click)="openNotificationHistoryModal()" style="padding-top: 10px;">
      <ion-icon name="notifications-outline" style="width:28px; height:28px; display:block; margin:auto;" slot="start"></ion-icon>
      <ion-label style="padding-left: 15px; font-size: 17px;">Push Notification History</ion-label>
    </ion-item>

    <ion-item button (click)="openEmergencyContactsModal()" style="padding-top: 10px;">
      <img src="assets/medical-call.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Emergency Contacts</ion-label>
    </ion-item>

    <ion-item button (click)="openSafetyTipsModal()" style="padding-top: 10px;">
      <img src="assets/first-aid-box.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Safety Tips</ion-label>
    </ion-item>

    <ion-item button (click)="openPrivacyModal()" style="padding-top: 10px;">
      <img src="assets/shield.png" style="width:28px; height:28px; display:block; margin:auto;" slot="start" />
      <ion-label style="padding-left: 15px;   font-size: 17px;">Privacy Policy</ion-label>
    </ion-item>

    <ion-item button (click)="openTermsModal()" style="padding-top: 10px;">
      <img src="assets/terms-and-conditions.png" style="width:28px; height:28px; display:block; margin:auto; " slot="start" />
      <ion-label style="padding-left: 15px; font-size: 17px;">Terms and Condition</ion-label>
    </ion-item>

    <ion-item button (click)="logout()" style="padding-top: 10px; --color: #d32f2f;">
      <ion-icon name="log-out-outline" style="font-size: 28px; color: #d32f2f;" slot="start"></ion-icon>
      <ion-label style="padding-left: 15px; font-size: 17px; color: #d32f2f;">Log Out</ion-label>
    </ion-item>

  </ion-list>
</ion-content>