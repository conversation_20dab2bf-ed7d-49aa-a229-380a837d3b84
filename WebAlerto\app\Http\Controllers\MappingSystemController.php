<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evacuation; // Changed from EvacuationCenter to Evacuation
use App\Models\Barangay;
use Illuminate\Support\Facades\Auth;

class MappingSystemController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get all barangays for the filter dropdown
        $barangays = Barangay::pluck('name')->unique();
        
        $query = Evacuation::query();

        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }

        // Apply filters from the request
        $searchQuery = $request->input('search');
        $statusFilter = $request->input('status', 'All');
        $disasterFilter = $request->input('disaster_type', 'All');
        $selectedBarangay = $request->input('barangay', 'All');

        if ($searchQuery) {
            $query->where('name', 'like', '%' . $searchQuery . '%');
        }

        if ($statusFilter !== 'All') {
            $query->where('status', $statusFilter);
        }

        if ($disasterFilter !== 'All') {
            if ($disasterFilter === 'Multi-disaster') {
                // Find centers with more than one disaster type
                $query->whereRaw('JSON_LENGTH(disaster_type) > 1');
            } else {
                // Find centers with exactly one disaster type that matches the filter
                $query->whereRaw('JSON_LENGTH(disaster_type) = 1')
                      ->whereJsonContains('disaster_type', $disasterFilter);
            }
        }
        
        if ($selectedBarangay !== 'All' && $user->hasRole('super_admin')) {
            $query->where('barangay', $selectedBarangay);
        }

        $centers = $query->get()->map(function($center) {
            // Create a formatted address from the separate location fields
            $addressParts = [
                $center->building_name,
                $center->street_name,
                $center->barangay,
                $center->city,
                $center->province,
            ];
            
            // Clean up parts and remove duplicates/empties
            $addressParts = array_map('trim', $addressParts);
            $addressParts = array_filter($addressParts);
            $addressParts = array_unique($addressParts);
            
            $address = implode(', ', $addressParts);
            
            // Ensure "Philippines" is at the end, but only once
            $address = trim(str_ireplace('Philippines', '', $address), ' ,');
            $address .= ', Philippines';
            
            // Add the formatted address to the center data
            $center->address = $address;
            
            return $center;
        });

        return view('components.map', compact('centers', 'barangays', 'searchQuery', 'statusFilter', 'disasterFilter', 'selectedBarangay'));
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        
        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $request->merge(['barangay' => $user->barangay]);
        }

        // Validate the incoming request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'street_name' => 'required|string|max:255',
            'province' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'barangay' => 'required|string|max:255',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'capacity' => 'required|integer|min:1',
            'contact' => 'required|string|max:255',
            'disaster_type' => 'required|string|max:255',
            'status' => 'required|string|in:Active,Inactive,Under Maintenance',
            'image_url' => 'nullable|string|max:255'
        ]);

        // Handle disaster type - convert to array format to match evacuation management
        if (strpos($validatedData['disaster_type'], 'Others:') === 0) {
            // Already formatted as "Others: Custom Disaster"
            $validatedData['disaster_type'] = [$validatedData['disaster_type']];
        } else {
            // Convert single disaster type to array format
            $validatedData['disaster_type'] = [$validatedData['disaster_type']];
        }

        // Create new evacuation center
        $center = Evacuation::create($validatedData);

        // Return response with success message and center data
        return response()->json([
            'success' => true,
            'message' => 'Evacuation center added successfully',
            'center' => $center
        ], 201);
    }

    public function storeBatch()
    {
        $user = Auth::user();
        $centers = [
            [
                'name' => 'Mabolo Elementary School',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3200,
                'longitude' => 123.9000,
                'capacity' => 500,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo National High School',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3180,
                'longitude' => 123.9020,
                'capacity' => 800,
                'status' => 'Active',
                'image_url' => 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjB-jSVIm2eBIw0tjMMVnuHYl-Bm2QSeLHCdZWjRVAoNMPgqP0rc3Ht_6yO66XrT0c0yPsVzjwox612YiOQR00IzyljV6UVmCVpHnSmnbPVCvH8dC_RbmpmDAmxMlpMOwK3H4Y5npMGPspu9e4JnSPZx6iNUIxfCuBJSO4SkqLHDpgkBHyb1TdZPcmunpE/w1200-h630-p-k-no-nu/urot%20(1).jpg'
            ],
            [
                'name' => 'Mabolo Barangay Hall',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3220,
                'longitude' => 123.8980,
                'capacity' => 300,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Sports Complex',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3210,
                'longitude' => 123.9010,
                'capacity' => 1000,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Open Field',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3190,
                'longitude' => 123.8990,
                'capacity' => 2000,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ],
            [
                'name' => 'Mabolo Community Center',
                'address' => 'Mabolo, Cebu City',
                'latitude' => 10.3230,
                'longitude' => 123.9030,
                'capacity' => 600,
                'status' => 'Active',
                'image_url' => 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
            ]
        ];

        foreach ($centers as $centerData) {
            // Add barangay to the data
            $centerData['barangay'] = $user->barangay;
            
            // Check if center already exists
            $exists = Evacuation::where('name', $centerData['name'])
                ->where('barangay', $user->barangay)
                ->exists();
            
            if (!$exists) {
                Evacuation::create($centerData);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Evacuation centers added successfully'
        ]);
    }

    public function destroy($id)
    {
        try {
            $user = Auth::user();
            $query = Evacuation::query();

            // Super admin can delete any center
            if ($user->hasRole('super_admin')) {
                $center = $query->findOrFail($id);
            }
            // Admin can only delete centers in their barangay
            else if ($user->hasRole('admin')) {
                $center = $query->where('barangay', $user->barangay)
                    ->findOrFail($id);
            }
            else {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized action'
                ], 403);
            }

            $center->delete();

            return response()->json([
                'success' => true,
                'message' => 'Evacuation center deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting evacuation center'
            ], 500);
        }
    }

    public function search(Request $request)
    {
        $user = Auth::user();
        $query = Evacuation::query();

        // Super admin can search all centers
        if ($user->hasRole('super_admin')) {
            $centers = $query->where('name', 'LIKE', '%' . $request->search . '%')
                ->orWhere('barangay', 'LIKE', '%' . $request->search . '%')
                ->get();
        }
        // Admin can search centers in their barangay
        else if ($user->hasRole('admin')) {
            $centers = $query->where('barangay', $user->barangay)
                ->where(function($q) use ($request) {
                    $q->where('name', 'LIKE', '%' . $request->search . '%')
                        ->orWhere('barangay', 'LIKE', '%' . $request->search . '%');
                })
                ->get();
        }
        // Regular users can only search active centers in their barangay
        else {
            $centers = $query->where('barangay', $user->barangay)
                ->where('status', 'Active')
                ->where(function($q) use ($request) {
                    $q->where('name', 'LIKE', '%' . $request->search . '%')
                        ->orWhere('barangay', 'LIKE', '%' . $request->search . '%');
                })
                ->get();
        }
        
        if ($centers->isNotEmpty()) {
            return response()->json([
                'success' => true,
                'centers' => $centers
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'No centers found'
        ]);
    }
}
