<?php
namespace App\Http\Controllers;
use App\Models\Evacuation;
use App\Models\AppNotification;
use App\Models\User;
use App\Models\Barangay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EvacuationManagementController extends Controller
{
    public function showDashboard(Request $request)
    {
        $user = Auth::user();
        $selectedBarangay = $request->input('barangay');
        $search = $request->input('search');
        $statusFilter = $request->input('status');
        
        // Get list of all barangays for super_admin filter
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }
        
        // Base query for evacuation centers
        $query = Evacuation::query();

        // Apply role-based filtering
        if ($user->hasRole('super_admin')) {
            // Super admin can see all evacuation centers
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            }
        } else {
            // Barangay users can only see their barangay's evacuation centers
            $query->where('barangay', $user->barangay);
        }
        
        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('street_name', 'like', "%{$search}%")
                  ->orWhere('barangay', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($statusFilter) {
            $query->where('status', $statusFilter);
        }
        
        // Order by created_at in descending order (latest first)
        $query->orderBy('created_at', 'desc');
        
        $centers = $query->paginate(10);
            
        // Get total counts for active and inactive centers
        // Important: Clone the query BEFORE pagination to get accurate counts
        $baseQuery = clone $query;
        $activeCenters = (clone $baseQuery)->where('status', 'Active')->count();
        
        // Create a new clone for inactive count
        $inactiveCenters = (clone $baseQuery)->where('status', 'Inactive')->count();
        
        // Count for 'Under Maintenance'
        $maintenanceCenters = (clone $baseQuery)->where('status', 'Under Maintenance')->count();
        
        return view('components.evacuation_management.evacuation-dashboard', 
            compact('centers', 'activeCenters', 'inactiveCenters', 'maintenanceCenters', 'barangays', 'selectedBarangay', 'search', 'statusFilter'));
    }

    public function showAddForm()
    {
        $user = Auth::user();
        
        // Get list of all barangays for super_admin
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }
        
        return view('components.evacuation_management.add-evacuation-center', compact('barangays'));
    }

    public function showEditForm($id)
    {
        $user = Auth::user();
        
        // Super admin and Admin can edit any center
        if ($user->hasRole('super_admin') || $user->hasRole('admin')) {
            $evacuationCenter = Evacuation::findOrFail($id);
        } 
        // Regular users can only edit centers in their barangay
        else {
            $evacuationCenter = Evacuation::where('barangay', $user->barangay)
                ->findOrFail($id);
        }
        
        return view('components.evacuation_management.edit-evacuation-center', compact('evacuationCenter'));
    }

    public function store(Request $request)
    {
        try {
            $user = Auth::user();

            // If user is not super_admin, force the barangay in the request before validation
            if (!$user->hasRole('super_admin')) {
                $request->merge(['barangay' => $user->barangay]);
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'building_name' => 'nullable|string|max:255',
                'street_name' => 'nullable|string|max:255',
                'province' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:20',
                'barangay' => 'required|string|max:255|exists:barangays,name',
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'capacity' => 'required|integer',
                'contact' => 'required|string|max:255',
                'disaster_type' => 'required|array',
                'disaster_type.*' => 'required|string',
                'status' => 'required|in:Active,Inactive,Under Maintenance',
                'other_disaster_type' => 'nullable|string|max:255',
            ]);

            // Handle 'Others' disaster type
            if (in_array('Others', $validated['disaster_type']) && !empty($validated['other_disaster_type'])) {
                // Find the 'Others' value and replace it with the custom text
                $otherKey = array_search('Others', $validated['disaster_type']);
                if ($otherKey !== false) {
                    $validated['disaster_type'][$otherKey] = 'Others: ' . $validated['other_disaster_type'];
                }
            }
            unset($validated['other_disaster_type']);

            // Ensure coordinates are saved with high precision
            $validated['latitude'] = number_format((float)$validated['latitude'], 8, '.', '');
            $validated['longitude'] = number_format((float)$validated['longitude'], 8, '.', '');
            
            $center = Evacuation::create($validated);

            // Send notification to all users about new evacuation center
            $this->sendEvacuationCenterNotification($center);

            // Return JSON for AJAX requests, redirect for regular form submissions
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center added successfully',
                    'redirect' => route('components.evacuation_management.evacuation-dashboard')
                ]);
            }

            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center added successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error adding evacuation center: ' . $e->getMessage() . ' Errors: ' . json_encode($e->errors()));
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error adding evacuation center: ' . $e->getMessage());
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error adding evacuation center: ' . $e->getMessage()
                ], 500);
            }
            return back()->withInput()->withErrors(['error' => 'Error adding evacuation center: ' . $e->getMessage()]);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $user = Auth::user();
            
            // Super admin can update any center
            if ($user->hasRole('super_admin')) {
                $center = Evacuation::findOrFail($id);
            } 
            // Regular users can only update centers in their barangay
            else {
                $center = Evacuation::where('barangay', $user->barangay)
                    ->findOrFail($id);
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'building_name' => 'nullable|string|max:255',
                'street_name' => 'nullable|string|max:255',
                'province' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:20',
                'barangay' => 'required|string|max:255|exists:barangays,name',
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'capacity' => 'required|integer',
                'contact' => 'required|string|max:255',
                'disaster_type' => 'required|array',
                'disaster_type.*' => 'required|string',
                'status' => 'required|in:Active,Inactive,Under Maintenance',
                'other_disaster_type' => 'nullable|string|max:255',
            ]);

            // Handle 'Others' disaster type
            if (in_array('Others', $validated['disaster_type']) && !empty($validated['other_disaster_type'])) {
                $otherKey = array_search('Others', $validated['disaster_type']);
                if ($otherKey !== false) {
                    $validated['disaster_type'][$otherKey] = 'Others: ' . $validated['other_disaster_type'];
                }
            }
            unset($validated['other_disaster_type']);
            
            // If user is not super_admin or admin, force their barangay
            if (!$user->hasRole('super_admin') && !$user->hasRole('admin')) {
                $validated['barangay'] = $user->barangay;
            }
            
            // Ensure coordinates are saved with high precision
            $validated['latitude'] = number_format((float)$validated['latitude'], 8, '.', '');
            $validated['longitude'] = number_format((float)$validated['longitude'], 8, '.', '');

            $center->update($validated);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center updated successfully',
                    'redirect' => route('components.evacuation_management.evacuation-dashboard')
                ]);
            }

            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center updated successfully');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error updating evacuation center: ' . $e->getMessage() . ' Errors: ' . json_encode($e->errors()));
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error updating evacuation center: ' . $e->getMessage());
            
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating evacuation center: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => 'An error occurred while updating the evacuation center.']);
        }
    }

    public function destroy($id)
    {
        try {
            $user = Auth::user();
            
            // Super admin can delete any center
            if ($user->hasRole('super_admin')) {
                $center = Evacuation::findOrFail($id);
            } 
            // Regular users can only delete centers in their barangay
            else {
                $center = Evacuation::where('barangay', $user->barangay)
                    ->findOrFail($id);
            }
            
            // Log the delete attempt
            \Log::info("Delete attempt for evacuation center {$id} by user {$user->email} (role: {$user->role})");
            
            $center->delete();
            
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center deleted successfully'
                ]);
            }
            
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center deleted successfully');
        } catch (\Exception $e) {
            \Log::error("Error deleting evacuation center {$id}: " . $e->getMessage());
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('error', 'Error deleting evacuation center: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        $user = Auth::user();
        $query = Evacuation::query();
        
        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }
        
        $center = $query->findOrFail($id);
        return view('components.evacuation_management.view-map', compact('center'));
    }

    public function centersList(Request $request, $type = 'all')
    {
        $user = Auth::user();
        $query = Evacuation::query();
        $selectedBarangay = $request->input('barangay');
        $search = $request->input('search');
        
        // Set title based on type
        $title = ucfirst($type) . ' Evacuation Centers';
        
        // Apply type filter
        if ($type === 'active') {
            $query->where('status', 'Active');
        } else if ($type === 'inactive') {
            $query->where('status', 'Inactive');
        } else if ($type === 'maintenance') {
            $query->where('status', 'Under Maintenance');
        }
        
        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('street_name', 'like', "%{$search}%")
                  ->orWhere('barangay', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }
        
        // Apply barangay filter if provided
        if ($selectedBarangay) {
            $query->where('barangay', $selectedBarangay);
        }
        // If no barangay filter and user is not super_admin, show only user's barangay
        else if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }
        
        // Order by created_at in descending order (latest first)
        $query->orderBy('created_at', 'desc');
        
        $centers = $query->paginate(9);
        
        // Get total counts for active and inactive centers
        // Important: Clone the query BEFORE pagination to get accurate counts
        // But AFTER applying barangay and search filters
        $baseQuery = clone $query;
        $activeCount = (clone $baseQuery)->where('status', 'Active')->count();
        
        // Create a new clone for inactive count
        $inactiveCount = (clone $baseQuery)->where('status', 'Inactive')->count();
        
        // Count for 'Under Maintenance'
        $maintenanceCount = (clone $baseQuery)->where('status', 'Under Maintenance')->count();
        
        // Define specific barangays for super_admin filter
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }
        
        return view('components.evacuation_management.centers-list', 
            compact('centers', 'activeCount', 'inactiveCount', 'maintenanceCount', 'type', 'barangays', 'selectedBarangay', 'title'));
    }

    public function getCenterDetails($id)
    {
        $user = Auth::user();
        $query = Evacuation::query();

        // If user is not admin, restrict to their barangay
        if (!$user->hasRole('admin') && !$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }

        $center = $query->findOrFail($id);
        return response()->json($center);
    }

    /**
     * API endpoint to get evacuation centers for mobile app
     */
    public function apiList(Request $request)
    {
        try {
            $query = Evacuation::query();

            // Filter by disaster type if provided - handle JSON array format
            if ($request->has('disaster_type') && $request->disaster_type) {
                $query->whereJsonContains('disaster_type', $request->disaster_type);
            }

            // Filter by barangay if provided
            if ($request->has('barangay') && $request->barangay) {
                $query->where('barangay', $request->barangay);
            }

            // Only return active centers for mobile app
            $query->where('status', 'Active');

            $centers = $query->select([
                'id',
                'name',
                'street_name',
                'barangay',
                'city',
                'province',
                'latitude',
                'longitude',
                'capacity',
                'status',
                'disaster_type',
                'contact'
            ])->get();

            // Format the response for mobile app
            $formattedCenters = $centers->map(function($center) {
                return [
                    'id' => $center->id,
                    'name' => $center->name,
                    'address' => "{$center->street_name}, {$center->barangay}, {$center->city}, {$center->province}",
                    'latitude' => (float) $center->latitude,
                    'longitude' => (float) $center->longitude,
                    'capacity' => (int) $center->capacity,
                    'status' => $center->status,
                    'disaster_type' => $center->disaster_type,
                    'contact' => $center->contact,
                    'barangay' => $center->barangay
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedCenters,
                'count' => $formattedCenters->count()
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch evacuation centers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send notification to users when a new evacuation center is added
     */
    private function sendEvacuationCenterNotification(Evacuation $center)
    {
        try {
            // Get disaster types as a readable string
            $disasterTypes = is_array($center->disaster_type)
                ? implode(', ', $center->disaster_type)
                : $center->disaster_type;

            // Create notification for all users
            $users = User::where('role', '!=', 'admin')->get(); // Don't notify admins

            foreach ($users as $user) {
                AppNotification::create([
                    'user_id' => $user->id,
                    'type' => 'evacuation_center_added',
                    'title' => 'New Evacuation Center Added',
                    'message' => "A new evacuation center '{$center->name}' has been added in {$center->barangay} for {$disasterTypes} emergencies.",
                    'data' => json_encode([
                        'center_id' => $center->id,
                        'center_name' => $center->name,
                        'barangay' => $center->barangay,
                        'disaster_types' => $center->disaster_type,
                        'latitude' => $center->latitude,
                        'longitude' => $center->longitude
                    ]),
                    'read' => false
                ]);
            }

            \Log::info("Sent evacuation center notification for center: {$center->name} to " . $users->count() . " users");

        } catch (\Exception $e) {
            \Log::error("Failed to send evacuation center notification: " . $e->getMessage());
        }
    }

    public function geocode(Request $request)
    {
        $address = $request->get('address');
        if (!$address) {
            return response()->json(['error' => 'Address parameter required'], 400);
        }

        // Search the entire Philippines for the address
        $params = [
            'q' => $address,
            'countrycodes' => 'ph', // Restrict search to the Philippines
            'format' => 'json',
            'limit' => 10,
            'addressdetails' => 1,
        ];

        $url = "https://nominatim.openstreetmap.org/search?" . http_build_query($params);

        try {
            // Use stream context to set a User-Agent, which is required by Nominatim
            $context = stream_context_create(['http' => ['header' => "User-Agent: WebAlerto Laravel App\r\n"]]);
            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);
            
            // Adapt Nominatim's response to a format similar to Mapbox's for frontend compatibility
            $features = array_map(function($item) {
                return [
                    'id' => 'poi.' . $item['place_id'],
                    'text' => $item['display_name'],
                    'place_name' => $item['display_name'],
                    'center' => [(float)$item['lon'], (float)$item['lat']],
                    'context' => [
                        ['id' => 'postcode.' . ($item['address']['postcode'] ?? ''), 'text' => $item['address']['postcode'] ?? ''],
                        ['id' => 'place.' . ($item['address']['city'] ?? ''), 'text' => $item['address']['city'] ?? ''],
                        ['id' => 'region.' . ($item['address']['state'] ?? ''), 'text' => $item['address']['state'] ?? ''],
                    ]
                ];
            }, $data);

            return response()->json(['features' => $features]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Geocoding failed: ' . $e->getMessage()], 500);
        }
    }

    public function reverseGeocode(Request $request)
    {
        $lat = $request->get('lat');
        $lng = $request->get('lng');

        if (!$lat || !$lng) {
            return response()->json(['error' => 'Latitude and longitude parameters required'], 400);
        }

        $params = [
            'lat' => $lat,
            'lon' => $lng,
            'format' => 'json',
            'addressdetails' => 1,
            'zoom' => 18,
        ];

        $url = "https://nominatim.openstreetmap.org/reverse?" . http_build_query($params);

        try {
            // Use stream context to set a User-Agent
            $context = stream_context_create(['http' => ['header' => "User-Agent: WebAlerto Laravel App\r\n"]]);
            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);
            
            if (!empty($data) && !isset($data['error'])) {
                $address = $data['address'];
                
                // Get POI name and street name
                $buildingName = $address['school'] ?? $address['hospital'] ?? $address['public_building'] ?? $address['shop'] ?? $address['office'] ?? $address['commercial'] ?? $address['amenity'] ?? $address['tourism'] ?? '';
                $street = $address['road'] ?? $address['path'] ?? '';

                // Fallback: If no specific POI name, try to get it from the full display name
                if (empty($buildingName)) {
                    $fullAddressParts = array_map('trim', explode(',', $data['display_name']));
                    $potentialName = $fullAddressParts[0] ?? '';
                    if (!empty($potentialName) && !is_numeric($potentialName) && strcasecmp($potentialName, $street) !== 0) {
                        $buildingName = $potentialName;
                    }
                }

                // *** FIX FOR REDUNDANCY ***
                // If the identified building name is the same as the street, clear the street to avoid duplication.
                if (!empty($buildingName) && strcasecmp($buildingName, $street) === 0) {
                    $street = '';
                }

                // Handle province mapping
                $province = $address['state'] ?? $address['region'] ?? $address['province'] ?? '';
                $city = $address['city'] ?? $address['town'] ?? $address['municipality'] ?? '';
                $postal_code = $address['postcode'] ?? '';
                $full_address = $data['display_name'] ?? '';

                // Clean up province name for consistency
                if ($province === 'Central Visayas') {
                    $province = 'Cebu';
                }
                
                $responseAddress = [
                    'building_name' => $buildingName,
                    'street' => $street,
                    'barangay' => $address['suburb'] ?? $address['village'] ?? $address['city_district'] ?? $address['neighbourhood'] ?? '',
                    'city' => $city,
                    'province' => $province,
                    'postal_code' => $postal_code,
                    'full_address' => $full_address
                ];

                // Handle Metro Manila case where city might be in a different field
                if ($responseAddress['province'] === 'Metro Manila' && empty($responseAddress['city'])) {
                    $responseAddress['city'] = $address['county'] ?? 'Metro Manila';
                }

                return response()->json($responseAddress);
            }

            return response()->json(['error' => 'No features found', 'details' => $data['error'] ?? ''], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Reverse geocoding failed: ' . $e->getMessage()], 500);
        }
    }
}