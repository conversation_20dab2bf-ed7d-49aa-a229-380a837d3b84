@extends('layout.app')

@section('title', 'Evacuation Centers Map')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Center Search & Filters Form -->
        <form method="GET" action="{{ route('map') }}" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-3 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">
                <!-- Status Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-filter text-sky-600 mr-1"></i>Status
                    </label>
                    <select id="status-filter" name="status" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" {{ ($statusFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Status</option>
                        <option value="Active" {{ ($statusFilter ?? '') === 'Active' ? 'selected' : '' }}>Active</option>
                        <option value="Inactive" {{ ($statusFilter ?? '') === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="Under Maintenance" {{ ($statusFilter ?? '') === 'Under Maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                    </select>
                </div>
                <!-- Disaster Type Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-exclamation-triangle text-sky-600 mr-1"></i>Disaster
                    </label>
                    <select name="disaster_type" id="disaster-filter" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" {{ ($disasterFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Disasters</option>
                        <option value="Typhoon" {{ ($disasterFilter ?? '') === 'Typhoon' ? 'selected' : '' }}>Typhoon</option>
                        <option value="Flood" {{ ($disasterFilter ?? '') === 'Flood' ? 'selected' : '' }}>Flood</option>
                        <option value="Fire" {{ ($disasterFilter ?? '') === 'Fire' ? 'selected' : '' }}>Fire</option>
                        <option value="Earthquake" {{ ($disasterFilter ?? '') === 'Earthquake' ? 'selected' : '' }}>Earthquake</option>
                        <option value="Landslide" {{ ($disasterFilter ?? '') === 'Landslide' ? 'selected' : '' }}>Landslide</option>
                        <option value="Others" {{ ($disasterFilter ?? '') === 'Others' ? 'selected' : '' }}>Others</option>
                        <option value="Multi-disaster" {{ ($disasterFilter ?? '') === 'Multi-disaster' ? 'selected' : '' }}>Multi-disaster</option>
                    </select>
                </div>
                <!-- Barangay Filter -->
                @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('admin'))
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-map text-sky-600 mr-1"></i>Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" {{ ($selectedBarangay ?? 'All') === 'All' ? 'selected' : '' }}>All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>{{ $barangay }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
                <!-- Filter Centers + Search Button (side by side, rightmost) -->
                <div class="col-span-1 md:col-span-2 flex gap-2">
                    <div class="flex-1">
                        <label class="block text-xs font-semibold text-gray-700 mb-1">
                            <i class="fas fa-search text-sky-600 mr-1"></i>Filter Centers
                        </label>
                        <div class="relative">
                        <input id="search" type="text" name="search" value="{{ $searchQuery ?? '' }}" placeholder="Search by name, address..." class="w-full pl-8 pr-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                           
                        </div>
                    </div>
                    <div class="self-end pb-1">
                        <button type="submit" class="px-3 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors text-sm h-full">
                            <i class="fas fa-search mr-1"></i>Search
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Map Section (bigger) -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 mb-8 map-section-container">
            <!-- Map Controls Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Evacuation Centers Map</h3>
                <button id="reset-map" class="inline-flex items-center px-3 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors text-sm shadow-md">
                    <i class="fas fa-undo-alt mr-2"></i>
                    Reset Map
                </button>
            </div>
            <!-- Legend Overlay -->
            <div class="absolute top-32 right-12 z-20 bg-white/90 rounded-xl shadow-lg border border-sky-200 p-4 flex flex-col items-center" style="min-width: 220px;">
                <div class="grid grid-cols-2 gap-x-6 gap-y-2">
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #22c55e;"></div><span class="text-xs text-gray-600">Typhoon</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #3b82f6;"></div><span class="text-xs text-gray-600">Flood</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #ef4444;"></div><span class="text-xs text-gray-600">Fire</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #f59e42;"></div><span class="text-xs text-gray-600">Earthquake</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #a16207;"></div><span class="text-xs text-gray-600">Landslide</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #9333ea;"></div><span class="text-xs text-gray-600">Others</span></div>
                    <div class="flex items-center gap-2 col-span-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #6b7280;"></div><span class="text-xs text-gray-600">Multi-disaster</span></div>
                </div>
            </div>
            <div id="map" class="w-full rounded-xl overflow-hidden shadow-md border-2 border-sky-200 bg-white relative" style="z-index: 1; height: 650px; min-height: 650px;"></div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-o9N1j+n1D2dLkUobZLCOoMOGMNF9OL8W8BO6FhJdguI=" crossorigin=""/>
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    body {
        font-family: 'Poppins', sans-serif;
    }
    #map {
        height: 500px !important;
        width: 100% !important;
        z-index: 1;
        position: relative;
        overflow: hidden;
        min-height: 500px;
        flex: 1;
    }
    .leaflet-container {
        position: relative !important;
        outline: none;
        overflow: hidden !important;
        background: white !important;
        width: 100% !important;
        height: 100% !important;
    }
    /* Map Section Container */
    .map-section-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }
    .leaflet-popup-content {
        margin: 0;
        padding: 1rem;
        font-family: 'Poppins', sans-serif;
    }
    .leaflet-popup-content-wrapper {
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e0f2fe;
        background: white;
    }
    .leaflet-popup-tip {
        background: white;
    }
    .leaflet-popup-content h3 {
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    .leaflet-popup-content p {
        color: #4b5563;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    .leaflet-popup-content .actions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e0f2fe;
    }
    .leaflet-popup-content .actions button:hover {
        background-color: #fee2e2;
        border-color: #fecaca;
    }
    .leaflet-popup-content .actions a:hover {
        background-color: #dbeafe;
        border-color: #bfdbfe;
    }

    /* Location search results styling */
    .search-result-item {
        transition: background-color 0.2s;
    }

    .search-result-item:hover {
        background-color: #f0f9ff;
    }

    .location-search-marker {
        z-index: 1000;
    }

    .location-search-marker .animate-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Search results container */
    #mapSearchResults {
        max-width: 100%;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    /* Search input focus styling */
    #mapLocationSearch:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: #3b82f6;
    }

    /* Reset button styling */
    #reset-map {
        transition: all 0.2s ease-in-out;
    }

    #reset-map:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
    }

    #reset-map:active {
        transform: translateY(0);
    }

    /* Map controls header styling */
    .map-section-container h3 {
        color: #1e293b;
        font-weight: 600;
    }

    /* Legend Overlay Styling */
    .map-section-container .legend-overlay {
        position: absolute;
        top: 2rem;
        right: 3rem;
        z-index: 20;
        background: rgba(255,255,255,0.95);
        border-radius: 1rem;
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        border: 1px solid #bae6fd;
        padding: 1.25rem 1.5rem;
        min-width: 220px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
</style>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>
<script src="{{ asset('js/map.js') }}"></script>
<script>
    // Initialize map with data from Laravel
    document.addEventListener('DOMContentLoaded', function() {
        const centers = @json($centers);
        const isAdmin = {{ auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('admin') ? 'true' : 'false' }};
        
        // Initialize map functionality
        if (typeof initializeMap === 'function') {
            initializeMap(centers, isAdmin);
        }
        
        // Initialize location search functionality
        if (typeof initializeLocationSearch === 'function') {
            initializeLocationSearch();
        }
        
        // Initialize barangay filter functionality
        if (typeof initializeBarangayFilter === 'function') {
            initializeBarangayFilter();
        }
    });
</script>
@endsection

