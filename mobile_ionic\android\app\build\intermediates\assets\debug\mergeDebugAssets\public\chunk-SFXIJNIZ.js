import{b as kc}from"./chunk-SV2ZKNWA.js";import{a as wo}from"./chunk-HC6MZPB3.js";import{c as xc,e as Ac,f as Rc,g as Nc,h as Oc}from"./chunk-Q5Q6EGIP.js";import{a as bo}from"./chunk-RS5W3JWO.js";import{c as Tc}from"./chunk-ZJ5IMUT4.js";import{m as Up}from"./chunk-SGSBBWFA.js";import{b as bc,c as wc,d as Ec,e as Mc,f as _c}from"./chunk-APL3YEA6.js";import{g as Rp,h as Np}from"./chunk-HSXX7Y3C.js";import{a as Vp,d as Bp}from"./chunk-FUGLTCJS.js";import{a as nn,d as Fp,e as jp,f as Lp,h as Cc}from"./chunk-XTVTS2NW.js";import{a as Sc}from"./chunk-NMYJD6OP.js";import{a as $e,b as Op,c as kp,d as Pp,e as Io,f as Co}from"./chunk-C5RQ2IC2.js";import{b as tn}from"./chunk-SV7S5NYR.js";import{a as b,b as V,d as Ic,g as ve}from"./chunk-2R6CW7ES.js";function rn(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var at=rn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function k(e){return typeof e=="function"}var Eo=rn(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function On(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var de=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let o of t)o.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(o){n=o instanceof Eo?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{$p(o)}catch(s){n=n??[],s instanceof Eo?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Eo(n)}}add(n){var t;if(n&&n!==this)if(this.closed)$p(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&On(t,n)}remove(n){let{_finalizers:t}=this;t&&On(t,n),n instanceof e&&n._removeParent(this)}};de.EMPTY=(()=>{let e=new de;return e.closed=!0,e})();var Pc=de.EMPTY;function Mo(e){return e instanceof de||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function $p(e){k(e)?e():e.unsubscribe()}var ct={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var dr={setTimeout(e,n,...t){let{delegate:r}=dr;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=dr;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function _o(e){dr.setTimeout(()=>{let{onUnhandledError:n}=ct;if(n)n(e);else throw e})}function fi(){}var Hp=Fc("C",void 0,void 0);function zp(e){return Fc("E",void 0,e)}function Gp(e){return Fc("N",e,void 0)}function Fc(e,n,t){return{kind:e,value:n,error:t}}var kn=null;function fr(e){if(ct.useDeprecatedSynchronousErrorHandling){let n=!kn;if(n&&(kn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=kn;if(kn=null,t)throw r}}else e()}function Wp(e){ct.useDeprecatedSynchronousErrorHandling&&kn&&(kn.errorThrown=!0,kn.error=e)}var Pn=class extends de{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Mo(n)&&n.add(this)):this.destination=U0}static create(n,t,r){return new on(n,t,r)}next(n){this.isStopped?Lc(Gp(n),this):this._next(n)}error(n){this.isStopped?Lc(zp(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Lc(Hp,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},V0=Function.prototype.bind;function jc(e,n){return V0.call(e,n)}var Vc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){So(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){So(r)}else So(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){So(t)}}},on=class extends Pn{constructor(n,t,r){super();let i;if(k(n)||!n)i={next:n??void 0,error:t??void 0,complete:r??void 0};else{let o;this&&ct.useDeprecatedNextContext?(o=Object.create(n),o.unsubscribe=()=>this.unsubscribe(),i={next:n.next&&jc(n.next,o),error:n.error&&jc(n.error,o),complete:n.complete&&jc(n.complete,o)}):i=n}this.destination=new Vc(i)}};function So(e){ct.useDeprecatedSynchronousErrorHandling?Wp(e):_o(e)}function B0(e){throw e}function Lc(e,n){let{onStoppedNotification:t}=ct;t&&dr.setTimeout(()=>t(e,n))}var U0={closed:!0,next:fi,error:B0,complete:fi};function $0(e,n){let t=typeof n=="object";return new Promise((r,i)=>{let o=new on({next:s=>{r(s),o.unsubscribe()},error:i,complete:()=>{t?r(n.defaultValue):i(new at)}});e.subscribe(o)})}var To=class extends de{constructor(n,t){super()}schedule(n,t=0){return this}};var hi={setInterval(e,n,...t){let{delegate:r}=hi;return r?.setInterval?r.setInterval(e,n,...t):setInterval(e,n,...t)},clearInterval(e){let{delegate:n}=hi;return(n?.clearInterval||clearInterval)(e)},delegate:void 0};var xo=class extends To{constructor(n,t){super(n,t),this.scheduler=n,this.work=t,this.pending=!1}schedule(n,t=0){var r;if(this.closed)return this;this.state=n;let i=this.id,o=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(o,i,t)),this.pending=!0,this.delay=t,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(o,this.id,t),this}requestAsyncId(n,t,r=0){return hi.setInterval(n.flush.bind(n,this),r)}recycleAsyncId(n,t,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return t;t!=null&&hi.clearInterval(t)}execute(n,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(n,t);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,t){let r=!1,i;try{this.work(n)}catch(o){r=!0,i=o||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),i}unsubscribe(){if(!this.closed){let{id:n,scheduler:t}=this,{actions:r}=t;this.work=this.state=this.scheduler=null,this.pending=!1,On(r,this),n!=null&&(this.id=this.recycleAsyncId(t,n,null)),this.delay=null,super.unsubscribe()}}};var Bc={now(){return(Bc.delegate||Date).now()},delegate:void 0};var hr=class e{constructor(n,t=e.now){this.schedulerActionCtor=n,this.now=t}schedule(n,t=0,r){return new this.schedulerActionCtor(this,n).schedule(r,t)}};hr.now=Bc.now;var Ao=class extends hr{constructor(n,t=hr.now){super(n,t),this.actions=[],this._active=!1}flush(n){let{actions:t}=this;if(this._active){t.push(n);return}let r;this._active=!0;do if(r=n.execute(n.state,n.delay))break;while(n=t.shift());if(this._active=!1,r){for(;n=t.shift();)n.unsubscribe();throw r}}};var pi=new Ao(xo),qp=pi;var pr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function xe(e){return e}function Uc(...e){return $c(e)}function $c(e){return e.length===0?xe:e.length===1?e[0]:function(t){return e.reduce((r,i)=>i(r),t)}}var W=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,i){let o=z0(t)?t:new on(t,r,i);return fr(()=>{let{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Zp(r),new r((i,o)=>{let s=new on({next:a=>{try{t(a)}catch(c){o(c),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[pr](){return this}pipe(...t){return $c(t)(this)}toPromise(t){return t=Zp(t),new t((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return e.create=n=>new e(n),e})();function Zp(e){var n;return(n=e??ct.Promise)!==null&&n!==void 0?n:Promise}function H0(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function z0(e){return e&&e instanceof Pn||H0(e)&&Mo(e)}function Ro(e){return e&&k(e.schedule)}function No(e){return e instanceof Date&&!isNaN(e)}function Oo(e=0,n,t=qp){let r=-1;return n!=null&&(Ro(n)?t=n:r=n),new W(i=>{let o=No(e)?+e-t.now():e;o<0&&(o=0);let s=0;return t.schedule(function(){i.closed||(i.next(s++),0<=r?this.schedule(void 0,r):i.complete())},o)})}function G0(e=0,n=pi){return e<0&&(e=0),Oo(e,e,n)}function Hc(e){return k(e?.lift)}function $(e){return n=>{if(Hc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function L(e,n,t,r,i){return new zc(e,n,t,r,i)}var zc=class extends Pn{constructor(n,t,r,i,o,s){super(n),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=i?function(a){try{i(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function gr(){return $((e,n)=>{let t=null;e._refCount++;let r=L(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let i=e._connection,o=t;t=null,i&&(!o||i===o)&&i.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var mr=class extends W{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,Hc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new de;let t=this.getSubject();n.add(this.source.subscribe(L(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=de.EMPTY)}return n}refCount(){return gr()(this)}};var Qp=rn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ae=(()=>{class e extends W{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new ko(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Qp}next(t){fr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){fr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){fr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Pc:(this.currentObservers=null,o.push(t),new de(()=>{this.currentObservers=null,On(o,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:i,isStopped:o}=this;r?t.error(i):o&&t.complete()}asObservable(){let t=new W;return t.source=this,t}}return e.create=(n,t)=>new ko(n,t),e})(),ko=class extends ae{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Pc}};var ye=class extends ae{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var He=new W(e=>e.complete());function Yp(e){return e[e.length-1]}function Po(e){return k(Yp(e))?e.pop():void 0}function sn(e){return Ro(Yp(e))?e.pop():void 0}function w(e,n,t,r){var i=arguments.length,o=i<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(o=(i<3?s(o):i>3?s(n,t,o):s(n,t))||o);return i>3&&o&&Object.defineProperty(n,t,o),o}function Xp(e,n,t,r){function i(o){return o instanceof t?o:new t(function(s){s(o)})}return new(t||(t=Promise))(function(o,s){function a(d){try{l(r.next(d))}catch(f){s(f)}}function c(d){try{l(r.throw(d))}catch(f){s(f)}}function l(d){d.done?o(d.value):i(d.value).then(a,c)}l((r=r.apply(e,n||[])).next())})}function Kp(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function Fn(e){return this instanceof Fn?(this.v=e,this):new Fn(e)}function Jp(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),i,o=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(h){return function(D){return Promise.resolve(D).then(h,f)}}function a(h,D){r[h]&&(i[h]=function(S){return new Promise(function(T,F){o.push([h,S,T,F])>1||c(h,S)})},D&&(i[h]=D(i[h])))}function c(h,D){try{l(r[h](D))}catch(S){p(o[0][3],S)}}function l(h){h.value instanceof Fn?Promise.resolve(h.value.v).then(d,f):p(o[0][2],h)}function d(h){c("next",h)}function f(h){c("throw",h)}function p(h,D){h(D),o.shift(),o.length&&c(o[0][0],o[0][1])}}function eg(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Kp=="function"?Kp(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(o){t[o]=e[o]&&function(s){return new Promise(function(a,c){s=e[o](s),i(a,c,s.done,s.value)})}}function i(o,s,a,c){Promise.resolve(c).then(function(l){o({value:l,done:a})},s)}}var vr=e=>e&&typeof e.length=="number"&&typeof e!="function";function Fo(e){return k(e?.then)}function jo(e){return k(e[pr])}function Lo(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function Vo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function W0(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Bo=W0();function Uo(e){return k(e?.[Bo])}function $o(e){return Jp(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:i}=yield Fn(t.read());if(i)return yield Fn(void 0);yield yield Fn(r)}}finally{t.releaseLock()}})}function Ho(e){return k(e?.getReader)}function ie(e){if(e instanceof W)return e;if(e!=null){if(jo(e))return q0(e);if(vr(e))return Z0(e);if(Fo(e))return Q0(e);if(Lo(e))return tg(e);if(Uo(e))return Y0(e);if(Ho(e))return K0(e)}throw Vo(e)}function q0(e){return new W(n=>{let t=e[pr]();if(k(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Z0(e){return new W(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function Q0(e){return new W(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,_o)})}function Y0(e){return new W(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function tg(e){return new W(n=>{X0(e,n).catch(t=>n.error(t))})}function K0(e){return tg($o(e))}function X0(e,n){var t,r,i,o;return Xp(this,void 0,void 0,function*(){try{for(t=eg(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=t.return)&&(yield o.call(t))}finally{if(i)throw i.error}}n.complete()})}function Ne(e,n,t,r=0,i=!1){let o=n.schedule(function(){t(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function zo(e,n=0){return $((t,r)=>{t.subscribe(L(r,i=>Ne(r,e,()=>r.next(i),n),()=>Ne(r,e,()=>r.complete(),n),i=>Ne(r,e,()=>r.error(i),n)))})}function Go(e,n=0){return $((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function ng(e,n){return ie(e).pipe(Go(n),zo(n))}function rg(e,n){return ie(e).pipe(Go(n),zo(n))}function ig(e,n){return new W(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function og(e,n){return new W(t=>{let r;return Ne(t,n,()=>{r=e[Bo](),Ne(t,n,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){t.error(s);return}o?t.complete():t.next(i)},0,!0)}),()=>k(r?.return)&&r.return()})}function Wo(e,n){if(!e)throw new Error("Iterable cannot be null");return new W(t=>{Ne(t,n,()=>{let r=e[Symbol.asyncIterator]();Ne(t,n,()=>{r.next().then(i=>{i.done?t.complete():t.next(i.value)})},0,!0)})})}function sg(e,n){return Wo($o(e),n)}function ag(e,n){if(e!=null){if(jo(e))return ng(e,n);if(vr(e))return ig(e,n);if(Fo(e))return rg(e,n);if(Lo(e))return Wo(e,n);if(Uo(e))return og(e,n);if(Ho(e))return sg(e,n)}throw Vo(e)}function ce(e,n){return n?ag(e,n):ie(e)}function O(...e){let n=sn(e);return ce(e,n)}function yr(e,n){let t=k(e)?e:()=>e,r=i=>i.error(t());return new W(n?i=>n.schedule(r,0,i):r)}function Gc(e){return!!e&&(e instanceof W||k(e.lift)&&k(e.subscribe))}var J0=rn(e=>function(t=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t});function eC(e,n){let{first:t,each:r,with:i=tC,scheduler:o=n??pi,meta:s=null}=No(e)?{first:e}:typeof e=="number"?{each:e}:e;if(t==null&&r==null)throw new TypeError("No timeout provided.");return $((a,c)=>{let l,d,f=null,p=0,h=D=>{d=Ne(c,o,()=>{try{l.unsubscribe(),ie(i({meta:s,lastValue:f,seen:p})).subscribe(c)}catch(S){c.error(S)}},D)};l=a.subscribe(L(c,D=>{d?.unsubscribe(),p++,c.next(f=D),r>0&&h(r)},void 0,void 0,()=>{d?.closed||d?.unsubscribe(),f=null})),!p&&h(t!=null?typeof t=="number"?t:+t-o.now():r)})}function tC(e){throw new J0(e)}function B(e,n){return $((t,r)=>{let i=0;t.subscribe(L(r,o=>{r.next(e.call(n,o,i++))}))})}var{isArray:nC}=Array;function rC(e,n){return nC(n)?e(...n):e(n)}function Dr(e){return B(n=>rC(e,n))}var{isArray:iC}=Array,{getPrototypeOf:oC,prototype:sC,keys:aC}=Object;function qo(e){if(e.length===1){let n=e[0];if(iC(n))return{args:n,keys:null};if(cC(n)){let t=aC(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function cC(e){return e&&typeof e=="object"&&oC(e)===sC}function Zo(e,n){return e.reduce((t,r,i)=>(t[r]=n[i],t),{})}function jn(...e){let n=sn(e),t=Po(e),{args:r,keys:i}=qo(e);if(r.length===0)return ce([],n);let o=new W(lC(r,n,i?s=>Zo(i,s):xe));return t?o.pipe(Dr(t)):o}function lC(e,n,t=xe){return r=>{cg(n,()=>{let{length:i}=e,o=new Array(i),s=i,a=i;for(let c=0;c<i;c++)cg(n,()=>{let l=ce(e[c],n),d=!1;l.subscribe(L(r,f=>{o[c]=f,d||(d=!0,a--),a||r.next(t(o.slice()))},()=>{--s||r.complete()}))},r)},r)}}function cg(e,n,t){e?Ne(t,e,n):n()}function lg(e,n,t,r,i,o,s,a){let c=[],l=0,d=0,f=!1,p=()=>{f&&!c.length&&!l&&n.complete()},h=S=>l<r?D(S):c.push(S),D=S=>{o&&n.next(S),l++;let T=!1;ie(t(S,d++)).subscribe(L(n,F=>{i?.(F),o?h(F):n.next(F)},()=>{T=!0},void 0,()=>{if(T)try{for(l--;c.length&&l<r;){let F=c.shift();s?Ne(n,s,()=>D(F)):D(F)}p()}catch(F){n.error(F)}}))};return e.subscribe(L(n,h,()=>{f=!0,p()})),()=>{a?.()}}function fe(e,n,t=1/0){return k(n)?fe((r,i)=>B((o,s)=>n(r,o,i,s))(ie(e(r,i))),t):(typeof n=="number"&&(t=n),$((r,i)=>lg(r,i,e,t)))}function Ir(e=1/0){return fe(xe,e)}function ug(){return Ir(1)}function Cr(...e){return ug()(ce(e,sn(e)))}function Qo(e){return new W(n=>{ie(e()).subscribe(n)})}function Wc(...e){let n=Po(e),{args:t,keys:r}=qo(e),i=new W(o=>{let{length:s}=t;if(!s){o.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let f=!1;ie(t[d]).subscribe(L(o,p=>{f||(f=!0,l--),a[d]=p},()=>c--,void 0,()=>{(!c||!f)&&(l||o.next(r?Zo(r,a):a),o.complete())}))}});return n?i.pipe(Dr(n)):i}var uC=["addListener","removeListener"],dC=["addEventListener","removeEventListener"],fC=["on","off"];function Ln(e,n,t,r){if(k(t)&&(r=t,t=void 0),r)return Ln(e,n,t).pipe(Dr(r));let[i,o]=gC(e)?dC.map(s=>a=>e[s](n,a,t)):hC(e)?uC.map(dg(e,n)):pC(e)?fC.map(dg(e,n)):[];if(!i&&vr(e))return fe(s=>Ln(s,n,t))(ie(e));if(!i)throw new TypeError("Invalid event target");return new W(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return i(a),()=>o(a)})}function dg(e,n){return t=>r=>e[t](n,r)}function hC(e){return k(e.addListener)&&k(e.removeListener)}function pC(e){return k(e.on)&&k(e.off)}function gC(e){return k(e.addEventListener)&&k(e.removeEventListener)}function Ae(e,n){return $((t,r)=>{let i=0;t.subscribe(L(r,o=>e.call(n,o,i++)&&r.next(o)))})}function Ot(e){return $((n,t)=>{let r=null,i=!1,o;r=n.subscribe(L(t,void 0,void 0,s=>{o=ie(e(s,Ot(e)(n))),r?(r.unsubscribe(),r=null,o.subscribe(t)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(t))})}function fg(e,n,t,r,i){return(o,s)=>{let a=t,c=n,l=0;o.subscribe(L(s,d=>{let f=l++;c=a?e(c,d,f):(a=!0,d),r&&s.next(c)},i&&(()=>{a&&s.next(c),s.complete()})))}}function kt(e,n){return k(n)?fe(e,n,1):fe(e,1)}function an(e){return $((n,t)=>{let r=!1;n.subscribe(L(t,i=>{r=!0,t.next(i)},()=>{r||t.next(e),t.complete()}))})}function Pt(e){return e<=0?()=>He:$((n,t)=>{let r=0;n.subscribe(L(t,i=>{++r<=e&&(t.next(i),e<=r&&t.complete())}))})}function qc(e){return B(()=>e)}function Zc(e,n=xe){return e=e??mC,$((t,r)=>{let i,o=!0;t.subscribe(L(r,s=>{let a=n(s);(o||!e(i,a))&&(o=!1,i=a,r.next(s))}))})}function mC(e,n){return e===n}function Yo(e=vC){return $((n,t)=>{let r=!1;n.subscribe(L(t,i=>{r=!0,t.next(i)},()=>r?t.complete():t.error(e())))})}function vC(){return new at}function cn(e){return $((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function It(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Ae((i,o)=>e(i,o,r)):xe,Pt(1),t?an(n):Yo(()=>new at))}function br(e){return e<=0?()=>He:$((n,t)=>{let r=[];n.subscribe(L(t,i=>{r.push(i),e<r.length&&r.shift()},()=>{for(let i of r)t.next(i);t.complete()},void 0,()=>{r=null}))})}function Qc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?Ae((i,o)=>e(i,o,r)):xe,br(1),t?an(n):Yo(()=>new at))}function yC(e=1/0){let n;e&&typeof e=="object"?n=e:n={count:e};let{count:t=1/0,delay:r,resetOnSuccess:i=!1}=n;return t<=0?xe:$((o,s)=>{let a=0,c,l=()=>{let d=!1;c=o.subscribe(L(s,f=>{i&&(a=0),s.next(f)},void 0,f=>{if(a++<t){let p=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(r!=null){let h=typeof r=="number"?Oo(r):ie(r(f,a)),D=L(s,()=>{D.unsubscribe(),p()},()=>{s.complete()});h.subscribe(D)}else p()}else s.error(f)})),d&&(c.unsubscribe(),c=null,l())};l()})}function Yc(e,n){return $(fg(e,n,arguments.length>=2,!0))}function Kc(...e){let n=sn(e);return $((t,r)=>{(n?Cr(e,t,n):Cr(e,t)).subscribe(r)})}function we(e,n){return $((t,r)=>{let i=null,o=0,s=!1,a=()=>s&&!i&&r.complete();t.subscribe(L(r,c=>{i?.unsubscribe();let l=0,d=o++;ie(e(c,d)).subscribe(i=L(r,f=>r.next(n?n(c,f,d,l++):f),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function Xc(e){return $((n,t)=>{ie(e).subscribe(L(t,()=>t.complete(),fi)),!t.closed&&n.subscribe(t)})}function Ee(e,n,t){let r=k(e)||n||t?{next:e,error:n,complete:t}:e;return r?$((i,o)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;i.subscribe(L(o,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),o.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),o.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),o.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):xe}function hg(e,n){return Object.is(e,n)}var be=null,Ko=!1,Xo=1,Ft=Symbol("SIGNAL");function Q(e){let n=be;return be=e,n}function pg(){return be}var mi={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function tl(e){if(Ko)throw new Error("");if(be===null)return;be.consumerOnSignalRead(e);let n=be.nextProducerIndex++;if(ns(be),n<be.producerNode.length&&be.producerNode[n]!==e&&gi(be)){let t=be.producerNode[n];ts(t,be.producerIndexOfThis[n])}be.producerNode[n]!==e&&(be.producerNode[n]=e,be.producerIndexOfThis[n]=gi(be)?yg(e,be,n):0),be.producerLastReadVersion[n]=e.version}function DC(){Xo++}function gg(e){if(!(gi(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Xo)){if(!e.producerMustRecompute(e)&&!rl(e)){e.dirty=!1,e.lastCleanEpoch=Xo;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Xo}}function mg(e){if(e.liveConsumerNode===void 0)return;let n=Ko;Ko=!0;try{for(let t of e.liveConsumerNode)t.dirty||IC(t)}finally{Ko=n}}function vg(){return be?.consumerAllowSignalWrites!==!1}function IC(e){e.dirty=!0,mg(e),e.consumerMarkedDirty?.(e)}function es(e){return e&&(e.nextProducerIndex=0),Q(e)}function nl(e,n){if(Q(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(gi(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)ts(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function rl(e){ns(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(gg(t),r!==t.version))return!0}return!1}function il(e){if(ns(e),gi(e))for(let n=0;n<e.producerNode.length;n++)ts(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function yg(e,n,t){if(Dg(e),e.liveConsumerNode.length===0&&Ig(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=yg(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function ts(e,n){if(Dg(e),e.liveConsumerNode.length===1&&Ig(e))for(let r=0;r<e.producerNode.length;r++)ts(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],i=e.liveConsumerNode[n];ns(i),i.producerIndexOfThis[r]=n}}function gi(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ns(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Dg(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ig(e){return e.producerNode!==void 0}function Cg(e){let n=Object.create(CC);n.computation=e;let t=()=>{if(gg(n),tl(n),n.value===Jo)throw n.error;return n.value};return t[Ft]=n,t}var Jc=Symbol("UNSET"),el=Symbol("COMPUTING"),Jo=Symbol("ERRORED"),CC=V(b({},mi),{value:Jc,dirty:!0,error:null,equal:hg,producerMustRecompute(e){return e.value===Jc||e.value===el},producerRecomputeValue(e){if(e.value===el)throw new Error("Detected cycle in computations.");let n=e.value;e.value=el;let t=es(e),r;try{r=e.computation()}catch(i){r=Jo,e.error=i}finally{nl(e,t)}if(n!==Jc&&n!==Jo&&r!==Jo&&e.equal(n,r)){e.value=n;return}e.value=r,e.version++}});function bC(){throw new Error}var bg=bC;function wg(){bg()}function Eg(e){bg=e}var wC=null;function Mg(e){let n=Object.create(Sg);n.value=e;let t=()=>(tl(n),n.value);return t[Ft]=n,t}function ol(e,n){vg()||wg(),e.equal(e.value,n)||(e.value=n,EC(e))}function _g(e,n){vg()||wg(),ol(e,n(e.value))}var Sg=V(b({},mi),{equal:hg,value:void 0});function EC(e){e.version++,DC(),mg(e),wC?.()}var mm="https://g.co/ng/security#xss",R=class extends Error{constructor(n,t){super(Bs(n,t)),this.code=n}};function Bs(e,n){return`${`NG0${Math.abs(e)}`}${n?": "+n:""}`}function _i(e){return{toString:e}.toString()}var rs="__parameters__";function MC(e){return function(...t){if(e){let r=e(...t);for(let i in r)this[i]=r[i]}}}function vm(e,n,t){return _i(()=>{let r=MC(n);function i(...o){if(this instanceof i)return r.apply(this,o),this;let s=new i(...o);return a.annotation=s,a;function a(c,l,d){let f=c.hasOwnProperty(rs)?c[rs]:Object.defineProperty(c,rs,{value:[]})[rs];for(;f.length<=d;)f.push(null);return(f[d]=f[d]||[]).push(s),c}}return t&&(i.prototype=Object.create(t.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var jt=globalThis;function te(e){for(let n in e)if(e[n]===te)return n;throw Error("Could not find renamed property on target object.")}function _C(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Fe(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Fe).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let n=e.toString();if(n==null)return""+n;let t=n.indexOf(`
`);return t===-1?n:n.substring(0,t)}function Tg(e,n){return e==null||e===""?n===null?"":n:n==null||n===""?e:e+" "+n}var SC=te({__forward_ref__:te});function et(e){return e.__forward_ref__=et,e.toString=function(){return Fe(this())},e}function Pe(e){return ym(e)?e():e}function ym(e){return typeof e=="function"&&e.hasOwnProperty(SC)&&e.__forward_ref__===et}function x(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ft(e){return{providers:e.providers||[],imports:e.imports||[]}}function Us(e){return xg(e,Im)||xg(e,Cm)}function Dm(e){return Us(e)!==null}function xg(e,n){return e.hasOwnProperty(n)?e[n]:null}function TC(e){let n=e&&(e[Im]||e[Cm]);return n||null}function Ag(e){return e&&(e.hasOwnProperty(Rg)||e.hasOwnProperty(xC))?e[Rg]:null}var Im=te({\u0275prov:te}),Rg=te({\u0275inj:te}),Cm=te({ngInjectableDef:te}),xC=te({ngInjectorDef:te}),A=class{constructor(n,t){this._desc=n,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=x({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function bm(e){return e&&!!e.\u0275providers}var AC=te({\u0275cmp:te}),RC=te({\u0275dir:te}),NC=te({\u0275pipe:te}),OC=te({\u0275mod:te}),ms=te({\u0275fac:te}),yi=te({__NG_ELEMENT_ID__:te}),Ng=te({__NG_ENV_ID__:te});function Un(e){return typeof e=="string"?e:e==null?"":String(e)}function kC(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Un(e)}function PC(e,n){let t=n?`. Dependency path: ${n.join(" > ")} > ${e}`:"";throw new R(-200,e)}function Iu(e,n){throw new R(-201,!1)}var z=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(z||{}),Il;function wm(){return Il}function Ve(e){let n=Il;return Il=e,n}function Em(e,n,t){let r=Us(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&z.Optional)return null;if(n!==void 0)return n;Iu(e,"Injector")}var FC={},Di=FC,Cl="__NG_DI_FLAG__",vs="ngTempTokenPath",jC="ngTokenPath",LC=/\n/gm,VC="\u0275",Og="__source",Sr;function BC(){return Sr}function ln(e){let n=Sr;return Sr=e,n}function UC(e,n=z.Default){if(Sr===void 0)throw new R(-203,!1);return Sr===null?Em(e,void 0,n):Sr.get(e,n&z.Optional?null:void 0,n)}function N(e,n=z.Default){return(wm()||UC)(Pe(e),n)}function v(e,n=z.Default){return N(e,$s(n))}function $s(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function bl(e){let n=[];for(let t=0;t<e.length;t++){let r=Pe(e[t]);if(Array.isArray(r)){if(r.length===0)throw new R(900,!1);let i,o=z.Default;for(let s=0;s<r.length;s++){let a=r[s],c=$C(a);typeof c=="number"?c===-1?i=a.token:o|=c:i=a}n.push(N(i,o))}else n.push(N(r))}return n}function Mm(e,n){return e[Cl]=n,e.prototype[Cl]=n,e}function $C(e){return e[Cl]}function HC(e,n,t,r){let i=e[vs];throw n[Og]&&i.unshift(n[Og]),e.message=zC(`
`+e.message,i,t,r),e[jC]=i,e[vs]=null,e}function zC(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==VC?e.slice(2):e;let i=Fe(n);if(Array.isArray(n))i=n.map(Fe).join(" -> ");else if(typeof n=="object"){let o=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];o.push(s+":"+(typeof a=="string"?JSON.stringify(a):Fe(a)))}i=`{${o.join(", ")}}`}return`${t}${r?"("+r+")":""}[${i}]: ${e.replace(LC,`
  `)}`}var Hs=Mm(vm("Optional"),8);var Cu=Mm(vm("SkipSelf"),4);function $n(e,n){let t=e.hasOwnProperty(ms);return t?e[ms]:null}function GC(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=n[r];if(t&&(i=t(i),o=t(o)),o!==i)return!1}return!0}function WC(e){return e.flat(Number.POSITIVE_INFINITY)}function bu(e,n){e.forEach(t=>Array.isArray(t)?bu(t,n):n(t))}function _m(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function ys(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function qC(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function ZC(e,n,t,r){let i=e.length;if(i==n)e.push(t,r);else if(i===1)e.push(r,e[0]),e[0]=t;else{for(i--,e.push(e[i-1],e[i]);i>n;){let o=i-2;e[i]=e[o],i--}e[n]=t,e[n+1]=r}}function QC(e,n,t){let r=Si(e,n);return r>=0?e[r|1]=t:(r=~r,ZC(e,r,n,t)),r}function sl(e,n){let t=Si(e,n);if(t>=0)return e[t|1]}function Si(e,n){return YC(e,n,1)}function YC(e,n,t){let r=0,i=e.length>>t;for(;i!==r;){let o=r+(i-r>>1),s=e[o<<t];if(n===s)return o<<t;s>n?i=o:r=o+1}return~(i<<t)}var xr={},Ke=[],Ar=new A(""),Sm=new A("",-1),Tm=new A(""),Ds=class{get(n,t=Di){if(t===Di){let r=new Error(`NullInjectorError: No provider for ${Fe(n)}!`);throw r.name="NullInjectorError",r}return t}},xm=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(xm||{}),wt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(wt||{}),fn=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(fn||{});function KC(e,n,t){let r=e.length;for(;;){let i=e.indexOf(n,t);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=n.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}t=i+1}}function wl(e,n,t){let r=0;for(;r<t.length;){let i=t[r];if(typeof i=="number"){if(i!==0)break;r++;let o=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,o)}else{let o=i,s=t[++r];XC(o)?e.setProperty(n,o,s):e.setAttribute(n,o,s),r++}}return r}function Am(e){return e===3||e===4||e===6}function XC(e){return e.charCodeAt(0)===64}function Ii(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let i=n[r];typeof i=="number"?t=i:t===0||(t===-1||t===2?kg(e,t,i,null,n[++r]):kg(e,t,i,null,null))}}return e}function kg(e,n,t,r,i){let o=0,s=e.length;if(n===-1)s=-1;else for(;o<e.length;){let a=e[o++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=o-1;break}}}for(;o<e.length;){let a=e[o];if(typeof a=="number")break;if(a===t){if(r===null){i!==null&&(e[o+1]=i);return}else if(r===e[o+1]){e[o+2]=i;return}}o++,r!==null&&o++,i!==null&&o++}s!==-1&&(e.splice(s,0,n),o=s+1),e.splice(o++,0,t),r!==null&&e.splice(o++,0,r),i!==null&&e.splice(o++,0,i)}var Rm="ng-template";function JC(e,n,t,r){let i=0;if(r){for(;i<n.length&&typeof n[i]=="string";i+=2)if(n[i]==="class"&&KC(n[i+1].toLowerCase(),t,0)!==-1)return!0}else if(wu(e))return!1;if(i=n.indexOf(1,i),i>-1){let o;for(;++i<n.length&&typeof(o=n[i])=="string";)if(o.toLowerCase()===t)return!0}return!1}function wu(e){return e.type===4&&e.value!==Rm}function eb(e,n,t){let r=e.type===4&&!t?Rm:e.value;return n===r}function tb(e,n,t){let r=4,i=e.attrs,o=i!==null?ib(i):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!lt(r)&&!lt(c))return!1;if(s&&lt(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!eb(e,c,t)||c===""&&n.length===1){if(lt(r))return!1;s=!0}}else if(r&8){if(i===null||!JC(e,i,c,t)){if(lt(r))return!1;s=!0}}else{let l=n[++a],d=nb(c,i,wu(e),t);if(d===-1){if(lt(r))return!1;s=!0;continue}if(l!==""){let f;if(d>o?f="":f=i[d+1].toLowerCase(),r&2&&l!==f){if(lt(r))return!1;s=!0}}}}return lt(r)||s}function lt(e){return(e&1)===0}function nb(e,n,t,r){if(n===null)return-1;let i=0;if(r||!t){let o=!1;for(;i<n.length;){let s=n[i];if(s===e)return i;if(s===3||s===6)o=!0;else if(s===1||s===2){let a=n[++i];for(;typeof a=="string";)a=n[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=o?1:2}return-1}else return ob(n,e)}function Nm(e,n,t=!1){for(let r=0;r<n.length;r++)if(tb(e,n[r],t))return!0;return!1}function rb(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if(!(t&1))return n[t+1]}return null}function ib(e){for(let n=0;n<e.length;n++){let t=e[n];if(Am(t))return n}return e.length}function ob(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function sb(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let i=0;i<e.length;i++)if(e[i]!==r[i])continue e;return!0}}return!1}function Pg(e,n){return e?":not("+n.trim()+")":n}function ab(e){let n=e[0],t=1,r=2,i="",o=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?i+="."+s:r&4&&(i+=" "+s);else i!==""&&!lt(s)&&(n+=Pg(o,i),i=""),r=s,o=o||!lt(r);t++}return i!==""&&(n+=Pg(o,i)),n}function cb(e){return e.map(ab).join(",")}function lb(e){let n=[],t=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&n.push(o,e[++r]):i===8&&t.push(o);else{if(!lt(i))break;i=o}r++}return{attrs:n,classes:t}}function I(e){return _i(()=>{let n=Lm(e),t=V(b({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===xm.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||wt.Emulated,styles:e.styles||Ke,_:null,schemas:e.schemas||null,tView:null,id:""});Vm(t);let r=e.dependencies;return t.directiveDefs=jg(r,!1),t.pipeDefs=jg(r,!0),t.id=fb(t),t})}function ub(e){return Lt(e)||km(e)}function db(e){return e!==null}function ht(e){return _i(()=>({type:e.type,bootstrap:e.bootstrap||Ke,declarations:e.declarations||Ke,imports:e.imports||Ke,exports:e.exports||Ke,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Fg(e,n){if(e==null)return xr;let t={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,s,a=fn.None;Array.isArray(i)?(a=i[0],o=i[1],s=i[2]??o):(o=i,s=i),n?(t[o]=a!==fn.None?[r,a]:r,n[o]=s):t[o]=r}return t}function H(e){return _i(()=>{let n=Lm(e);return Vm(n),n})}function Om(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Lt(e){return e[AC]||null}function km(e){return e[RC]||null}function Pm(e){return e[NC]||null}function Fm(e){let n=Lt(e)||km(e)||Pm(e);return n!==null?n.standalone:!1}function jm(e,n){let t=e[OC]||null;if(!t&&n===!0)throw new Error(`Type ${Fe(e)} does not have '\u0275mod' property.`);return t}function Lm(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputTransforms:null,inputConfig:e.inputs||xr,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||Ke,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Fg(e.inputs,n),outputs:Fg(e.outputs),debugInfo:null}}function Vm(e){e.features?.forEach(n=>n(e))}function jg(e,n){if(!e)return null;let t=n?Pm:ub;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(db)}function fb(e){let n=0,t=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let i of t)n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=**********,"c"+n}function Ti(e){return{\u0275providers:e}}function hb(...e){return{\u0275providers:Bm(!0,e),\u0275fromNgModule:!0}}function Bm(e,...n){let t=[],r=new Set,i,o=s=>{t.push(s)};return bu(n,s=>{let a=s;El(a,o,[],r)&&(i||=[],i.push(a))}),i!==void 0&&Um(i,o),t}function Um(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:i}=e[t];Eu(i,o=>{n(o,r)})}}function El(e,n,t,r){if(e=Pe(e),!e)return!1;let i=null,o=Ag(e),s=!o&&Lt(e);if(!o&&!s){let c=e.ngModule;if(o=Ag(c),o)i=c;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)El(l,n,t,r)}}else if(o){if(o.imports!=null&&!a){r.add(i);let l;try{bu(o.imports,d=>{El(d,n,t,r)&&(l||=[],l.push(d))})}finally{}l!==void 0&&Um(l,n)}if(!a){let l=$n(i)||(()=>new i);n({provide:i,useFactory:l,deps:Ke},i),n({provide:Tm,useValue:i,multi:!0},i),n({provide:Ar,useValue:()=>N(i),multi:!0},i)}let c=o.providers;if(c!=null&&!a){let l=e;Eu(c,d=>{n(d,l)})}}else return!1;return i!==e&&e.providers!==void 0}function Eu(e,n){for(let t of e)bm(t)&&(t=t.\u0275providers),Array.isArray(t)?Eu(t,n):n(t)}var pb=te({provide:String,useValue:te});function $m(e){return e!==null&&typeof e=="object"&&pb in e}function gb(e){return!!(e&&e.useExisting)}function mb(e){return!!(e&&e.useFactory)}function Rr(e){return typeof e=="function"}function vb(e){return!!e.useClass}var zs=new A(""),us={},yb={},al;function Gs(){return al===void 0&&(al=new Ds),al}var ue=class{},Ci=class extends ue{get destroyed(){return this._destroyed}constructor(n,t,r,i){super(),this.parent=t,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,_l(n,s=>this.processProvider(s)),this.records.set(Sm,wr(void 0,this)),i.has("environment")&&this.records.set(ue,wr(void 0,this));let o=this.records.get(zs);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Tm,Ke,z.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let n=Q(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Q(n)}}onDestroy(n){return this.assertNotDestroyed(),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){this.assertNotDestroyed();let t=ln(this),r=Ve(void 0),i;try{return n()}finally{ln(t),Ve(r)}}get(n,t=Di,r=z.Default){if(this.assertNotDestroyed(),n.hasOwnProperty(Ng))return n[Ng](this);r=$s(r);let i,o=ln(this),s=Ve(void 0);try{if(!(r&z.SkipSelf)){let c=this.records.get(n);if(c===void 0){let l=wb(n)&&Us(n);l&&this.injectableDefInScope(l)?c=wr(Ml(n),us):c=null,this.records.set(n,c)}if(c!=null)return this.hydrate(n,c)}let a=r&z.Self?Gs():this.parent;return t=r&z.Optional&&t===Di?null:t,a.get(n,t)}catch(a){if(a.name==="NullInjectorError"){if((a[vs]=a[vs]||[]).unshift(Fe(n)),o)throw a;return HC(a,n,"R3InjectorError",this.source)}else throw a}finally{Ve(s),ln(o)}}resolveInjectorInitializers(){let n=Q(null),t=ln(this),r=Ve(void 0),i;try{let o=this.get(Ar,Ke,z.Self);for(let s of o)s()}finally{ln(t),Ve(r),Q(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Fe(r));return`R3Injector[${n.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new R(205,!1)}processProvider(n){n=Pe(n);let t=Rr(n)?n:Pe(n&&n.provide),r=Ib(n);if(!Rr(n)&&n.multi===!0){let i=this.records.get(t);i||(i=wr(void 0,us,!0),i.factory=()=>bl(i.multi),this.records.set(t,i)),t=n,i.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=Q(null);try{return t.value===us&&(t.value=yb,t.value=t.factory()),typeof t.value=="object"&&t.value&&bb(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{Q(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=Pe(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Ml(e){let n=Us(e),t=n!==null?n.factory:$n(e);if(t!==null)return t;if(e instanceof A)throw new R(204,!1);if(e instanceof Function)return Db(e);throw new R(204,!1)}function Db(e){if(e.length>0)throw new R(204,!1);let t=TC(e);return t!==null?()=>t.factory(e):()=>new e}function Ib(e){if($m(e))return wr(void 0,e.useValue);{let n=Hm(e);return wr(n,us)}}function Hm(e,n,t){let r;if(Rr(e)){let i=Pe(e);return $n(i)||Ml(i)}else if($m(e))r=()=>Pe(e.useValue);else if(mb(e))r=()=>e.useFactory(...bl(e.deps||[]));else if(gb(e))r=()=>N(Pe(e.useExisting));else{let i=Pe(e&&(e.useClass||e.provide));if(Cb(e))r=()=>new i(...bl(e.deps));else return $n(i)||Ml(i)}return r}function wr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function Cb(e){return!!e.deps}function bb(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function wb(e){return typeof e=="function"||typeof e=="object"&&e instanceof A}function _l(e,n){for(let t of e)Array.isArray(t)?_l(t,n):t&&bm(t)?_l(t.\u0275providers,n):n(t)}function tt(e,n){e instanceof Ci&&e.assertNotDestroyed();let t,r=ln(e),i=Ve(void 0);try{return n()}finally{ln(r),Ve(i)}}function zm(){return wm()!==void 0||BC()!=null}function Eb(e){if(!zm())throw new R(-203,!1)}function Mb(e){return typeof e=="function"}var $t=0,U=1,P=2,Oe=3,ut=4,Be=5,bi=6,Is=7,dt=8,Nr=9,Et=10,he=11,wi=12,Lg=13,Vr=14,Je=15,Hn=16,Er=17,Vt=18,Ws=19,Gm=20,un=21,cl=22,Xe=23,Ge=25,Wm=1;var zn=7,Cs=8,Or=9,ze=10,bs=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(bs||{});function dn(e){return Array.isArray(e)&&typeof e[Wm]=="object"}function Ht(e){return Array.isArray(e)&&e[Wm]===!0}function Mu(e){return(e.flags&4)!==0}function qs(e){return e.componentOffset>-1}function Zs(e){return(e.flags&1)===1}function hn(e){return!!e.template}function Sl(e){return(e[P]&512)!==0}var Tl=class{constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function qm(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}function nt(){return Zm}function Zm(e){return e.type.prototype.ngOnChanges&&(e.setInput=Sb),_b}nt.ngInherit=!0;function _b(){let e=Ym(this),n=e?.current;if(n){let t=e.previous;if(t===xr)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function Sb(e,n,t,r,i){let o=this.declaredInputs[r],s=Ym(e)||Tb(e,{previous:xr,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[o];a[o]=new Tl(l&&l.currentValue,t,c===xr),qm(e,n,i,t)}var Qm="__ngSimpleChanges__";function Ym(e){return e[Qm]||null}function Tb(e,n){return e[Qm]=n}var Vg=null;var Ct=function(e,n,t){Vg?.(e,n,t)},xb="svg",Ab="math";function Mt(e){for(;Array.isArray(e);)e=e[$t];return e}function Km(e,n){return Mt(n[e])}function rt(e,n){return Mt(n[e.index])}function Xm(e,n){return e.data[n]}function Rb(e,n){return e[n]}function mn(e,n){let t=n[e];return dn(t)?t:t[$t]}function Nb(e){return(e[P]&4)===4}function _u(e){return(e[P]&128)===128}function Ob(e){return Ht(e[Oe])}function kr(e,n){return n==null?null:e[n]}function Jm(e){e[Er]=0}function ev(e){e[P]&1024||(e[P]|=1024,_u(e)&&Ys(e))}function kb(e,n){for(;e>0;)n=n[Vr],e--;return n}function Qs(e){return!!(e[P]&9216||e[Xe]?.dirty)}function xl(e){e[Et].changeDetectionScheduler?.notify(8),e[P]&64&&(e[P]|=1024),Qs(e)&&Ys(e)}function Ys(e){e[Et].changeDetectionScheduler?.notify(0);let n=Gn(e);for(;n!==null&&!(n[P]&8192||(n[P]|=8192,!_u(n)));)n=Gn(n)}function tv(e,n){if((e[P]&256)===256)throw new R(911,!1);e[un]===null&&(e[un]=[]),e[un].push(n)}function Pb(e,n){if(e[un]===null)return;let t=e[un].indexOf(n);t!==-1&&e[un].splice(t,1)}function Gn(e){let n=e[Oe];return Ht(n)?n[Oe]:n}var G={lFrame:dv(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var nv=!1;function Fb(){return G.lFrame.elementDepthCount}function jb(){G.lFrame.elementDepthCount++}function Lb(){G.lFrame.elementDepthCount--}function rv(){return G.bindingsEnabled}function iv(){return G.skipHydrationRootTNode!==null}function Vb(e){return G.skipHydrationRootTNode===e}function Bb(){G.skipHydrationRootTNode=null}function q(){return G.lFrame.lView}function pe(){return G.lFrame.tView}function Su(e){return G.lFrame.contextLView=e,e[dt]}function Tu(e){return G.lFrame.contextLView=null,e}function Re(){let e=ov();for(;e!==null&&e.type===64;)e=e.parent;return e}function ov(){return G.lFrame.currentTNode}function Ub(){let e=G.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function Jn(e,n){let t=G.lFrame;t.currentTNode=e,t.isParent=n}function xu(){return G.lFrame.isParent}function Au(){G.lFrame.isParent=!1}function sv(){return nv}function Bg(e){nv=e}function Ru(){let e=G.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function $b(){return G.lFrame.bindingIndex}function Hb(e){return G.lFrame.bindingIndex=e}function Ks(){return G.lFrame.bindingIndex++}function av(e){let n=G.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function zb(){return G.lFrame.inI18n}function Gb(e,n){let t=G.lFrame;t.bindingIndex=t.bindingRootIndex=e,Al(n)}function Wb(){return G.lFrame.currentDirectiveIndex}function Al(e){G.lFrame.currentDirectiveIndex=e}function qb(e){let n=G.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function cv(){return G.lFrame.currentQueryIndex}function Nu(e){G.lFrame.currentQueryIndex=e}function Zb(e){let n=e[U];return n.type===2?n.declTNode:n.type===1?e[Be]:null}function lv(e,n,t){if(t&z.SkipSelf){let i=n,o=e;for(;i=i.parent,i===null&&!(t&z.Host);)if(i=Zb(o),i===null||(o=o[Vr],i.type&10))break;if(i===null)return!1;n=i,e=o}let r=G.lFrame=uv();return r.currentTNode=n,r.lView=e,!0}function Ou(e){let n=uv(),t=e[U];G.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function uv(){let e=G.lFrame,n=e===null?null:e.child;return n===null?dv(e):n}function dv(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function fv(){let e=G.lFrame;return G.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var hv=fv;function ku(){let e=fv();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Qb(e){return(G.lFrame.contextLView=kb(e,G.lFrame.contextLView))[dt]}function er(){return G.lFrame.selectedIndex}function Wn(e){G.lFrame.selectedIndex=e}function Xs(){let e=G.lFrame;return Xm(e.tView,e.selectedIndex)}function Yb(){return G.lFrame.currentNamespace}var pv=!0;function Js(){return pv}function ea(e){pv=e}function Kb(e,n,t){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=n.type.prototype;if(r){let s=Zm(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}i&&(t.preOrderHooks??=[]).push(0-e,i),o&&((t.preOrderHooks??=[]).push(e,o),(t.preOrderCheckHooks??=[]).push(e,o))}function ta(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let o=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:d}=o;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),l&&((e.viewHooks??=[]).push(t,l),(e.viewCheckHooks??=[]).push(t,l)),d!=null&&(e.destroyHooks??=[]).push(t,d)}}function ds(e,n,t){gv(e,n,3,t)}function fs(e,n,t,r){(e[P]&3)===t&&gv(e,n,t,r)}function ll(e,n){let t=e[P];(t&3)===n&&(t&=16383,t+=1,e[P]=t)}function gv(e,n,t,r){let i=r!==void 0?e[Er]&65535:0,o=r??-1,s=n.length-1,a=0;for(let c=i;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[Er]+=65536),(a<o||o==-1)&&(Xb(e,t,n,c),e[Er]=(e[Er]&**********)+c+2),c++}function Ug(e,n){Ct(4,e,n);let t=Q(null);try{n.call(e)}finally{Q(t),Ct(5,e,n)}}function Xb(e,n,t,r){let i=t[r]<0,o=t[r+1],s=i?-t[r]:t[r],a=e[s];i?e[P]>>14<e[Er]>>16&&(e[P]&3)===n&&(e[P]+=16384,Ug(a,o)):Ug(a,o)}var Tr=-1,qn=class{constructor(n,t,r){this.factory=n,this.resolving=!1,this.canSeeViewProviders=t,this.injectImpl=r}};function Jb(e){return e instanceof qn}function ew(e){return(e.flags&8)!==0}function tw(e){return(e.flags&16)!==0}var ul={},Rl=class{constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=$s(r);let i=this.injector.get(n,ul,r);return i!==ul||t===ul?i:this.parentInjector.get(n,t,r)}};function mv(e){return e!==Tr}function ws(e){return e&32767}function nw(e){return e>>16}function Es(e,n){let t=nw(e),r=n;for(;t>0;)r=r[Vr],t--;return r}var Nl=!0;function Ms(e){let n=Nl;return Nl=e,n}var rw=256,vv=rw-1,yv=5,iw=0,bt={};function ow(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(yi)&&(r=t[yi]),r==null&&(r=t[yi]=iw++);let i=r&vv,o=1<<i;n.data[e+(i>>yv)]|=o}function _s(e,n){let t=Dv(e,n);if(t!==-1)return t;let r=n[U];r.firstCreatePass&&(e.injectorIndex=n.length,dl(r.data,e),dl(n,null),dl(r.blueprint,null));let i=Pu(e,n),o=e.injectorIndex;if(mv(i)){let s=ws(i),a=Es(i,n),c=a[U].data;for(let l=0;l<8;l++)n[o+l]=a[s+l]|c[s+l]}return n[o+8]=i,o}function dl(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Dv(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function Pu(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,i=n;for(;i!==null;){if(r=Ev(i),r===null)return Tr;if(t++,i=i[Vr],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return Tr}function Ol(e,n,t){ow(e,n,t)}function sw(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,i=0;for(;i<r;){let o=t[i];if(Am(o))break;if(o===0)i=i+2;else if(typeof o=="number")for(i++;i<r&&typeof t[i]=="string";)i++;else{if(o===n)return t[i+1];i=i+2}}}return null}function Iv(e,n,t){if(t&z.Optional||e!==void 0)return e;Iu(n,"NodeInjector")}function Cv(e,n,t,r){if(t&z.Optional&&r===void 0&&(r=null),!(t&(z.Self|z.Host))){let i=e[Nr],o=Ve(void 0);try{return i?i.get(n,r,t&z.Optional):Em(n,r,t&z.Optional)}finally{Ve(o)}}return Iv(r,n,t)}function bv(e,n,t,r=z.Default,i){if(e!==null){if(n[P]&2048&&!(r&z.Self)){let s=uw(e,n,t,r,bt);if(s!==bt)return s}let o=wv(e,n,t,r,bt);if(o!==bt)return o}return Cv(n,t,r,i)}function wv(e,n,t,r,i){let o=cw(t);if(typeof o=="function"){if(!lv(n,e,r))return r&z.Host?Iv(i,t,r):Cv(n,t,r,i);try{let s;if(s=o(r),s==null&&!(r&z.Optional))Iu(t);else return s}finally{hv()}}else if(typeof o=="number"){let s=null,a=Dv(e,n),c=Tr,l=r&z.Host?n[Je][Be]:null;for((a===-1||r&z.SkipSelf)&&(c=a===-1?Pu(e,n):n[a+8],c===Tr||!Hg(r,!1)?a=-1:(s=n[U],a=ws(c),n=Es(c,n)));a!==-1;){let d=n[U];if($g(o,a,d.data)){let f=aw(a,n,t,s,r,l);if(f!==bt)return f}c=n[a+8],c!==Tr&&Hg(r,n[U].data[a+8]===l)&&$g(o,a,n)?(s=d,a=ws(c),n=Es(c,n)):a=-1}}return i}function aw(e,n,t,r,i,o){let s=n[U],a=s.data[e+8],c=r==null?qs(a)&&Nl:r!=s&&(a.type&3)!==0,l=i&z.Host&&o===a,d=hs(a,s,t,c,l);return d!==null?Zn(n,s,d,a):bt}function hs(e,n,t,r,i){let o=e.providerIndexes,s=n.data,a=o&1048575,c=e.directiveStart,l=e.directiveEnd,d=o>>20,f=r?a:a+d,p=i?a+d:l;for(let h=f;h<p;h++){let D=s[h];if(h<c&&t===D||h>=c&&D.type===t)return h}if(i){let h=s[c];if(h&&hn(h)&&h.type===t)return c}return null}function Zn(e,n,t,r){let i=e[t],o=n.data;if(Jb(i)){let s=i;s.resolving&&PC(kC(o[t]));let a=Ms(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Ve(s.injectImpl):null,d=lv(e,r,z.Default);try{i=e[t]=s.factory(void 0,o,e,r),n.firstCreatePass&&t>=r.directiveStart&&Kb(t,o[t],n)}finally{l!==null&&Ve(l),Ms(a),s.resolving=!1,hv()}}return i}function cw(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(yi)?e[yi]:void 0;return typeof n=="number"?n>=0?n&vv:lw:n}function $g(e,n,t){let r=1<<e;return!!(t[n+(e>>yv)]&r)}function Hg(e,n){return!(e&z.Self)&&!(e&z.Host&&n)}var Bn=class{constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return bv(this._tNode,this._lView,n,$s(r),t)}};function lw(){return new Bn(Re(),q())}function ke(e){return _i(()=>{let n=e.prototype.constructor,t=n[ms]||kl(n),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[ms]||kl(i);if(o&&o!==t)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function kl(e){return ym(e)?()=>{let n=kl(Pe(e));return n&&n()}:$n(e)}function uw(e,n,t,r,i){let o=e,s=n;for(;o!==null&&s!==null&&s[P]&2048&&!(s[P]&512);){let a=wv(o,s,t,r|z.Self,bt);if(a!==bt)return a;let c=o.parent;if(!c){let l=s[Gm];if(l){let d=l.get(t,bt,r);if(d!==bt)return d}c=Ev(s),s=s[Vr]}o=c}return i}function Ev(e){let n=e[U],t=n.type;return t===2?n.declTNode:t===1?e[Be]:null}function zt(e){return sw(Re(),e)}function zg(e,n=null,t=null,r){let i=Mv(e,n,t,r);return i.resolveInjectorInitializers(),i}function Mv(e,n=null,t=null,r,i=new Set){let o=[t||Ke,hb(e)];return r=r||(typeof e=="object"?void 0:Fe(e)),new Ci(o,n||Gs(),r||null,i)}var le=class e{static{this.THROW_IF_NOT_FOUND=Di}static{this.NULL=new Ds}static create(n,t){if(Array.isArray(n))return zg({name:""},t,n,"");{let r=n.name??"";return zg({name:r},n.parent,n.providers,r)}}static{this.\u0275prov=x({token:e,providedIn:"any",factory:()=>N(Sm)})}static{this.__NG_ELEMENT_ID__=-1}};var dw=new A("");dw.__NG_ELEMENT_ID__=e=>{let n=Re();if(n===null)throw new R(204,!1);if(n.type&2)return n.value;if(e&z.Optional)return null;throw new R(204,!1)};var fw="ngOriginalError";function fl(e){return e[fw]}var _v=!0,Fu=(()=>{class e{static{this.__NG_ELEMENT_ID__=hw}static{this.__NG_ENV_ID__=t=>t}}return e})(),Pl=class extends Fu{constructor(n){super(),this._lView=n}onDestroy(n){return tv(this._lView,n),()=>Pb(this._lView,n)}};function hw(){return new Pl(q())}var Gt=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new ye(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Fl=class extends ae{constructor(n=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=n,zm()&&(this.destroyRef=v(Fu,{optional:!0})??void 0,this.pendingTasks=v(Gt,{optional:!0})??void 0)}emit(n){let t=Q(null);try{super.next(n)}finally{Q(t)}}subscribe(n,t,r){let i=n,o=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;i=c.next?.bind(c),o=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:o,complete:s});return n instanceof de&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{n(t),r!==void 0&&this.pendingTasks?.remove(r)})}}},ee=Fl;function Ss(...e){}function Sv(e){let n,t;function r(){e=Ss;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Gg(e){return queueMicrotask(()=>e()),()=>{e=Ss}}var ju="isAngularZone",Ts=ju+"_ID",pw=0,g=class e{constructor(n){this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ee(!1),this.onMicrotaskEmpty=new ee(!1),this.onStable=new ee(!1),this.onError=new ee(!1);let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:i=!1,scheduleInRootZone:o=_v}=n;if(typeof Zone>"u")throw new R(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!i&&r,s.shouldCoalesceRunChangeDetection=i,s.callbackScheduled=!1,s.scheduleInRootZone=o,vw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ju)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new R(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new R(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,i){let o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,n,gw,Ss,Ss);try{return o.runTask(s,t,r)}finally{o.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},gw={};function Lu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function mw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){Sv(()=>{e.callbackScheduled=!1,jl(e),e.isCheckStableRunning=!0,Lu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),jl(e)}function vw(e){let n=()=>{mw(e)},t=pw++;e._inner=e._inner.fork({name:"angular",properties:{[ju]:!0,[Ts]:t,[Ts+t]:!0},onInvokeTask:(r,i,o,s,a,c)=>{if(yw(c))return r.invokeTask(o,s,a,c);try{return Wg(e),r.invokeTask(o,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),qg(e)}},onInvoke:(r,i,o,s,a,c,l)=>{try{return Wg(e),r.invoke(o,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Dw(c)&&n(),qg(e)}},onHasTask:(r,i,o,s)=>{r.hasTask(o,s),i===o&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,jl(e),Lu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,i,o,s)=>(r.handleError(o,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function jl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Wg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function qg(e){e._nesting--,Lu(e)}var Ll=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ee,this.onMicrotaskEmpty=new ee,this.onStable=new ee,this.onError=new ee}run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,i){return n.apply(t,r)}};function yw(e){return Tv(e,"__ignore_ng_zone__")}function Dw(e){return Tv(e,"__scheduler_tick__")}function Tv(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var Bt=class{constructor(){this._console=console}handleError(n){let t=this._findOriginalError(n);this._console.error("ERROR",n),t&&this._console.error("ORIGINAL ERROR",t)}_findOriginalError(n){let t=n&&fl(n);for(;t&&fl(t);)t=fl(t);return t||null}},Iw=new A("",{providedIn:"root",factory:()=>{let e=v(g),n=v(Bt);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function Cw(){return Br(Re(),q())}function Br(e,n){return new m(rt(e,n))}var m=(()=>{class e{constructor(t){this.nativeElement=t}static{this.__NG_ELEMENT_ID__=Cw}}return e})();function bw(e){return e instanceof m?e.nativeElement:e}function ww(){return this._results[Symbol.iterator]()}var Vl=class e{get changes(){return this._changes??=new ee}constructor(n=!1){this._emitDistinctChangesOnly=n,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let t=e.prototype;t[Symbol.iterator]||(t[Symbol.iterator]=ww)}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=WC(n);(this._changesDetected=!GC(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function xv(e){return(e.flags&128)===128}var Av=new Map,Ew=0;function Mw(){return Ew++}function _w(e){Av.set(e[Ws],e)}function Bl(e){Av.delete(e[Ws])}var Zg="__ngContext__";function pn(e,n){dn(n)?(e[Zg]=n[Ws],_w(n)):e[Zg]=n}function Rv(e){return Ov(e[wi])}function Nv(e){return Ov(e[ut])}function Ov(e){for(;e!==null&&!Ht(e);)e=e[ut];return e}var Ul;function kv(e){Ul=e}function Pv(){if(Ul!==void 0)return Ul;if(typeof document<"u")return document;throw new R(210,!1)}var Vu=new A("",{providedIn:"root",factory:()=>Sw}),Sw="ng",Bu=new A(""),St=new A("",{providedIn:"platform",factory:()=>"unknown"});var Uu=new A("",{providedIn:"root",factory:()=>Pv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Tw="h",xw="b";var Aw=()=>null;function $u(e,n,t=!1){return Aw(e,n,t)}var Fv=!1,Rw=new A("",{providedIn:"root",factory:()=>Fv});var is;function Nw(){if(is===void 0&&(is=null,jt.trustedTypes))try{is=jt.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return is}function na(e){return Nw()?.createHTML(e)||e}var os;function jv(){if(os===void 0&&(os=null,jt.trustedTypes))try{os=jt.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return os}function Qg(e){return jv()?.createHTML(e)||e}function Yg(e){return jv()?.createScriptURL(e)||e}var xs=class{constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${mm})`}};function Ur(e){return e instanceof xs?e.changingThisBreaksApplicationSecurity:e}function ra(e,n){let t=Ow(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${mm})`)}return t===n}function Ow(e){return e instanceof xs&&e.getTypeName()||null}function kw(e){let n=new Hl(e);return Pw()?new $l(n):n}var $l=class{constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{let t=new window.DOMParser().parseFromString(na(n),"text/html").body;return t===null?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}},Hl=class{constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){let t=this.inertDocument.createElement("template");return t.innerHTML=na(n),t}};function Pw(){try{return!!new window.DOMParser().parseFromString(na(""),"text/html")}catch{return!1}}var Fw=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Hu(e){return e=String(e),e.match(Fw)?e:"unsafe:"+e}function Wt(e){let n={};for(let t of e.split(","))n[t]=!0;return n}function xi(...e){let n={};for(let t of e)for(let r in t)t.hasOwnProperty(r)&&(n[r]=!0);return n}var Lv=Wt("area,br,col,hr,img,wbr"),Vv=Wt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Bv=Wt("rp,rt"),jw=xi(Bv,Vv),Lw=xi(Vv,Wt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Vw=xi(Bv,Wt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Kg=xi(Lv,Lw,Vw,jw),Uv=Wt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Bw=Wt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Uw=Wt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),$w=xi(Uv,Bw,Uw),Hw=Wt("script,style,template"),zl=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(n){let t=n.firstChild,r=!0,i=[];for(;t;){if(t.nodeType===Node.ELEMENT_NODE?r=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,r&&t.firstChild){i.push(t),t=Ww(t);continue}for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let o=Gw(t);if(o){t=o;break}t=i.pop()}}return this.buf.join("")}startElement(n){let t=Xg(n).toLowerCase();if(!Kg.hasOwnProperty(t))return this.sanitizedSomething=!0,!Hw.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);let r=n.attributes;for(let i=0;i<r.length;i++){let o=r.item(i),s=o.name,a=s.toLowerCase();if(!$w.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=o.value;Uv[a]&&(c=Hu(c)),this.buf.push(" ",s,'="',Jg(c),'"')}return this.buf.push(">"),!0}endElement(n){let t=Xg(n).toLowerCase();Kg.hasOwnProperty(t)&&!Lv.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(Jg(n))}};function zw(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Gw(e){let n=e.nextSibling;if(n&&e!==n.previousSibling)throw $v(n);return n}function Ww(e){let n=e.firstChild;if(n&&zw(e,n))throw $v(n);return n}function Xg(e){let n=e.nodeName;return typeof n=="string"?n:"FORM"}function $v(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var qw=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Zw=/([^\#-~ |!])/g;function Jg(e){return e.replace(/&/g,"&amp;").replace(qw,function(n){let t=n.charCodeAt(0),r=n.charCodeAt(1);return"&#"+((t-55296)*1024+(r-56320)+65536)+";"}).replace(Zw,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ss;function Hv(e,n){let t=null;try{ss=ss||kw(e);let r=n?String(n):"";t=ss.getInertBodyElement(r);let i=5,o=r;do{if(i===0)throw new Error("Failed to sanitize html because the input is unstable");i--,r=o,o=t.innerHTML,t=ss.getInertBodyElement(r)}while(r!==o);let a=new zl().sanitizeChildren(em(t)||t);return na(a)}finally{if(t){let r=em(t)||t;for(;r.firstChild;)r.firstChild.remove()}}}function em(e){return"content"in e&&Qw(e)?e.content:null}function Qw(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Ai=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ai||{});function xB(e){let n=zu();return n?Qg(n.sanitize(Ai.HTML,e)||""):ra(e,"HTML")?Qg(Ur(e)):Hv(Pv(),Un(e))}function Yw(e){let n=zu();return n?n.sanitize(Ai.URL,e)||"":ra(e,"URL")?Ur(e):Hu(Un(e))}function Kw(e){let n=zu();if(n)return Yg(n.sanitize(Ai.RESOURCE_URL,e)||"");if(ra(e,"ResourceURL"))return Yg(Ur(e));throw new R(904,!1)}function Xw(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?Kw:Yw}function zv(e,n,t){return Xw(n,t)(e)}function zu(){let e=q();return e&&e[Et].sanitizer}var Jw=/^>|^->|<!--|-->|--!>|<!-$/g,eE=/(<|>)/g,tE="\u200B$1\u200B";function nE(e){return e.replace(Jw,n=>n.replace(eE,tE))}function Gv(e){return e instanceof Function?e():e}function rE(e){return(e??v(le)).get(St)==="browser"}var Ut=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ut||{}),iE;function Gu(e,n){return iE(e,n)}function Mr(e,n,t,r,i){if(r!=null){let o,s=!1;Ht(r)?o=r:dn(r)&&(s=!0,r=r[$t]);let a=Mt(r);e===0&&t!==null?i==null?Kv(n,t,a):As(n,t,a,i||null,!0):e===1&&t!==null?As(n,t,a,i||null,!0):e===2?yE(n,a,s):e===3&&n.destroyNode(a),o!=null&&IE(n,e,o,t,i)}}function oE(e,n){return e.createText(n)}function sE(e,n,t){e.setValue(n,t)}function aE(e,n){return e.createComment(nE(n))}function Wv(e,n,t){return e.createElement(n,t)}function cE(e,n){qv(e,n),n[$t]=null,n[Be]=null}function lE(e,n,t,r,i,o){r[$t]=i,r[Be]=n,oa(e,r,t,1,i,o)}function qv(e,n){n[Et].changeDetectionScheduler?.notify(9),oa(e,n,n[he],2,null,null)}function uE(e){let n=e[wi];if(!n)return hl(e[U],e);for(;n;){let t=null;if(dn(n))t=n[wi];else{let r=n[ze];r&&(t=r)}if(!t){for(;n&&!n[ut]&&n!==e;)dn(n)&&hl(n[U],n),n=n[Oe];n===null&&(n=e),dn(n)&&hl(n[U],n),t=n&&n[ut]}n=t}}function dE(e,n,t,r){let i=ze+r,o=t.length;r>0&&(t[i-1][ut]=n),r<o-ze?(n[ut]=t[i],_m(t,ze+r,n)):(t.push(n),n[ut]=null),n[Oe]=t;let s=n[Hn];s!==null&&t!==s&&Zv(s,n);let a=n[Vt];a!==null&&a.insertView(e),xl(n),n[P]|=128}function Zv(e,n){let t=e[Or],r=n[Oe];if(dn(r))e[P]|=bs.HasTransplantedViews;else{let i=r[Oe][Je];n[Je]!==i&&(e[P]|=bs.HasTransplantedViews)}t===null?e[Or]=[n]:t.push(n)}function Wu(e,n){let t=e[Or],r=t.indexOf(n);t.splice(r,1)}function Gl(e,n){if(e.length<=ze)return;let t=ze+n,r=e[t];if(r){let i=r[Hn];i!==null&&i!==e&&Wu(i,r),n>0&&(e[t-1][ut]=r[ut]);let o=ys(e,ze+n);cE(r[U],r);let s=o[Vt];s!==null&&s.detachView(o[U]),r[Oe]=null,r[ut]=null,r[P]&=-129}return r}function Qv(e,n){if(!(n[P]&256)){let t=n[he];t.destroyNode&&oa(e,n,t,3,null,null),uE(n)}}function hl(e,n){if(n[P]&256)return;let t=Q(null);try{n[P]&=-129,n[P]|=256,n[Xe]&&il(n[Xe]),hE(e,n),fE(e,n),n[U].type===1&&n[he].destroy();let r=n[Hn];if(r!==null&&Ht(n[Oe])){r!==n[Oe]&&Wu(r,n);let i=n[Vt];i!==null&&i.detachView(e)}Bl(n)}finally{Q(t)}}function fE(e,n){let t=e.cleanup,r=n[Is];if(t!==null)for(let o=0;o<t.length-1;o+=2)if(typeof t[o]=="string"){let s=t[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else{let s=r[t[o+1]];t[o].call(s)}r!==null&&(n[Is]=null);let i=n[un];if(i!==null){n[un]=null;for(let o=0;o<i.length;o++){let s=i[o];s()}}}function hE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let i=n[t[r]];if(!(i instanceof qn)){let o=t[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){let a=i[o[s]],c=o[s+1];Ct(4,a,c);try{c.call(a)}finally{Ct(5,a,c)}}else{Ct(4,i,o);try{o.call(i)}finally{Ct(5,i,o)}}}}}function Yv(e,n,t){return pE(e,n.parent,t)}function pE(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[$t];{let{componentOffset:i}=r;if(i>-1){let{encapsulation:o}=e.data[r.directiveStart+i];if(o===wt.None||o===wt.Emulated)return null}return rt(r,t)}}function As(e,n,t,r,i){e.insertBefore(n,t,r,i)}function Kv(e,n,t){e.appendChild(n,t)}function tm(e,n,t,r,i){r!==null?As(e,n,t,r,i):Kv(e,n,t)}function Xv(e,n){return e.parentNode(n)}function gE(e,n){return e.nextSibling(n)}function Jv(e,n,t){return vE(e,n,t)}function mE(e,n,t){return e.type&40?rt(e,t):null}var vE=mE,nm;function ia(e,n,t,r){let i=Yv(e,r,n),o=n[he],s=r.parent||n[Be],a=Jv(s,r,n);if(i!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)tm(o,i,t[c],a,!1);else tm(o,i,t,a,!1);nm!==void 0&&nm(o,r,n,t,i)}function vi(e,n){if(n!==null){let t=n.type;if(t&3)return rt(n,e);if(t&4)return Wl(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return vi(e,r);{let i=e[n.index];return Ht(i)?Wl(-1,i):Mt(i)}}else{if(t&128)return vi(e,n.next);if(t&32)return Gu(n,e)()||Mt(e[n.index]);{let r=ey(e,n);if(r!==null){if(Array.isArray(r))return r[0];let i=Gn(e[Je]);return vi(i,r)}else return vi(e,n.next)}}}return null}function ey(e,n){if(n!==null){let r=e[Je][Be],i=n.projection;return r.projection[i]}return null}function Wl(e,n){let t=ze+e+1;if(t<n.length){let r=n[t],i=r[U].firstChild;if(i!==null)return vi(r,i)}return n[zn]}function yE(e,n,t){e.removeChild(null,n,t)}function qu(e,n,t,r,i,o,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&pn(Mt(a),r),t.flags|=2),(t.flags&32)!==32)if(c&8)qu(e,n,t.child,r,i,o,!1),Mr(n,e,i,a,o);else if(c&32){let l=Gu(t,r),d;for(;d=l();)Mr(n,e,i,d,o);Mr(n,e,i,a,o)}else c&16?ty(e,n,r,t,i,o):Mr(n,e,i,a,o);t=s?t.projectionNext:t.next}}function oa(e,n,t,r,i,o){qu(t,r,e.firstChild,n,i,o,!1)}function DE(e,n,t){let r=n[he],i=Yv(e,t,n),o=t.parent||n[Be],s=Jv(o,t,n);ty(r,0,n,t,i,s)}function ty(e,n,t,r,i,o){let s=t[Je],c=s[Be].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];Mr(n,e,i,d,o)}else{let l=c,d=s[Oe];xv(r)&&(l.flags|=128),qu(e,n,l,d,i,o,!0)}}function IE(e,n,t,r,i){let o=t[zn],s=Mt(t);o!==s&&Mr(n,e,r,o,i);for(let a=ze;a<t.length;a++){let c=t[a];oa(c[U],c,e,n,r,o)}}function CE(e,n,t,r,i){if(n)i?e.addClass(t,r):e.removeClass(t,r);else{let o=r.indexOf("-")===-1?void 0:Ut.DashCase;i==null?e.removeStyle(t,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=Ut.Important),e.setStyle(t,r,i,o))}}function bE(e,n,t){e.setAttribute(n,"style",t)}function ny(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function ry(e,n,t){let{mergedAttrs:r,classes:i,styles:o}=t;r!==null&&wl(e,n,r),i!==null&&ny(e,n,i),o!==null&&bE(e,n,o)}var pt={};function sa(e=1){iy(pe(),q(),er()+e,!1)}function iy(e,n,t,r){if(!r)if((n[P]&3)===3){let o=e.preOrderCheckHooks;o!==null&&ds(n,o,t)}else{let o=e.preOrderHooks;o!==null&&fs(n,o,0,t)}Wn(t)}function u(e,n=z.Default){let t=q();if(t===null)return N(e,n);let r=Re();return bv(r,t,Pe(e),n)}function oy(){let e="invalid";throw new Error(e)}function sy(e,n,t,r,i,o){let s=Q(null);try{let a=null;i&fn.SignalBased&&(a=n[r][Ft]),a!==null&&a.transformFn!==void 0&&(o=a.transformFn(o)),i&fn.HasDecoratorInputTransform&&(o=e.inputTransforms[r].call(n,o)),e.setInput!==null?e.setInput(n,a,o,t,r):qm(n,a,r,o)}finally{Q(s)}}function wE(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let i=t[r];if(i<0)Wn(~i);else{let o=i,s=t[++r],a=t[++r];Gb(s,o);let c=n[o];a(2,c)}}}finally{Wn(-1)}}function aa(e,n,t,r,i,o,s,a,c,l,d){let f=n.blueprint.slice();return f[$t]=i,f[P]=r|4|128|8|64,(l!==null||e&&e[P]&2048)&&(f[P]|=2048),Jm(f),f[Oe]=f[Vr]=e,f[dt]=t,f[Et]=s||e&&e[Et],f[he]=a||e&&e[he],f[Nr]=c||e&&e[Nr]||null,f[Be]=o,f[Ws]=Mw(),f[bi]=d,f[Gm]=l,f[Je]=n.type==2?e[Je]:f,f}function $r(e,n,t,r,i){let o=e.data[n];if(o===null)o=EE(e,n,t,r,i),zb()&&(o.flags|=32);else if(o.type&64){o.type=t,o.value=r,o.attrs=i;let s=Ub();o.injectorIndex=s===null?-1:s.injectorIndex}return Jn(o,!0),o}function EE(e,n,t,r,i){let o=ov(),s=xu(),a=s?o:o&&o.parent,c=e.data[n]=AE(e,a,t,n,r,i);return e.firstChild===null&&(e.firstChild=c),o!==null&&(s?o.child==null&&c.parent!==null&&(o.child=c):o.next===null&&(o.next=c,c.prev=o)),c}function ay(e,n,t,r){if(t===0)return-1;let i=n.length;for(let o=0;o<t;o++)n.push(r),e.blueprint.push(r),e.data.push(null);return i}function cy(e,n,t,r,i){let o=er(),s=r&2;try{Wn(-1),s&&n.length>Ge&&iy(e,n,Ge,!1),Ct(s?2:0,i),t(r,i)}finally{Wn(o),Ct(s?3:1,i)}}function Zu(e,n,t){if(Mu(n)){let r=Q(null);try{let i=n.directiveStart,o=n.directiveEnd;for(let s=i;s<o;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{Q(r)}}}function Qu(e,n,t){rv()&&(FE(e,n,t,rt(t,n)),(t.flags&64)===64&&dy(e,n,t))}function Yu(e,n,t=rt){let r=n.localNames;if(r!==null){let i=n.index+1;for(let o=0;o<r.length;o+=2){let s=r[o+1],a=s===-1?t(n,e):e[s];e[i++]=a}}}function ly(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=Ku(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function Ku(e,n,t,r,i,o,s,a,c,l,d){let f=Ge+r,p=f+i,h=ME(f,p),D=typeof l=="function"?l():l;return h[U]={type:e,blueprint:h,template:t,queries:null,viewQuery:a,declTNode:n,data:h.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:D,incompleteFirstPass:!1,ssrId:d}}function ME(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:pt);return t}function _E(e,n,t,r){let o=r.get(Rw,Fv)||t===wt.ShadowDom,s=e.selectRootElement(n,o);return SE(s),s}function SE(e){TE(e)}var TE=()=>null;function xE(e,n,t,r){let i=py(n);i.push(t),e.firstCreatePass&&gy(e).push(r,i.length-1)}function AE(e,n,t,r,i,o){let s=n?n.injectorIndex:-1,a=0;return iv()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function rm(e,n,t,r,i){for(let o in n){if(!n.hasOwnProperty(o))continue;let s=n[o];if(s===void 0)continue;r??={};let a,c=fn.None;Array.isArray(s)?(a=s[0],c=s[1]):a=s;let l=o;if(i!==null){if(!i.hasOwnProperty(o))continue;l=i[o]}e===0?im(r,t,l,a,c):im(r,t,l,a)}return r}function im(e,n,t,r,i){let o;e.hasOwnProperty(t)?(o=e[t]).push(n,r):o=e[t]=[n,r],i!==void 0&&o.push(i)}function RE(e,n,t){let r=n.directiveStart,i=n.directiveEnd,o=e.data,s=n.attrs,a=[],c=null,l=null;for(let d=r;d<i;d++){let f=o[d],p=t?t.get(f):null,h=p?p.inputs:null,D=p?p.outputs:null;c=rm(0,f.inputs,d,c,h),l=rm(1,f.outputs,d,l,D);let S=c!==null&&s!==null&&!wu(n)?qE(c,d,s):null;a.push(S)}c!==null&&(c.hasOwnProperty("class")&&(n.flags|=8),c.hasOwnProperty("style")&&(n.flags|=16)),n.initialInputs=a,n.inputs=c,n.outputs=l}function NE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Xu(e,n,t,r,i,o,s,a){let c=rt(n,t),l=n.inputs,d;!a&&l!=null&&(d=l[r])?(ed(e,t,d,r,i),qs(n)&&OE(t,n.index)):n.type&3?(r=NE(r),i=s!=null?s(i,n.value||"",r):i,o.setProperty(c,r,i)):n.type&12}function OE(e,n){let t=mn(n,e);t[P]&16||(t[P]|=64)}function Ju(e,n,t,r){if(rv()){let i=r===null?null:{"":-1},o=LE(e,t),s,a;o===null?s=a=null:[s,a]=o,s!==null&&uy(e,n,t,s,i,a),i&&VE(t,r,i)}t.mergedAttrs=Ii(t.mergedAttrs,t.attrs)}function uy(e,n,t,r,i,o){for(let l=0;l<r.length;l++)Ol(_s(t,n),e,r[l].type);UE(t,e.data.length,r.length);for(let l=0;l<r.length;l++){let d=r[l];d.providersResolver&&d.providersResolver(d)}let s=!1,a=!1,c=ay(e,n,r.length,null);for(let l=0;l<r.length;l++){let d=r[l];t.mergedAttrs=Ii(t.mergedAttrs,d.hostAttrs),$E(e,t,n,c,d),BE(c,d,i),d.contentQueries!==null&&(t.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(t.flags|=64);let f=d.type.prototype;!s&&(f.ngOnChanges||f.ngOnInit||f.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),s=!0),!a&&(f.ngOnChanges||f.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),a=!0),c++}RE(e,t,o)}function kE(e,n,t,r,i){let o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;PE(s)!=a&&s.push(a),s.push(t,r,o)}}function PE(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function FE(e,n,t,r){let i=t.directiveStart,o=t.directiveEnd;qs(t)&&HE(n,t,e.data[i+t.componentOffset]),e.firstCreatePass||_s(t,n),pn(r,n);let s=t.initialInputs;for(let a=i;a<o;a++){let c=e.data[a],l=Zn(n,e,a,t);if(pn(l,n),s!==null&&WE(n,a-i,l,c,t,s),hn(c)){let d=mn(t.index,n);d[dt]=Zn(n,e,a,t)}}}function dy(e,n,t){let r=t.directiveStart,i=t.directiveEnd,o=t.index,s=Wb();try{Wn(o);for(let a=r;a<i;a++){let c=e.data[a],l=n[a];Al(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&jE(c,l)}}finally{Wn(-1),Al(s)}}function jE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function LE(e,n){let t=e.directiveRegistry,r=null,i=null;if(t)for(let o=0;o<t.length;o++){let s=t[o];if(Nm(n,s.selectors,!1))if(r||(r=[]),hn(s))if(s.findHostDirectiveDefs!==null){let a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s);let c=a.length;ql(e,n,c)}else r.unshift(s),ql(e,n,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return r===null?null:[r,i]}function ql(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function VE(e,n,t){if(n){let r=e.localNames=[];for(let i=0;i<n.length;i+=2){let o=t[n[i+1]];if(o==null)throw new R(-301,!1);r.push(n[i],o)}}}function BE(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;hn(n)&&(t[""]=e)}}function UE(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function $E(e,n,t,r,i){e.data[r]=i;let o=i.factory||(i.factory=$n(i.type,!0)),s=new qn(o,hn(i),u);e.blueprint[r]=s,t[r]=s,kE(e,n,r,ay(e,t,i.hostVars,pt),i)}function HE(e,n,t){let r=rt(n,e),i=ly(t),o=e[Et].rendererFactory,s=16;t.signals?s=4096:t.onPush&&(s=64);let a=ca(e,aa(e,i,null,s,r,n,null,o.createRenderer(r,t),null,null,null));e[n.index]=a}function zE(e,n,t,r,i,o){let s=rt(e,n);GE(n[he],s,o,e.value,t,r,i)}function GE(e,n,t,r,i,o,s){if(o==null)e.removeAttribute(n,i,t);else{let a=s==null?Un(o):s(o,r||"",i);e.setAttribute(n,i,a,t)}}function WE(e,n,t,r,i,o){let s=o[n];if(s!==null)for(let a=0;a<s.length;){let c=s[a++],l=s[a++],d=s[a++],f=s[a++];sy(r,t,c,l,d,f)}}function qE(e,n,t){let r=null,i=0;for(;i<t.length;){let o=t[i];if(o===0){i+=4;continue}else if(o===5){i+=2;continue}if(typeof o=="number")break;if(e.hasOwnProperty(o)){r===null&&(r=[]);let s=e[o];for(let a=0;a<s.length;a+=3)if(s[a]===n){r.push(o,s[a+1],s[a+2],t[i+1]);break}}i+=2}return r}function fy(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function hy(e,n){let t=e.contentQueries;if(t!==null){let r=Q(null);try{for(let i=0;i<t.length;i+=2){let o=t[i],s=t[i+1];if(s!==-1){let a=e.data[s];Nu(o),a.contentQueries(2,n[s],s)}}}finally{Q(r)}}}function ca(e,n){return e[wi]?e[Lg][ut]=n:e[wi]=n,e[Lg]=n,n}function Zl(e,n,t){Nu(0);let r=Q(null);try{n(e,t)}finally{Q(r)}}function py(e){return e[Is]??=[]}function gy(e){return e.cleanup??=[]}function my(e,n){let t=e[Nr],r=t?t.get(Bt,null):null;r&&r.handleError(n)}function ed(e,n,t,r,i){for(let o=0;o<t.length;){let s=t[o++],a=t[o++],c=t[o++],l=n[s],d=e.data[s];sy(d,l,r,a,c,i)}}function vy(e,n,t){let r=Km(n,e);sE(e[he],r,t)}function ZE(e,n){let t=mn(n,e),r=t[U];QE(r,t);let i=t[$t];i!==null&&t[bi]===null&&(t[bi]=$u(i,t[Nr])),td(r,t,t[dt])}function QE(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function td(e,n,t){Ou(n);try{let r=e.viewQuery;r!==null&&Zl(1,r,t);let i=e.template;i!==null&&cy(e,n,i,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[Vt]?.finishViewCreation(e),e.staticContentQueries&&hy(e,n),e.staticViewQueries&&Zl(2,e.viewQuery,t);let o=e.components;o!==null&&YE(n,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[P]&=-5,ku()}}function YE(e,n){for(let t=0;t<n.length;t++)ZE(e,n[t])}function yy(e,n,t,r){let i=Q(null);try{let o=n.tView,a=e[P]&4096?4096:16,c=aa(e,o,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[n.index];c[Hn]=l;let d=e[Vt];return d!==null&&(c[Vt]=d.createEmbeddedView(o)),td(o,c,t),c}finally{Q(i)}}function Ql(e,n){return!n||n.firstChild===null||xv(e)}function Dy(e,n,t,r=!0){let i=n[U];if(dE(i,n,e,t),r){let s=Wl(t,e),a=n[he],c=Xv(a,e[zn]);c!==null&&lE(i,e[Be],a,n,c,s)}let o=n[bi];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function Rs(e,n,t,r,i=!1){for(;t!==null;){if(t.type===128){t=i?t.projectionNext:t.next;continue}let o=n[t.index];o!==null&&r.push(Mt(o)),Ht(o)&&KE(o,r);let s=t.type;if(s&8)Rs(e,n,t.child,r);else if(s&32){let a=Gu(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=ey(n,t);if(Array.isArray(a))r.push(...a);else{let c=Gn(n[Je]);Rs(c[U],c,a,r,!0)}}t=i?t.projectionNext:t.next}return r}function KE(e,n){for(let t=ze;t<e.length;t++){let r=e[t],i=r[U].firstChild;i!==null&&Rs(r[U],r,i,n)}e[zn]!==e[$t]&&n.push(e[zn])}var Iy=[];function XE(e){return e[Xe]??JE(e)}function JE(e){let n=Iy.pop()??Object.create(tM);return n.lView=e,n}function eM(e){e.lView[Xe]!==e&&(e.lView=null,Iy.push(e))}var tM=V(b({},mi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Ys(e.lView)},consumerOnSignalRead(){this.lView[Xe]=this}});function nM(e){let n=e[Xe]??Object.create(rM);return n.lView=e,n}var rM=V(b({},mi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let n=Gn(e.lView);for(;n&&!Cy(n[U]);)n=Gn(n);n&&ev(n)},consumerOnSignalRead(){this.lView[Xe]=this}});function Cy(e){return e.type!==2}var iM=100;function by(e,n=!0,t=0){let r=e[Et],i=r.rendererFactory,o=!1;o||i.begin?.();try{oM(e,t)}catch(s){throw n&&my(e,s),s}finally{o||(i.end?.(),r.inlineEffectRunner?.flush())}}function oM(e,n){let t=sv();try{Bg(!0),Yl(e,n);let r=0;for(;Qs(e);){if(r===iM)throw new R(103,!1);r++,Yl(e,1)}}finally{Bg(t)}}function sM(e,n,t,r){let i=n[P];if((i&256)===256)return;let o=!1,s=!1;!o&&n[Et].inlineEffectRunner?.flush(),Ou(n);let a=!0,c=null,l=null;o||(Cy(e)?(l=XE(n),c=es(l)):pg()===null?(a=!1,l=nM(n),c=es(l)):n[Xe]&&(il(n[Xe]),n[Xe]=null));try{Jm(n),Hb(e.bindingStartIndex),t!==null&&cy(e,n,t,2,r);let d=(i&3)===3;if(!o)if(d){let h=e.preOrderCheckHooks;h!==null&&ds(n,h,null)}else{let h=e.preOrderHooks;h!==null&&fs(n,h,0,null),ll(n,0)}if(s||aM(n),wy(n,0),e.contentQueries!==null&&hy(e,n),!o)if(d){let h=e.contentCheckHooks;h!==null&&ds(n,h)}else{let h=e.contentHooks;h!==null&&fs(n,h,1),ll(n,1)}wE(e,n);let f=e.components;f!==null&&My(n,f,0);let p=e.viewQuery;if(p!==null&&Zl(2,p,r),!o)if(d){let h=e.viewCheckHooks;h!==null&&ds(n,h)}else{let h=e.viewHooks;h!==null&&fs(n,h,2),ll(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[cl]){for(let h of n[cl])h();n[cl]=null}o||(n[P]&=-73)}catch(d){throw o||Ys(n),d}finally{l!==null&&(nl(l,c),a&&eM(l)),ku()}}function wy(e,n){for(let t=Rv(e);t!==null;t=Nv(t))for(let r=ze;r<t.length;r++){let i=t[r];Ey(i,n)}}function aM(e){for(let n=Rv(e);n!==null;n=Nv(n)){if(!(n[P]&bs.HasTransplantedViews))continue;let t=n[Or];for(let r=0;r<t.length;r++){let i=t[r];ev(i)}}}function cM(e,n,t){let r=mn(n,e);Ey(r,t)}function Ey(e,n){_u(e)&&Yl(e,n)}function Yl(e,n){let r=e[U],i=e[P],o=e[Xe],s=!!(n===0&&i&16);if(s||=!!(i&64&&n===0),s||=!!(i&1024),s||=!!(o?.dirty&&rl(o)),s||=!1,o&&(o.dirty=!1),e[P]&=-9217,s)sM(r,e,r.template,e[dt]);else if(i&8192){wy(e,1);let a=r.components;a!==null&&My(e,a,1)}}function My(e,n,t){for(let r=0;r<n.length;r++)cM(e,n[r],t)}function nd(e,n){let t=sv()?64:1088;for(e[Et].changeDetectionScheduler?.notify(n);e;){e[P]|=t;let r=Gn(e);if(Sl(e)&&!r)return e;e=r}return null}var Qn=class{get rootNodes(){let n=this._lView,t=n[U];return Rs(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[dt]}set context(n){this._lView[dt]=n}get destroyed(){return(this._lView[P]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[Oe];if(Ht(n)){let t=n[Cs],r=t?t.indexOf(this):-1;r>-1&&(Gl(n,r),ys(t,r))}this._attachedToViewContainer=!1}Qv(this._lView[U],this._lView)}onDestroy(n){tv(this._lView,n)}markForCheck(){nd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[P]&=-129}reattach(){xl(this._lView),this._lView[P]|=128}detectChanges(){this._lView[P]|=1024,by(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new R(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=Sl(this._lView),t=this._lView[Hn];t!==null&&!n&&Wu(t,this._lView),qv(this._lView[U],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new R(902,!1);this._appRef=n;let t=Sl(this._lView),r=this._lView[Hn];r!==null&&!t&&Zv(r,this._lView),xl(this._lView)}},_t=(()=>{class e{static{this.__NG_ELEMENT_ID__=dM}}return e})(),lM=_t,uM=class extends lM{constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){let i=yy(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new Qn(i)}};function dM(){return rd(Re(),q())}function rd(e,n){return e.type&4?new uM(n,e,Br(e,n)):null}var RB=new RegExp(`^(\\d+)*(${xw}|${Tw})*(.*)`);var fM=()=>null;function Kl(e,n){return fM(e,n)}var Pr=class{},id=new A("",{providedIn:"root",factory:()=>!1});var _y=new A(""),Sy=new A(""),Xl=class{},Ns=class{};function hM(e){let n=Error(`No component factory found for ${Fe(e)}.`);return n[pM]=e,n}var pM="ngComponent";var Jl=class{resolveComponentFactory(n){throw hM(n)}},Fr=class{static{this.NULL=new Jl}},jr=class{},vn=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>gM()}}return e})();function gM(){let e=q(),n=Re(),t=mn(n.index,e);return(dn(t)?t:e)[he]}var mM=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>null})}}return e})();function Os(e,n,t){let r=t?e.styles:null,i=t?e.classes:null,o=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")o=a;else if(o==1)i=Tg(i,a);else if(o==2){let c=a,l=n[++s];r=Tg(r,c+": "+l+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=i:e.classesWithoutHost=i}var ks=class extends Fr{constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=Lt(n);return new Yn(t,this.ngModule)}};function om(e,n){let t=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let i=e[r];if(i===void 0)continue;let o=Array.isArray(i),s=o?i[0]:i,a=o?i[1]:fn.None;n?t.push({propName:s,templateName:r,isSignal:(a&fn.SignalBased)!==0}):t.push({propName:s,templateName:r})}return t}function vM(e){let n=e.toLowerCase();return n==="svg"?xb:n==="math"?Ab:null}var Yn=class extends Ns{get inputs(){let n=this.componentDef,t=n.inputTransforms,r=om(n.inputs,!0);if(t!==null)for(let i of r)t.hasOwnProperty(i.propName)&&(i.transform=t[i.propName]);return r}get outputs(){return om(this.componentDef.outputs,!1)}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=cb(n.selectors),this.ngContentSelectors=n.ngContentSelectors?n.ngContentSelectors:[],this.isBoundToModule=!!t}create(n,t,r,i){let o=Q(null);try{i=i||this.ngModule;let s=i instanceof ue?i:i?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Rl(n,s):n,c=a.get(jr,null);if(c===null)throw new R(407,!1);let l=a.get(mM,null),d=a.get(Pr,null),f={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,changeDetectionScheduler:d},p=c.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",D=r?_E(p,r,this.componentDef.encapsulation,a):Wv(p,h,vM(h)),S=512;this.componentDef.signals?S|=4096:this.componentDef.onPush||(S|=16);let T=null;D!==null&&(T=$u(D,a,!0));let F=Ku(0,null,null,1,0,null,null,null,null,null,null),re=aa(null,F,null,S,null,null,f,p,a,null,T);Ou(re);let Z,me,Te=null;try{let Ce=this.componentDef,Nt,Dc=null;Ce.findHostDirectiveDefs?(Nt=[],Dc=new Map,Ce.findHostDirectiveDefs(Ce,Nt,Dc),Nt.push(Ce)):Nt=[Ce];let L0=yM(re,D);Te=DM(L0,D,Ce,Nt,re,f,p),me=Xm(F,Ge),D&&bM(p,Ce,D,r),t!==void 0&&wM(me,this.ngContentSelectors,t),Z=CM(Te,Ce,Nt,Dc,re,[EM]),td(F,re,null)}catch(Ce){throw Te!==null&&Bl(Te),Bl(re),Ce}finally{ku()}return new eu(this.componentType,Z,Br(me,re),re,me)}finally{Q(o)}}},eu=class extends Xl{constructor(n,t,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=t,this.hostView=this.changeDetectorRef=new Qn(i,void 0,!1),this.componentType=n}setInput(n,t){let r=this._tNode.inputs,i;if(r!==null&&(i=r[n])){if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView;ed(o[U],o,i,n,t),this.previousInputValues.set(n,t);let s=mn(this._tNode.index,o);nd(s,1)}}get injector(){return new Bn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function yM(e,n){let t=e[U],r=Ge;return e[r]=n,$r(t,r,2,"#host",null)}function DM(e,n,t,r,i,o,s){let a=i[U];IM(r,e,n,s);let c=null;n!==null&&(c=$u(n,i[Nr]));let l=o.rendererFactory.createRenderer(n,t),d=16;t.signals?d=4096:t.onPush&&(d=64);let f=aa(i,ly(t),null,d,i[e.index],e,o,l,null,null,c);return a.firstCreatePass&&ql(a,e,r.length-1),ca(i,f),i[e.index]=f}function IM(e,n,t,r){for(let i of e)n.mergedAttrs=Ii(n.mergedAttrs,i.hostAttrs);n.mergedAttrs!==null&&(Os(n,n.mergedAttrs,!0),t!==null&&ry(r,t,n))}function CM(e,n,t,r,i,o){let s=Re(),a=i[U],c=rt(s,i);uy(a,i,s,t,null,r);for(let d=0;d<t.length;d++){let f=s.directiveStart+d,p=Zn(i,a,f,s);pn(p,i)}dy(a,i,s),c&&pn(c,i);let l=Zn(i,a,s.directiveStart+s.componentOffset,s);if(e[dt]=i[dt]=l,o!==null)for(let d of o)d(l,n);return Zu(a,s,i),l}function bM(e,n,t,r){if(r)wl(e,t,["ng-version","18.2.13"]);else{let{attrs:i,classes:o}=lb(n.selectors[0]);i&&wl(e,t,i),o&&o.length>0&&ny(e,t,o.join(" "))}}function wM(e,n,t){let r=e.projection=[];for(let i=0;i<n.length;i++){let o=t[i];r.push(o!=null?Array.from(o):null)}}function EM(){let e=Re();ta(q()[U],e)}var We=(()=>{class e{static{this.__NG_ELEMENT_ID__=MM}}return e})();function MM(){let e=Re();return xy(e,q())}var _M=We,Ty=class extends _M{constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return Br(this._hostTNode,this._hostLView)}get injector(){return new Bn(this._hostTNode,this._hostLView)}get parentInjector(){let n=Pu(this._hostTNode,this._hostLView);if(mv(n)){let t=Es(n,this._hostLView),r=ws(n),i=t[U].data[r+8];return new Bn(i,t)}else return new Bn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=sm(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-ze}createEmbeddedView(n,t,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let s=Kl(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},o,s);return this.insertImpl(a,i,Ql(this._hostTNode,s)),a}createComponent(n,t,r,i,o){let s=n&&!Mb(n),a;if(s)a=t;else{let D=t||{};a=D.index,r=D.injector,i=D.projectableNodes,o=D.environmentInjector||D.ngModuleRef}let c=s?n:new Yn(Lt(n)),l=r||this.parentInjector;if(!o&&c.ngModule==null){let S=(s?l:this.parentInjector).get(ue,null);S&&(o=S)}let d=Lt(c.componentType??{}),f=Kl(this._lContainer,d?.id??null),p=f?.firstChild??null,h=c.create(l,i,p,o);return this.insertImpl(h.hostView,a,Ql(this._hostTNode,f)),h}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let i=n._lView;if(Ob(i)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=i[Oe],l=new Ty(c,c[Be],c[Oe]);l.detach(l.indexOf(n))}}let o=this._adjustIndex(t),s=this._lContainer;return Dy(s,i,o,r),n.attachToViewContainerRef(),_m(pl(s),o,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=sm(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=Gl(this._lContainer,t);r&&(ys(pl(this._lContainer),t),Qv(r[U],r))}detach(n){let t=this._adjustIndex(n,-1),r=Gl(this._lContainer,t);return r&&ys(pl(this._lContainer),t)!=null?new Qn(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function sm(e){return e[Cs]}function pl(e){return e[Cs]||(e[Cs]=[])}function xy(e,n){let t,r=n[e.index];return Ht(r)?t=r:(t=fy(r,n,null,e),n[e.index]=t,ca(n,t)),TM(t,n,e,r),new Ty(t,e,n)}function SM(e,n){let t=e[he],r=t.createComment(""),i=rt(n,e),o=Xv(t,i);return As(t,o,r,gE(t,i),!1),r}var TM=RM,xM=()=>!1;function AM(e,n,t){return xM(e,n,t)}function RM(e,n,t,r){if(e[zn])return;let i;t.type&8?i=Mt(r):i=SM(n,t),e[zn]=i}var tu=class e{constructor(n){this.queryList=n,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},nu=class e{constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,i=[];for(let o=0;o<r;o++){let s=t.getByIndex(o),a=this.queries[s.indexInDeclarationView];i.push(a.clone())}return new e(i)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)od(n,t).matches!==null&&this.queries[t].setDirty()}},Ps=class{constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=VM(n):this.predicate=n}},ru=class e{constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let i=t!==null?t.length:0,o=this.getByIndex(r).embeddedTView(n,i);o&&(o.indexInDeclarationView=r,t!==null?t.push(o):t=[o])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},iu=class e{constructor(n,t=-1){this.metadata=n,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){let o=r[i];this.matchTNodeWithReadOption(n,t,NM(t,o)),this.matchTNodeWithReadOption(n,t,hs(t,n,o,!1,!1))}else r===_t?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,hs(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let i=this.metadata.read;if(i!==null)if(i===m||i===We||i===_t&&t.type&4)this.addMatch(t.index,-2);else{let o=hs(t,n,i,!1,!1);o!==null&&this.addMatch(t.index,o)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function NM(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function OM(e,n){return e.type&11?Br(e,n):e.type&4?rd(e,n):null}function kM(e,n,t,r){return t===-1?OM(n,e):t===-2?PM(e,n,r):Zn(e,e[U],t,n)}function PM(e,n,t){if(t===m)return Br(n,e);if(t===_t)return rd(n,e);if(t===We)return xy(n,e)}function Ay(e,n,t,r){let i=n[Vt].queries[r];if(i.matches===null){let o=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=o[l];a.push(kM(n,d,s[c+1],t.metadata.read))}}i.matches=a}return i.matches}function ou(e,n,t,r){let i=e.queries.getByIndex(t),o=i.matches;if(o!==null){let s=Ay(e,n,i,t);for(let a=0;a<o.length;a+=2){let c=o[a];if(c>0)r.push(s[a/2]);else{let l=o[a+1],d=n[-c];for(let f=ze;f<d.length;f++){let p=d[f];p[Hn]===p[Oe]&&ou(p[U],p,l,r)}if(d[Or]!==null){let f=d[Or];for(let p=0;p<f.length;p++){let h=f[p];ou(h[U],h,l,r)}}}}}return r}function FM(e,n){return e[Vt].queries[n].queryList}function Ry(e,n,t){let r=new Vl((t&4)===4);return xE(e,n,r,r.destroy),(n[Vt]??=new nu).queries.push(new tu(r))-1}function jM(e,n,t){let r=pe();return r.firstCreatePass&&(Ny(r,new Ps(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Ry(r,q(),n)}function LM(e,n,t,r){let i=pe();if(i.firstCreatePass){let o=Re();Ny(i,new Ps(n,t,r),o.index),BM(i,e),(t&2)===2&&(i.staticContentQueries=!0)}return Ry(i,q(),t)}function VM(e){return e.split(",").map(n=>n.trim())}function Ny(e,n,t){e.queries===null&&(e.queries=new ru),e.queries.track(new iu(n,t))}function BM(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function od(e,n){return e.queries.getByIndex(n)}function UM(e,n){let t=e[U],r=od(t,n);return r.crossesNgTemplate?ou(t,e,n,[]):Ay(t,e,r,n)}var am=new Set;function Hr(e){am.has(e)||(am.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function $M(e){return typeof e=="function"&&e[Ft]!==void 0}function Ri(e,n){Hr("NgSignals");let t=Mg(e),r=t[Ft];return n?.equal&&(r.equal=n.equal),t.set=i=>ol(r,i),t.update=i=>_g(r,i),t.asReadonly=HM.bind(t),t}function HM(){let e=this[Ft];if(e.readonlyFn===void 0){let n=()=>this();n[Ft]=e,e.readonlyFn=n}return e.readonlyFn}function Oy(e){return $M(e)&&typeof e.set=="function"}function zM(e){return Object.getPrototypeOf(e.prototype).constructor}function oe(e){let n=zM(e.type),t=!0,r=[e];for(;n;){let i;if(hn(e))i=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new R(903,!1);i=n.\u0275dir}if(i){if(t){r.push(i);let s=e;s.inputs=as(e.inputs),s.inputTransforms=as(e.inputTransforms),s.declaredInputs=as(e.declaredInputs),s.outputs=as(e.outputs);let a=i.hostBindings;a&&QM(e,a);let c=i.viewQuery,l=i.contentQueries;if(c&&qM(e,c),l&&ZM(e,l),GM(e,i),_C(e.outputs,i.outputs),hn(i)&&i.data.animation){let d=e.data;d.animation=(d.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let s=0;s<o.length;s++){let a=o[s];a&&a.ngInherit&&a(e),a===oe&&(t=!1)}}n=Object.getPrototypeOf(n)}WM(r)}function GM(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];if(r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t],n.inputTransforms!==null)){let i=Array.isArray(r)?r[0]:r;if(!n.inputTransforms.hasOwnProperty(i))continue;e.inputTransforms??={},e.inputTransforms[i]=n.inputTransforms[i]}}}function WM(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=n+=i.hostVars,i.hostAttrs=Ii(i.hostAttrs,t=Ii(t,i.hostAttrs))}}function as(e){return e===xr?{}:e===Ke?[]:e}function qM(e,n){let t=e.viewQuery;t?e.viewQuery=(r,i)=>{n(r,i),t(r,i)}:e.viewQuery=n}function ZM(e,n){let t=e.contentQueries;t?e.contentQueries=(r,i,o)=>{n(r,i,o),t(r,i,o)}:e.contentQueries=n}function QM(e,n){let t=e.hostBindings;t?e.hostBindings=(r,i)=>{n(r,i),t(r,i)}:e.hostBindings=n}function sd(e){let n=e.inputConfig,t={};for(let r in n)if(n.hasOwnProperty(r)){let i=n[r];Array.isArray(i)&&i[3]&&(t[r]=i[3])}e.inputTransforms=t}var gn=class{},Ei=class{};var su=class extends gn{constructor(n,t,r,i=!0){super(),this.ngModuleType=n,this._parent=t,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new ks(this);let o=jm(n);this._bootstrapComponents=Gv(o.bootstrap),this._r3Injector=Mv(n,t,[{provide:gn,useValue:this},{provide:Fr,useValue:this.componentFactoryResolver},...r],Fe(n),new Set(["environment"])),i&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},au=class extends Ei{constructor(n){super(),this.moduleType=n}create(n){return new su(this.moduleType,n,[])}};var Fs=class extends gn{constructor(n){super(),this.componentFactoryResolver=new ks(this),this.instance=null;let t=new Ci([...n.providers,{provide:gn,useValue:this},{provide:Fr,useValue:this.componentFactoryResolver}],n.parent||Gs(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function la(e,n,t=null){return new Fs({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}function ky(e){return KM(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function YM(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function KM(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Py(e,n,t){return e[n]=t}function XM(e,n){return e[n]}function Kn(e,n,t){let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function Fy(e,n,t,r){let i=Kn(e,n,t);return Kn(e,n+1,r)||i}function JM(e){return(e.flags&32)===32}function e_(e,n,t,r,i,o,s,a,c){let l=n.consts,d=$r(n,e,4,s||null,a||null);Ju(n,t,d,kr(l,c)),ta(n,d);let f=d.tView=Ku(2,d,r,i,o,n.directiveRegistry,n.pipeRegistry,null,n.schemas,l,null);return n.queries!==null&&(n.queries.template(n,d),f.queries=n.queries.embeddedTView(d)),d}function jy(e,n,t,r,i,o,s,a,c,l){let d=t+Ge,f=n.firstCreatePass?e_(d,n,e,r,i,o,s,a,c):n.data[d];Jn(f,!1);let p=t_(n,e,f,t);Js()&&ia(n,e,p,f),pn(p,e);let h=fy(p,e,p,f);return e[d]=h,ca(e,h),AM(h,f,e),Zs(f)&&Qu(n,e,f),c!=null&&Yu(e,f,l),f}function Ni(e,n,t,r,i,o,s,a){let c=q(),l=pe(),d=kr(l.consts,o);return jy(c,l,e,n,t,r,i,d,s,a),Ni}var t_=n_;function n_(e,n,t,r){return ea(!0),n[he].createComment("")}var _r=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(_r||{}),Ly=(()=>{class e{constructor(){this.impl=null}execute(){this.impl?.execute()}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}}return e})(),cu=class e{constructor(){this.ngZone=v(g),this.scheduler=v(Pr),this.errorHandler=v(Bt,{optional:!0}),this.sequences=new Set,this.deferredRegistrations=new Set,this.executing=!1}static{this.PHASES=[_r.EarlyRead,_r.Write,_r.MixedReadWrite,_r.Read]}execute(){this.executing=!0;for(let n of e.PHASES)for(let t of this.sequences)if(!(t.erroredOrDestroyed||!t.hooks[n]))try{t.pipelinedValue=this.ngZone.runOutsideAngular(()=>t.hooks[n](t.pipelinedValue))}catch(r){t.erroredOrDestroyed=!0,this.errorHandler?.handleError(r)}this.executing=!1;for(let n of this.sequences)n.afterRun(),n.once&&(this.sequences.delete(n),n.destroy());for(let n of this.deferredRegistrations)this.sequences.add(n);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear()}register(n){this.executing?this.deferredRegistrations.add(n):(this.sequences.add(n),this.scheduler.notify(6))}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}},lu=class{constructor(n,t,r,i){this.impl=n,this.hooks=t,this.once=r,this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function ad(e,n){!n?.injector&&Eb(ad);let t=n?.injector??v(le);return rE(t)?(Hr("NgAfterNextRender"),i_(e,t,n,!0)):o_}function r_(e,n){if(e instanceof Function){let t=[void 0,void 0,void 0,void 0];return t[n]=e,t}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function i_(e,n,t,r){let i=n.get(Ly);i.impl??=n.get(cu);let o=t?.phase??_r.MixedReadWrite,s=t?.manualCleanup!==!0?n.get(Fu):null,a=new lu(i.impl,r_(e,o),r,s);return i.impl.register(a),a}var o_={destroy(){}};function Tt(e,n,t,r){let i=q(),o=Ks();if(Kn(i,o,n)){let s=pe(),a=Xs();zE(a,i,e,n,t,r)}return Tt}function Vy(e,n,t,r){return Kn(e,Ks(),t)?n+Un(t)+r:pt}function s_(e,n,t,r,i,o){let s=$b(),a=Fy(e,s,t,i);return av(2),a?n+Un(t)+r+Un(i)+o:pt}function cs(e,n){return e<<17|n<<2}function Xn(e){return e>>17&32767}function a_(e){return(e&2)==2}function c_(e,n){return e&131071|n<<17}function uu(e){return e|2}function Lr(e){return(e&131068)>>2}function gl(e,n){return e&-131069|n<<2}function l_(e){return(e&1)===1}function du(e){return e|1}function u_(e,n,t,r,i,o){let s=o?n.classBindings:n.styleBindings,a=Xn(s),c=Lr(s);e[r]=t;let l=!1,d;if(Array.isArray(t)){let f=t;d=f[1],(d===null||Si(f,d)>0)&&(l=!0)}else d=t;if(i)if(c!==0){let p=Xn(e[a+1]);e[r+1]=cs(p,a),p!==0&&(e[p+1]=gl(e[p+1],r)),e[a+1]=c_(e[a+1],r)}else e[r+1]=cs(a,0),a!==0&&(e[a+1]=gl(e[a+1],r)),a=r;else e[r+1]=cs(c,0),a===0?a=r:e[c+1]=gl(e[c+1],r),c=r;l&&(e[r+1]=uu(e[r+1])),cm(e,d,r,!0),cm(e,d,r,!1),d_(n,d,e,r,o),s=cs(a,c),o?n.classBindings=s:n.styleBindings=s}function d_(e,n,t,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof n=="string"&&Si(o,n)>=0&&(t[r+1]=du(t[r+1]))}function cm(e,n,t,r){let i=e[t+1],o=n===null,s=r?Xn(i):Lr(i),a=!1;for(;s!==0&&(a===!1||o);){let c=e[s],l=e[s+1];f_(c,n)&&(a=!0,e[s+1]=r?du(l):uu(l)),s=r?Xn(l):Lr(l)}a&&(e[t+1]=r?uu(i):du(i))}function f_(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?Si(e,n)>=0:!1}function yn(e,n,t){let r=q(),i=Ks();if(Kn(r,i,n)){let o=pe(),s=Xs();Xu(o,s,r,e,n,r[he],t,!1)}return yn}function lm(e,n,t,r,i){let o=n.inputs,s=i?"class":"style";ed(e,t,o[s],s,r)}function By(e,n,t){return Uy(e,n,t,!1),By}function ua(e,n){return Uy(e,n,null,!0),ua}function Uy(e,n,t,r){let i=q(),o=pe(),s=av(2);if(o.firstUpdatePass&&p_(o,e,s,r),n!==pt&&Kn(i,s,n)){let a=o.data[er()];D_(o,a,i,i[he],e,i[s+1]=I_(n,t),r,s)}}function h_(e,n){return n>=e.expandoStartIndex}function p_(e,n,t,r){let i=e.data;if(i[t+1]===null){let o=i[er()],s=h_(e,t);C_(o,r)&&n===null&&!s&&(n=!1),n=g_(i,o,n,r),u_(i,o,n,t,s,r)}}function g_(e,n,t,r){let i=qb(e),o=r?n.residualClasses:n.residualStyles;if(i===null)(r?n.classBindings:n.styleBindings)===0&&(t=ml(null,e,n,t,r),t=Mi(t,n.attrs,r),o=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==i)if(t=ml(i,e,n,t,r),o===null){let c=m_(e,n,r);c!==void 0&&Array.isArray(c)&&(c=ml(null,e,n,c[1],r),c=Mi(c,n.attrs,r),v_(e,n,r,c))}else o=y_(e,n,r)}return o!==void 0&&(r?n.residualClasses=o:n.residualStyles=o),t}function m_(e,n,t){let r=t?n.classBindings:n.styleBindings;if(Lr(r)!==0)return e[Xn(r)]}function v_(e,n,t,r){let i=t?n.classBindings:n.styleBindings;e[Xn(i)]=r}function y_(e,n,t){let r,i=n.directiveEnd;for(let o=1+n.directiveStylingLast;o<i;o++){let s=e[o].hostAttrs;r=Mi(r,s,t)}return Mi(r,n.attrs,t)}function ml(e,n,t,r,i){let o=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(o=n[a],r=Mi(r,o.hostAttrs,i),o!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Mi(e,n,t){let r=t?1:2,i=-1;if(n!==null)for(let o=0;o<n.length;o++){let s=n[o];typeof s=="number"?i=s:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),QC(e,s,t?!0:n[++o]))}return e===void 0?null:e}function D_(e,n,t,r,i,o,s,a){if(!(n.type&3))return;let c=e.data,l=c[a+1],d=l_(l)?um(c,n,t,i,Lr(l),s):void 0;if(!js(d)){js(o)||a_(l)&&(o=um(c,null,t,i,a,s));let f=Km(er(),t);CE(r,s,f,i,o)}}function um(e,n,t,r,i,o){let s=n===null,a;for(;i>0;){let c=e[i],l=Array.isArray(c),d=l?c[1]:c,f=d===null,p=t[i+1];p===pt&&(p=f?Ke:void 0);let h=f?sl(p,r):d===r?p:void 0;if(l&&!js(h)&&(h=sl(c,r)),js(h)&&(a=h,s))return a;let D=e[i+1];i=s?Xn(D):Lr(D)}if(n!==null){let c=o?n.residualClasses:n.residualStyles;c!=null&&(a=sl(c,r))}return a}function js(e){return e!==void 0}function I_(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Fe(Ur(e)))),e}function C_(e,n){return(e.flags&(n?8:16))!==0}function b_(e,n,t,r,i,o){let s=n.consts,a=kr(s,i),c=$r(n,e,2,r,a);return Ju(n,t,c,kr(s,o)),c.attrs!==null&&Os(c,c.attrs,!1),c.mergedAttrs!==null&&Os(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function zr(e,n,t,r){let i=q(),o=pe(),s=Ge+e,a=i[he],c=o.firstCreatePass?b_(s,o,i,n,t,r):o.data[s],l=w_(o,i,c,a,n,e);i[s]=l;let d=Zs(c);return Jn(c,!0),ry(a,l,c),!JM(c)&&Js()&&ia(o,i,l,c),Fb()===0&&pn(l,i),jb(),d&&(Qu(o,i,c),Zu(o,c,i)),r!==null&&Yu(i,c),zr}function Gr(){let e=Re();xu()?Au():(e=e.parent,Jn(e,!1));let n=e;Vb(n)&&Bb(),Lb();let t=pe();return t.firstCreatePass&&(ta(t,e),Mu(e)&&t.queries.elementEnd(e)),n.classesWithoutHost!=null&&ew(n)&&lm(t,n,q(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&tw(n)&&lm(t,n,q(),n.stylesWithoutHost,!1),Gr}function cd(e,n,t,r){return zr(e,n,t,r),Gr(),cd}var w_=(e,n,t,r,i,o)=>(ea(!0),Wv(r,i,Yb()));function E_(e,n,t,r,i){let o=n.consts,s=kr(o,r),a=$r(n,e,8,"ng-container",s);s!==null&&Os(a,s,!0);let c=kr(o,i);return Ju(n,t,a,c),n.queries!==null&&n.queries.elementStart(n,a),a}function da(e,n,t){let r=q(),i=pe(),o=e+Ge,s=i.firstCreatePass?E_(o,i,r,n,t):i.data[o];Jn(s,!0);let a=M_(i,r,s,e);return r[o]=a,Js()&&ia(i,r,a,s),pn(a,r),Zs(s)&&(Qu(i,r,s),Zu(i,s,r)),t!=null&&Yu(r,s),da}function fa(){let e=Re(),n=pe();return xu()?Au():(e=e.parent,Jn(e,!1)),n.firstCreatePass&&(ta(n,e),Mu(e)&&n.queries.elementEnd(e)),fa}function ha(e,n,t){return da(e,n,t),fa(),ha}var M_=(e,n,t,r)=>(ea(!0),aE(n[he],""));function $y(){return q()}var Vn=void 0;function __(e){let n=e,t=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&r===0?1:5}var S_=["en",[["a","p"],["AM","PM"],Vn],[["AM","PM"],Vn,Vn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Vn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Vn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Vn,"{1} 'at' {0}",Vn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",__],vl={};function it(e){let n=T_(e),t=dm(n);if(t)return t;let r=n.split("-")[0];if(t=dm(r),t)return t;if(r==="en")return S_;throw new R(701,!1)}function dm(e){return e in vl||(vl[e]=jt.ng&&jt.ng.common&&jt.ng.common.locales&&jt.ng.common.locales[e]),vl[e]}var ge=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ge||{});function T_(e){return e.toLowerCase().replace(/_/g,"-")}var Ls="en-US";var x_=Ls;function A_(e){typeof e=="string"&&(x_=e.toLowerCase().replace(/_/g,"-"))}var R_=(e,n,t)=>{};function Me(e,n,t,r){let i=q(),o=pe(),s=Re();return Hy(o,i,i[he],s,e,n,r),Me}function N_(e,n,t,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let s=i[o];if(s===t&&i[o+1]===r){let a=n[Is],c=i[o+2];return a.length>c?a[c]:null}typeof s=="string"&&(o+=2)}return null}function Hy(e,n,t,r,i,o,s){let a=Zs(r),l=e.firstCreatePass&&gy(e),d=n[dt],f=py(n),p=!0;if(r.type&3||s){let S=rt(r,n),T=s?s(S):S,F=f.length,re=s?me=>s(Mt(me[r.index])):r.index,Z=null;if(!s&&a&&(Z=N_(e,n,i,r.index)),Z!==null){let me=Z.__ngLastListenerFn__||Z;me.__ngNextListenerFn__=o,Z.__ngLastListenerFn__=o,p=!1}else{o=hm(r,n,d,o),R_(S,i,o);let me=t.listen(T,i,o);f.push(o,me),l&&l.push(i,re,F,F+1)}}else o=hm(r,n,d,o);let h=r.outputs,D;if(p&&h!==null&&(D=h[i])){let S=D.length;if(S)for(let T=0;T<S;T+=2){let F=D[T],re=D[T+1],Te=n[F][re].subscribe(o),Ce=f.length;f.push(o,Te),l&&l.push(i,r.index,Ce,-(Ce+1))}}}function fm(e,n,t,r){let i=Q(null);try{return Ct(6,n,t),t(r)!==!1}catch(o){return my(e,o),!1}finally{Ct(7,n,t),Q(i)}}function hm(e,n,t,r){return function i(o){if(o===Function)return r;let s=e.componentOffset>-1?mn(e.index,n):n;nd(s,5);let a=fm(n,t,r,o),c=i.__ngNextListenerFn__;for(;c;)a=fm(n,t,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Oi(e=1){return Qb(e)}function O_(e,n){let t=null,r=rb(e);for(let i=0;i<n.length;i++){let o=n[i];if(o==="*"){t=i;continue}if(r===null?Nm(e,o,!0):sb(r,o))return i}return t}function E(e){let n=q()[Je][Be];if(!n.projection){let t=e?e.length:1,r=n.projection=qC(t,null),i=r.slice(),o=n.child;for(;o!==null;){if(o.type!==128){let s=e?O_(o,e):0;s!==null&&(i[s]?i[s].projectionNext=o:r[s]=o,i[s]=o)}o=o.next}}}function C(e,n=0,t,r,i,o){let s=q(),a=pe(),c=r?e+1:null;c!==null&&jy(s,a,c,r,i,o,null,t);let l=$r(a,Ge+e,16,null,t||null);l.projection===null&&(l.projection=n),Au();let f=!s[bi]||iv();s[Je][Be].projection[l.projection]===null&&c!==null?k_(s,a,c):f&&(l.flags&32)!==32&&DE(a,s,l)}function k_(e,n,t){let r=Ge+t,i=n.data[r],o=e[r],s=Kl(o,i.tView.ssrId),a=yy(e,i,void 0,{dehydratedView:s});Dy(o,a,0,Ql(i,s))}function P_(e,n,t){return zy(e,"",n,"",t),P_}function zy(e,n,t,r,i){let o=q(),s=Vy(o,n,t,r);if(s!==pt){let a=pe(),c=Xs();Xu(a,c,o,e,s,o[he],i,!1)}return zy}function Dn(e,n,t,r){LM(e,n,t,r)}function ki(e,n,t){jM(e,n,t)}function gt(e){let n=q(),t=pe(),r=cv();Nu(r+1);let i=od(t,r);if(e.dirty&&Nb(n)===((i.metadata.flags&2)===2)){if(i.matches===null)e.reset([]);else{let o=UM(n,r);e.reset(o,bw),e.notifyOnChanges()}return!0}return!1}function mt(){return FM(q(),cv())}function F_(e,n,t,r){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=r}function OB(e,n=""){let t=q(),r=pe(),i=e+Ge,o=r.firstCreatePass?$r(r,i,1,n,null):r.data[i],s=j_(r,t,o,n,e);t[i]=s,Js()&&ia(r,t,s,o),Jn(o,!1)}var j_=(e,n,t,r,i)=>(ea(!0),oE(n[he],r));function L_(e){return Gy("",e,""),L_}function Gy(e,n,t){let r=q(),i=Vy(r,e,n,t);return i!==pt&&vy(r,er(),i),Gy}function V_(e,n,t,r,i){let o=q(),s=s_(o,e,n,t,r,i);return s!==pt&&vy(o,er(),s),V_}function B_(e,n,t){Oy(n)&&(n=n());let r=q(),i=Ks();if(Kn(r,i,n)){let o=pe(),s=Xs();Xu(o,s,r,e,n,r[he],t,!1)}return B_}function kB(e,n){let t=Oy(e);return t&&e.set(n),t}function U_(e,n){let t=q(),r=pe(),i=Re();return Hy(r,t,t[he],i,e,n),U_}function $_(e,n,t){let r=pe();if(r.firstCreatePass){let i=hn(e);fu(t,r.data,r.blueprint,i,!0),fu(n,r.data,r.blueprint,i,!1)}}function fu(e,n,t,r,i){if(e=Pe(e),Array.isArray(e))for(let o=0;o<e.length;o++)fu(e[o],n,t,r,i);else{let o=pe(),s=q(),a=Re(),c=Rr(e)?e:Pe(e.provide),l=Hm(e),d=a.providerIndexes&1048575,f=a.directiveStart,p=a.providerIndexes>>20;if(Rr(e)||!e.multi){let h=new qn(l,i,u),D=Dl(c,n,i?d:d+p,f);D===-1?(Ol(_s(a,s),o,c),yl(o,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),t.push(h),s.push(h)):(t[D]=h,s[D]=h)}else{let h=Dl(c,n,d+p,f),D=Dl(c,n,d,d+p),S=h>=0&&t[h],T=D>=0&&t[D];if(i&&!T||!i&&!S){Ol(_s(a,s),o,c);let F=G_(i?z_:H_,t.length,i,r,l);!i&&T&&(t[D].providerFactory=F),yl(o,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),t.push(F),s.push(F)}else{let F=Wy(t[i?D:h],l,!i&&r);yl(o,e,h>-1?h:D,F)}!i&&r&&T&&t[D].componentProviders++}}}function yl(e,n,t,r){let i=Rr(n),o=vb(n);if(i||o){let c=(o?Pe(n.useClass):n).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!i&&n.multi){let d=l.indexOf(t);d===-1?l.push(t,[r,c]):l[d+1].push(r,c)}else l.push(t,c)}}}function Wy(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Dl(e,n,t,r){for(let i=t;i<r;i++)if(n[i]===e)return i;return-1}function H_(e,n,t,r){return hu(this.multi,[])}function z_(e,n,t,r){let i=this.multi,o;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Zn(t,t[U],this.providerFactory.index,r);o=a.slice(0,s),hu(i,o);for(let c=s;c<a.length;c++)o.push(a[c])}else o=[],hu(i,o);return o}function hu(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function G_(e,n,t,r,i){let o=new qn(e,t,u);return o.multi=[],o.index=n,o.componentProviders=0,Wy(o,i,r&&!t),o}function Ue(e,n=[]){return t=>{t.providersResolver=(r,i)=>$_(r,i?i(e):e,n)}}var W_=(()=>{class e{constructor(t){this._injector=t,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Bm(!1,t.type),i=r.length>0?la([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,i)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=x({token:e,providedIn:"environment",factory:()=>new e(N(ue))})}}return e})();function qy(e){Hr("NgStandalone"),e.getStandaloneInjector=n=>n.get(W_).getOrCreateStandaloneInjector(e)}function PB(e,n,t){let r=Ru()+e,i=q();return i[r]===pt?Py(i,r,t?n.call(t):n()):XM(i,r)}function FB(e,n,t,r,i){return Zy(q(),Ru(),e,n,t,r,i)}function q_(e,n){let t=e[n];return t===pt?void 0:t}function Zy(e,n,t,r,i,o,s){let a=n+t;return Fy(e,a,i,o)?Py(e,a+2,s?r.call(s,i,o):r(i,o)):q_(e,a+2)}function jB(e,n){let t=pe(),r,i=e+Ge;t.firstCreatePass?(r=Z_(n,t.pipeRegistry),t.data[i]=r,r.onDestroy&&(t.destroyHooks??=[]).push(i,r.onDestroy)):r=t.data[i];let o=r.factory||(r.factory=$n(r.type,!0)),s,a=Ve(u);try{let c=Ms(!1),l=o();return Ms(c),F_(t,q(),i,l),l}finally{Ve(a)}}function Z_(e,n){if(n)for(let t=n.length-1;t>=0;t--){let r=n[t];if(e===r.name)return r}}function LB(e,n,t,r){let i=e+Ge,o=q(),s=Rb(o,i);return Q_(o,i)?Zy(o,Ru(),n,s.transform,t,r,s):s.transform(t,r)}function Q_(e,n){return e[U].data[n].pure}var pa=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Qy=new A("");function tr(e){return!!e&&typeof e.then=="function"}function Yy(e){return!!e&&typeof e.subscribe=="function"}var Wr=new A(""),Ky=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r}),this.appInits=v(Wr,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let t=[];for(let i of this.appInits){let o=i();if(tr(o))t.push(o);else if(Yy(o)){let s=new Promise((a,c)=>{o.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(i=>{this.reject(i)}),t.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Pi=new A("");function Y_(){Eg(()=>{throw new R(600,!1)})}function K_(e){return e.isBoundToModule}var X_=10;function J_(e,n,t){try{let r=t();return tr(r)?r.catch(i=>{throw n.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}var xt=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=v(Iw),this.afterRenderManager=v(Ly),this.zonelessEnabled=v(id),this.dirtyFlags=0,this.deferredDirtyFlags=0,this.externalTestViews=new Set,this.beforeRender=new ae,this.afterTick=new ae,this.componentTypes=[],this.components=[],this.isStable=v(Gt).hasPendingTasks.pipe(B(t=>!t)),this._injector=v(ue)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:i=>{i&&r()}})}).finally(()=>{t.unsubscribe()})}get injector(){return this._injector}bootstrap(t,r){let i=t instanceof Ns;if(!this._injector.get(Ky).done){let p=!i&&Fm(t),h=!1;throw new R(405,h)}let s;i?s=t:s=this._injector.get(Fr).resolveComponentFactory(t),this.componentTypes.push(s.componentType);let a=K_(s)?void 0:this._injector.get(gn),c=r||s.selector,l=s.create(le.NULL,[],c,a),d=l.location.nativeElement,f=l.injector.get(Qy,null);return f?.registerApplication(d),l.onDestroy(()=>{this.detachView(l.hostView),ps(this.components,l),f?.unregisterApplication(d)}),this._loadComponent(l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){if(this._runningTick)throw new R(101,!1);let t=Q(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,Q(t),this.afterTick.next()}}synchronize(){let t=null;this._injector.destroyed||(t=this._injector.get(jr,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let r=0;for(;this.dirtyFlags!==0&&r++<X_;)this.synchronizeOnce(t)}synchronizeOnce(t){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8,this.beforeRender.next(r);for(let{_lView:i,notifyErrorHandler:o}of this._views)eS(i,o,r,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&7)return}else t?.begin?.(),t?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>Qs(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;ps(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t);let r=this._injector.get(Pi,[]);[...this._bootstrapListeners,...r].forEach(i=>i(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>ps(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new R(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function ps(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function eS(e,n,t,r){if(!t&&!Qs(e))return;by(e,n,t&&!r?0:1)}var pu=class{constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},ga=(()=>{class e{compileModuleSync(t){return new au(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),i=jm(t),o=Gv(i.declarations).reduce((s,a)=>{let c=Lt(a);return c&&s.push(new Yn(c)),s},[]);return new pu(r,o)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var tS=(()=>{class e{constructor(){this.zone=v(g),this.changeDetectionScheduler=v(Pr),this.applicationRef=v(xt)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function nS({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new g(V(b({},rS()),{scheduleInRootZone:t})),[{provide:g,useFactory:e},{provide:Ar,multi:!0,useFactory:()=>{let r=v(tS,{optional:!0});return()=>r.initialize()}},{provide:Ar,multi:!0,useFactory:()=>{let r=v(iS);return()=>{r.initialize()}}},n===!0?{provide:_y,useValue:!0}:[],{provide:Sy,useValue:t??_v}]}function rS(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var iS=(()=>{class e{constructor(){this.subscription=new de,this.initialized=!1,this.zone=v(g),this.pendingTasks=v(Gt)}initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{g.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var oS=(()=>{class e{constructor(){this.appRef=v(xt),this.taskService=v(Gt),this.ngZone=v(g),this.zonelessEnabled=v(id),this.disableScheduling=v(_y,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new de,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ts):null,this.scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(v(Sy,{optional:!0})??!1),this.cancelScheduledCallback=null,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ll||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 7:{this.appRef.deferredDirtyFlags|=8;break}case 9:case 8:case 6:case 10:default:this.appRef.dirtyFlags|=8}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?Gg:Sv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>r(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>r(()=>this.tick()))}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ts+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Gg(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function sS(){return typeof $localize<"u"&&$localize.locale||Ls}var ma=new A("",{providedIn:"root",factory:()=>v(ma,z.Optional|z.SkipSelf)||sS()});var gu=new A("");function ls(e){return!e.moduleRef}function aS(e){let n=ls(e)?e.r3Injector:e.moduleRef.injector,t=n.get(g);return t.run(()=>{ls(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Bt,null),i;if(t.runOutsideAngular(()=>{i=t.onError.subscribe({next:o=>{r.handleError(o)}})}),ls(e)){let o=()=>n.destroy(),s=e.platformInjector.get(gu);s.add(o),n.onDestroy(()=>{i.unsubscribe(),s.delete(o)})}else{let o=()=>e.moduleRef.destroy(),s=e.platformInjector.get(gu);s.add(o),e.moduleRef.onDestroy(()=>{ps(e.allPlatformModules,e.moduleRef),i.unsubscribe(),s.delete(o)})}return J_(r,t,()=>{let o=n.get(Ky);return o.runInitializers(),o.donePromise.then(()=>{let s=n.get(ma,Ls);if(A_(s||Ls),ls(e)){let a=n.get(xt);return e.rootComponent!==void 0&&a.bootstrap(e.rootComponent),a}else return cS(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function cS(e,n){let t=e.injector.get(xt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(t);else throw new R(-403,!1);n.push(e)}var gs=null;function lS(e=[],n){return le.create({name:n,providers:[{provide:zs,useValue:"platform"},{provide:gu,useValue:new Set([()=>gs=null])},...e]})}function uS(e=[]){if(gs)return gs;let n=lS(e);return gs=n,Y_(),dS(n),n}function dS(e){e.get(Bu,null)?.forEach(t=>t())}var y=(()=>{class e{static{this.__NG_ELEMENT_ID__=fS}}return e})();function fS(e){return hS(Re(),q(),(e&16)===16)}function hS(e,n,t){if(qs(e)&&!t){let r=mn(e.index,n);return new Qn(r,r)}else if(e.type&175){let r=n[Je];return new Qn(r,n)}return null}var mu=class{constructor(){}supports(n){return ky(n)}create(n){return new vu(n)}},pS=(e,n)=>n,vu=class{constructor(n){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=n||pS}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,i=0,o=null;for(;t||r;){let s=!r||t&&t.currentIndex<pm(r,i,o)?t:r,a=pm(s,i,o),c=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)i++;else{o||(o=[]);let l=a-i,d=c-i;if(l!=d){for(let p=0;p<l;p++){let h=p<o.length?o[p]:o[p]=0,D=h+p;d<=D&&D<l&&(o[p]=h+1)}let f=s.previousIndex;o[f]=d-l}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!ky(n))throw new R(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,i,o,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)o=n[a],s=this._trackByFn(a,o),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,o,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,o,s,a)),Object.is(t.item,o)||this._addIdentityChange(t,o)),t=t._next}else i=0,YM(n,a=>{s=this._trackByFn(i,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,i),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,i)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,i++}),this.length=i;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,i){let o;return n===null?o=this._itTail:(o=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,o,i)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,i),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,o,i)):n=this._addAfter(new yu(t,r),o,i)),n}_verifyReinsertion(n,t,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?n=this._reinsertAfter(o,n._prev,i):n.currentIndex!=i&&(n.currentIndex=i,this._addToMoves(n,i)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let i=n._prevRemoved,o=n._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let i=t===null?this._itHead:t._next;return n._next=i,n._prev=t,i===null?this._itTail=n:i._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Vs),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Vs),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},yu=class{constructor(n,t){this.item=n,this.trackById=t,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Du=class{constructor(){this._head=null,this._tail=null}add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Vs=class{constructor(){this.map=new Map}put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Du,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,i=this.map.get(r);return i?i.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function pm(e,n,t){let r=e.previousIndex;if(r===null)return r;let i=0;return t&&r<t.length&&(i=t[r]),r+n+i}function gm(){return new ld([new mu])}var ld=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:gm})}constructor(t){this.factories=t}static create(t,r){if(r!=null){let i=r.factories.slice();t=t.concat(i)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||gm()),deps:[[e,new Cu,new Hs]]}}find(t){let r=this.factories.find(i=>i.supports(t));if(r!=null)return r;throw new R(901,!1)}}return e})();function Xy(e){try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,i=uS(r),o=[nS({}),{provide:Pr,useExisting:oS},...t||[]],s=new Fs({providers:o,parent:i,debugName:"",runEnvironmentInitializers:!1});return aS({r3Injector:s.injector,platformInjector:i,rootComponent:n})}catch(n){return Promise.reject(n)}}function In(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Fi(e,n){Hr("NgSignals");let t=Cg(e);return n?.equal&&(t[Ft].equal=n.equal),t}function qt(e){let n=Q(null);try{return e()}finally{Q(n)}}function Jy(e,n){let t=Lt(e),r=n.elementInjector||Gs();return new Yn(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector)}function va(e){let n=Lt(e);if(!n)return null;let t=new Yn(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var sD=null;function Kt(){return sD}function aD(e){sD??=e}var _a=class{};var Ie=new A(""),Dd=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(mS),providedIn:"platform"})}}return e})(),cD=new A(""),mS=(()=>{class e extends Dd{constructor(){super(),this._doc=v(Ie),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Kt().getBaseHref(this._doc)}onPopState(t){let r=Kt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=Kt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,i){this._history.pushState(t,r,i)}replaceState(t,r,i){this._history.replaceState(t,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Id(e,n){if(e.length==0)return n;if(n.length==0)return e;let t=0;return e.endsWith("/")&&t++,n.startsWith("/")&&t++,t==2?e+n.substring(1):t==1?e+n:e+"/"+n}function eD(e){let n=e.match(/#|\?|$/),t=n&&n.index||e.length,r=t-(e[t-1]==="/"?1:0);return e.slice(0,r)+e.slice(t)}function Qt(e){return e&&e[0]!=="?"?"?"+e:e}var Ze=(()=>{class e{historyGo(t){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(Cd),providedIn:"root"})}}return e})(),lD=new A(""),Cd=(()=>{class e extends Ze{constructor(t,r){super(),this._platformLocation=t,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??v(Ie).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return Id(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+Qt(this._platformLocation.search),i=this._platformLocation.hash;return i&&t?`${r}${i}`:r}pushState(t,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));this._platformLocation.pushState(t,r,s)}replaceState(t,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static{this.\u0275fac=function(r){return new(r||e)(N(Dd),N(lD,8))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),bd=(()=>{class e extends Ze{constructor(t,r){super(),this._platformLocation=t,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=Id(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(t,r,s)}replaceState(t,r,i,o){let s=this.prepareExternalUrl(i+Qt(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static{this.\u0275fac=function(r){return new(r||e)(N(Dd),N(lD,8))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),yt=(()=>{class e{constructor(t){this._subject=new ee,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=DS(eD(tD(r))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Qt(r))}normalize(t){return e.stripTrailingSlash(yS(this._basePath,tD(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",i=null){this._locationStrategy.pushState(i,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Qt(r)),i)}replaceState(t,r="",i=null){this._locationStrategy.replaceState(i,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Qt(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(i=>i(t,r))}subscribe(t,r,i){return this._subject.subscribe({next:t,error:r,complete:i})}static{this.normalizeQueryParams=Qt}static{this.joinWithSlash=Id}static{this.stripTrailingSlash=eD}static{this.\u0275fac=function(r){return new(r||e)(N(Ze))}}static{this.\u0275prov=x({token:e,factory:()=>vS(),providedIn:"root"})}}return e})();function vS(){return new yt(N(Ze))}function yS(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function tD(e){return e.replace(/\/index.html$/,"")}function DS(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var je=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(je||{}),ne=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(ne||{}),qe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(qe||{}),Cn={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function IS(e){return it(e)[ge.LocaleId]}function CS(e,n,t){let r=it(e),i=[r[ge.DayPeriodsFormat],r[ge.DayPeriodsStandalone]],o=ot(i,n);return ot(o,t)}function bS(e,n,t){let r=it(e),i=[r[ge.DaysFormat],r[ge.DaysStandalone]],o=ot(i,n);return ot(o,t)}function wS(e,n,t){let r=it(e),i=[r[ge.MonthsFormat],r[ge.MonthsStandalone]],o=ot(i,n);return ot(o,t)}function ES(e,n){let r=it(e)[ge.Eras];return ot(r,n)}function ya(e,n){let t=it(e);return ot(t[ge.DateFormat],n)}function Da(e,n){let t=it(e);return ot(t[ge.TimeFormat],n)}function Ia(e,n){let r=it(e)[ge.DateTimeFormat];return ot(r,n)}function Ta(e,n){let t=it(e),r=t[ge.NumberSymbols][n];if(typeof r>"u"){if(n===Cn.CurrencyDecimal)return t[ge.NumberSymbols][Cn.Decimal];if(n===Cn.CurrencyGroup)return t[ge.NumberSymbols][Cn.Group]}return r}function uD(e){if(!e[ge.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ge.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function MS(e){let n=it(e);return uD(n),(n[ge.ExtraData][2]||[]).map(r=>typeof r=="string"?ud(r):[ud(r[0]),ud(r[1])])}function _S(e,n,t){let r=it(e);uD(r);let i=[r[ge.ExtraData][0],r[ge.ExtraData][1]],o=ot(i,n)||[];return ot(o,t)||[]}function ot(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new Error("Locale data API: locale data undefined")}function ud(e){let[n,t]=e.split(":");return{hours:+n,minutes:+t}}var SS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ca={},TS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Yt=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(Yt||{}),X=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(X||{}),K=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(K||{});function xS(e,n,t,r){let i=LS(e);n=Zt(t,n)||n;let s=[],a;for(;n;)if(a=TS.exec(n),a){s=s.concat(a.slice(1));let d=s.pop();if(!d)break;n=d}else{s.push(n);break}let c=i.getTimezoneOffset();r&&(c=fD(r,c),i=jS(i,r,!0));let l="";return s.forEach(d=>{let f=PS(d);l+=f?f(i,t,c):d==="''"?"'":d.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function Sa(e,n,t){let r=new Date(0);return r.setFullYear(e,n,t),r.setHours(0,0,0),r}function Zt(e,n){let t=IS(e);if(Ca[t]??={},Ca[t][n])return Ca[t][n];let r="";switch(n){case"shortDate":r=ya(e,qe.Short);break;case"mediumDate":r=ya(e,qe.Medium);break;case"longDate":r=ya(e,qe.Long);break;case"fullDate":r=ya(e,qe.Full);break;case"shortTime":r=Da(e,qe.Short);break;case"mediumTime":r=Da(e,qe.Medium);break;case"longTime":r=Da(e,qe.Long);break;case"fullTime":r=Da(e,qe.Full);break;case"short":let i=Zt(e,"shortTime"),o=Zt(e,"shortDate");r=ba(Ia(e,qe.Short),[i,o]);break;case"medium":let s=Zt(e,"mediumTime"),a=Zt(e,"mediumDate");r=ba(Ia(e,qe.Medium),[s,a]);break;case"long":let c=Zt(e,"longTime"),l=Zt(e,"longDate");r=ba(Ia(e,qe.Long),[c,l]);break;case"full":let d=Zt(e,"fullTime"),f=Zt(e,"fullDate");r=ba(Ia(e,qe.Full),[d,f]);break}return r&&(Ca[t][n]=r),r}function ba(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,r){return n!=null&&r in n?n[r]:t})),e}function vt(e,n,t="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=t));let s=String(e);for(;s.length<n;)s="0"+s;return r&&(s=s.slice(s.length-n)),o+s}function AS(e,n){return vt(e,3).substring(0,n)}function De(e,n,t=0,r=!1,i=!1){return function(o,s){let a=RS(e,o);if((t>0||a>-t)&&(a+=t),e===X.Hours)a===0&&t===-12&&(a=12);else if(e===X.FractionalSeconds)return AS(a,n);let c=Ta(s,Cn.MinusSign);return vt(a,n,c,r,i)}}function RS(e,n){switch(e){case X.FullYear:return n.getFullYear();case X.Month:return n.getMonth();case X.Date:return n.getDate();case X.Hours:return n.getHours();case X.Minutes:return n.getMinutes();case X.Seconds:return n.getSeconds();case X.FractionalSeconds:return n.getMilliseconds();case X.Day:return n.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function se(e,n,t=je.Format,r=!1){return function(i,o){return NS(i,o,e,n,t,r)}}function NS(e,n,t,r,i,o){switch(t){case K.Months:return wS(n,i,r)[e.getMonth()];case K.Days:return bS(n,i,r)[e.getDay()];case K.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(o){let l=MS(n),d=_S(n,i,r),f=l.findIndex(p=>{if(Array.isArray(p)){let[h,D]=p,S=s>=h.hours&&a>=h.minutes,T=s<D.hours||s===D.hours&&a<D.minutes;if(h.hours<D.hours){if(S&&T)return!0}else if(S||T)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(f!==-1)return d[f]}return CS(n,i,r)[s<12?0:1];case K.Eras:return ES(n,r)[e.getFullYear()<=0?0:1];default:let c=t;throw new Error(`unexpected translation type ${c}`)}}function wa(e){return function(n,t,r){let i=-1*r,o=Ta(t,Cn.MinusSign),s=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case Yt.Short:return(i>=0?"+":"")+vt(s,2,o)+vt(Math.abs(i%60),2,o);case Yt.ShortGMT:return"GMT"+(i>=0?"+":"")+vt(s,1,o);case Yt.Long:return"GMT"+(i>=0?"+":"")+vt(s,2,o)+":"+vt(Math.abs(i%60),2,o);case Yt.Extended:return r===0?"Z":(i>=0?"+":"")+vt(s,2,o)+":"+vt(Math.abs(i%60),2,o);default:throw new Error(`Unknown zone width "${e}"`)}}}var OS=0,Ma=4;function kS(e){let n=Sa(e,OS,1).getDay();return Sa(e,0,1+(n<=Ma?Ma:Ma+7)-n)}function dD(e){let n=e.getDay(),t=n===0?-3:Ma-n;return Sa(e.getFullYear(),e.getMonth(),e.getDate()+t)}function dd(e,n=!1){return function(t,r){let i;if(n){let o=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,s=t.getDate();i=1+Math.floor((s+o)/7)}else{let o=dD(t),s=kS(o.getFullYear()),a=o.getTime()-s.getTime();i=1+Math.round(a/6048e5)}return vt(i,e,Ta(r,Cn.MinusSign))}}function Ea(e,n=!1){return function(t,r){let o=dD(t).getFullYear();return vt(o,e,Ta(r,Cn.MinusSign),n)}}var fd={};function PS(e){if(fd[e])return fd[e];let n;switch(e){case"G":case"GG":case"GGG":n=se(K.Eras,ne.Abbreviated);break;case"GGGG":n=se(K.Eras,ne.Wide);break;case"GGGGG":n=se(K.Eras,ne.Narrow);break;case"y":n=De(X.FullYear,1,0,!1,!0);break;case"yy":n=De(X.FullYear,2,0,!0,!0);break;case"yyy":n=De(X.FullYear,3,0,!1,!0);break;case"yyyy":n=De(X.FullYear,4,0,!1,!0);break;case"Y":n=Ea(1);break;case"YY":n=Ea(2,!0);break;case"YYY":n=Ea(3);break;case"YYYY":n=Ea(4);break;case"M":case"L":n=De(X.Month,1,1);break;case"MM":case"LL":n=De(X.Month,2,1);break;case"MMM":n=se(K.Months,ne.Abbreviated);break;case"MMMM":n=se(K.Months,ne.Wide);break;case"MMMMM":n=se(K.Months,ne.Narrow);break;case"LLL":n=se(K.Months,ne.Abbreviated,je.Standalone);break;case"LLLL":n=se(K.Months,ne.Wide,je.Standalone);break;case"LLLLL":n=se(K.Months,ne.Narrow,je.Standalone);break;case"w":n=dd(1);break;case"ww":n=dd(2);break;case"W":n=dd(1,!0);break;case"d":n=De(X.Date,1);break;case"dd":n=De(X.Date,2);break;case"c":case"cc":n=De(X.Day,1);break;case"ccc":n=se(K.Days,ne.Abbreviated,je.Standalone);break;case"cccc":n=se(K.Days,ne.Wide,je.Standalone);break;case"ccccc":n=se(K.Days,ne.Narrow,je.Standalone);break;case"cccccc":n=se(K.Days,ne.Short,je.Standalone);break;case"E":case"EE":case"EEE":n=se(K.Days,ne.Abbreviated);break;case"EEEE":n=se(K.Days,ne.Wide);break;case"EEEEE":n=se(K.Days,ne.Narrow);break;case"EEEEEE":n=se(K.Days,ne.Short);break;case"a":case"aa":case"aaa":n=se(K.DayPeriods,ne.Abbreviated);break;case"aaaa":n=se(K.DayPeriods,ne.Wide);break;case"aaaaa":n=se(K.DayPeriods,ne.Narrow);break;case"b":case"bb":case"bbb":n=se(K.DayPeriods,ne.Abbreviated,je.Standalone,!0);break;case"bbbb":n=se(K.DayPeriods,ne.Wide,je.Standalone,!0);break;case"bbbbb":n=se(K.DayPeriods,ne.Narrow,je.Standalone,!0);break;case"B":case"BB":case"BBB":n=se(K.DayPeriods,ne.Abbreviated,je.Format,!0);break;case"BBBB":n=se(K.DayPeriods,ne.Wide,je.Format,!0);break;case"BBBBB":n=se(K.DayPeriods,ne.Narrow,je.Format,!0);break;case"h":n=De(X.Hours,1,-12);break;case"hh":n=De(X.Hours,2,-12);break;case"H":n=De(X.Hours,1);break;case"HH":n=De(X.Hours,2);break;case"m":n=De(X.Minutes,1);break;case"mm":n=De(X.Minutes,2);break;case"s":n=De(X.Seconds,1);break;case"ss":n=De(X.Seconds,2);break;case"S":n=De(X.FractionalSeconds,1);break;case"SS":n=De(X.FractionalSeconds,2);break;case"SSS":n=De(X.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":n=wa(Yt.Short);break;case"ZZZZZ":n=wa(Yt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=wa(Yt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":n=wa(Yt.Long);break;default:return null}return fd[e]=n,n}function fD(e,n){e=e.replace(/:/g,"");let t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function FS(e,n){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+n),e}function jS(e,n,t){let r=t?-1:1,i=e.getTimezoneOffset(),o=fD(n,i);return FS(e,r*(o-i))}function LS(e){if(nD(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,s=1]=e.split("-").map(a=>+a);return Sa(i,o-1,s)}let t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let r;if(r=e.match(SS))return VS(r)}let n=new Date(e);if(!nD(n))throw new Error(`Unable to convert "${e}" into a date`);return n}function VS(e){let n=new Date(0),t=0,r=0,i=e[8]?n.setUTCFullYear:n.setFullYear,o=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-t,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(n,s,a,c,l),n}function nD(e){return e instanceof Date&&!isNaN(e.valueOf())}function xa(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[i,o]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(i.trim()===n)return decodeURIComponent(o)}return null}var hd=/\s+/,rD=[],s2=(()=>{class e{constructor(t,r){this._ngEl=t,this._renderer=r,this.initialClasses=rD,this.stateMap=new Map}set klass(t){this.initialClasses=t!=null?t.trim().split(hd):rD}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(hd):t}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let r of t)this._updateState(r,!0);else if(t!=null)for(let r of Object.keys(t))this._updateState(r,!!t[r]);this._applyStateDiff()}_updateState(t,r){let i=this.stateMap.get(t);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(t,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let r=t[0],i=t[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(t,r){t=t.trim(),t.length>0&&t.split(hd).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static{this.\u0275fac=function(r){return new(r||e)(u(m),u(vn))}}static{this.\u0275dir=H({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var pd=class{constructor(n,t,r,i){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},a2=(()=>{class e{set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}constructor(t,r,i){this._viewContainer=t,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;if(!this._differ&&t)if(0)try{}catch{}else this._differ=this._differs.find(t).create(this.ngForTrackBy)}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((i,o,s)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new pd(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(o===null?void 0:o);else if(o!==null){let a=r.get(o);r.move(a,s),iD(a,i)}});for(let i=0,o=r.length;i<o;i++){let a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}t.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);iD(o,i)})}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(u(We),u(_t),u(ld))}}static{this.\u0275dir=H({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function iD(e,n){e.context.$implicit=n.item}var Aa=(()=>{class e{constructor(t,r){this._viewContainer=t,this._context=new gd,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){oD("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){oD("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(u(We),u(_t))}}static{this.\u0275dir=H({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),gd=class{constructor(){this.$implicit=null,this.ngIf=null}};function oD(e,n){if(!!!(!n||n.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Fe(n)}'.`)}var wd=(()=>{class e{constructor(t){this._viewContainerRef=t,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,i):!1,get:(t,r,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,i)}})}static{this.\u0275fac=function(r){return new(r||e)(u(We))}}static{this.\u0275dir=H({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[nt]})}}return e})();function BS(e,n){return new R(2100,!1)}var US="mediumDate",$S=new A(""),HS=new A(""),c2=(()=>{class e{constructor(t,r,i){this.locale=t,this.defaultTimezone=r,this.defaultOptions=i}transform(t,r,i,o){if(t==null||t===""||t!==t)return null;try{let s=r??this.defaultOptions?.dateFormat??US,a=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return xS(t,s,o||this.locale,a)}catch(s){throw BS(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(u(ma,16),u($S,24),u(HS,24))}}static{this.\u0275pipe=Om({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var Ed=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ht({type:e})}static{this.\u0275inj=ft({})}}return e})(),Md="browser",zS="server";function GS(e){return e===Md}function Ra(e){return e===zS}var hD=(()=>{class e{static{this.\u0275prov=x({token:e,providedIn:"root",factory:()=>GS(v(St))?new md(v(Ie),window):new vd})}}return e})(),md=class{constructor(n,t){this.document=n,this.window=t,this.offset=()=>[0,0]}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n){this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){let t=WS(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){this.window.history.scrollRestoration=n}scrollToElement(n){let t=n.getBoundingClientRect(),r=t.left+this.window.pageXOffset,i=t.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(r-o[0],i-o[1])}};function WS(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),i=r.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(n)||o.querySelector(`[name="${n}"]`);if(s)return s}i=r.nextNode()}}return null}var vd=class{setOffset(n){}getScrollPosition(){return[0,0]}scrollToPosition(n){}scrollToAnchor(n){}setHistoryScrollRestoration(n){}},qr=class{};var Li=class{},ka=class{},Xt=class e{constructor(n){this.normalizedNames=new Map,this.lazyUpdate=null,n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let i=t.slice(0,r),o=i.toLowerCase(),s=t.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.setHeaderEntries(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let i=(n.op==="a"?this.headers.get(t):void 0)||[];i.push(...r),this.headers.set(t,i);break;case"d":let o=n.value;if(!o)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(o=>o.toString()),i=n.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(n,i)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Sd=class{encodeKey(n){return pD(n)}encodeValue(n){return pD(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function qS(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[n.decodeKey(i),""]:[n.decodeKey(i.slice(0,o)),n.decodeValue(i.slice(o+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var ZS=/%(\d[a-f0-9])/gi,QS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function pD(e){return encodeURIComponent(e).replace(ZS,(n,t)=>QS[t]??n)}function Oa(e){return`${e}`}var wn=class e{constructor(n={}){if(this.updates=null,this.cloneFrom=null,this.encoder=n.encoder||new Sd,n.fromString){if(n.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=qS(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],i=Array.isArray(r)?r.map(Oa):[Oa(r)];this.map.set(t,i)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let i=n[r];Array.isArray(i)?i.forEach(o=>{t.push({param:r,value:o,op:"a"})}):t.push({param:r,value:i,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(Oa(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],i=r.indexOf(Oa(n.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Td=class{constructor(){this.map=new Map}set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function YS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function gD(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function mD(e){return typeof Blob<"u"&&e instanceof Blob}function vD(e){return typeof FormData<"u"&&e instanceof FormData}function KS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var ji=class e{constructor(n,t,r,i){this.url=t,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=n.toUpperCase();let o;if(YS(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new Xt,this.context??=new Td,!this.params)this.params=new wn,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||gD(this.body)||mD(this.body)||vD(this.body)||KS(this.body)?this.body:this.body instanceof wn?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||vD(this.body)?null:mD(this.body)?this.body.type||null:gD(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof wn?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,i=n.responseType||this.responseType,o=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,c=n.reportProgress??this.reportProgress,l=n.headers||this.headers,d=n.params||this.params,f=n.context??this.context;return n.setHeaders!==void 0&&(l=Object.keys(n.setHeaders).reduce((p,h)=>p.set(h,n.setHeaders[h]),l)),n.setParams&&(d=Object.keys(n.setParams).reduce((p,h)=>p.set(h,n.setParams[h]),d)),new e(t,r,s,{params:d,headers:l,context:f,reportProgress:c,responseType:i,withCredentials:a,transferCache:o})}},En=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(En||{}),Vi=class{constructor(n,t=200,r="OK"){this.headers=n.headers||new Xt,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},Pa=class e extends Vi{constructor(n={}){super(n),this.type=En.ResponseHeader}clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Bi=class e extends Vi{constructor(n={}){super(n),this.type=En.Response,this.body=n.body!==void 0?n.body:null}clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},bn=class extends Vi{constructor(n){super(n,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},bD=200,XS=204;function _d(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var JS=(()=>{class e{constructor(t){this.handler=t}request(t,r,i={}){let o;if(t instanceof ji)o=t;else{let c;i.headers instanceof Xt?c=i.headers:c=new Xt(i.headers);let l;i.params&&(i.params instanceof wn?l=i.params:l=new wn({fromObject:i.params})),o=new ji(t,r,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:l,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=O(o).pipe(kt(c=>this.handler.handle(c)));if(t instanceof ji||i.observe==="events")return s;let a=s.pipe(Ae(c=>c instanceof Bi));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return a.pipe(B(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return a.pipe(B(c=>c.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new wn().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,i={}){return this.request("PATCH",t,_d(i,r))}post(t,r,i={}){return this.request("POST",t,_d(i,r))}put(t,r,i={}){return this.request("PUT",t,_d(i,r))}static{this.\u0275fac=function(r){return new(r||e)(N(Li))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),eT=/^\)\]\}',?\n/,tT="X-Request-URL";function yD(e){if(e.url)return e.url;let n=tT.toLocaleLowerCase();return e.headers.get(n)}var nT=(()=>{class e{constructor(){this.fetchImpl=v(xd,{optional:!0})?.fetch??((...t)=>globalThis.fetch(...t)),this.ngZone=v(g)}handle(t){return new W(r=>{let i=new AbortController;return this.doRequest(t,i.signal,r).then(Ad,o=>r.error(new bn({error:o}))),()=>i.abort()})}doRequest(t,r,i){return ve(this,null,function*(){let o=this.createRequestInit(t),s;try{let h=this.ngZone.runOutsideAngular(()=>this.fetchImpl(t.urlWithParams,b({signal:r},o)));rT(h),i.next({type:En.Sent}),s=yield h}catch(h){i.error(new bn({error:h,status:h.status??0,statusText:h.statusText,url:t.urlWithParams,headers:h.headers}));return}let a=new Xt(s.headers),c=s.statusText,l=yD(s)??t.urlWithParams,d=s.status,f=null;if(t.reportProgress&&i.next(new Pa({headers:a,status:d,statusText:c,url:l})),s.body){let h=s.headers.get("content-length"),D=[],S=s.body.getReader(),T=0,F,re,Z=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>ve(this,null,function*(){for(;;){let{done:Te,value:Ce}=yield S.read();if(Te)break;if(D.push(Ce),T+=Ce.length,t.reportProgress){re=t.responseType==="text"?(re??"")+(F??=new TextDecoder).decode(Ce,{stream:!0}):void 0;let Nt=()=>i.next({type:En.DownloadProgress,total:h?+h:void 0,loaded:T,partialText:re});Z?Z.run(Nt):Nt()}}}));let me=this.concatChunks(D,T);try{let Te=s.headers.get("Content-Type")??"";f=this.parseBody(t,me,Te)}catch(Te){i.error(new bn({error:Te,headers:new Xt(s.headers),status:s.status,statusText:s.statusText,url:yD(s)??t.urlWithParams}));return}}d===0&&(d=f?bD:0),d>=200&&d<300?(i.next(new Bi({body:f,headers:a,status:d,statusText:c,url:l})),i.complete()):i.error(new bn({error:f,headers:a,status:d,statusText:c,url:l}))})}parseBody(t,r,i){switch(t.responseType){case"json":let o=new TextDecoder().decode(r).replace(eT,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(t){let r={},i=t.withCredentials?"include":void 0;if(t.headers.forEach((o,s)=>r[o]=s.join(",")),t.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!t.headers.has("Content-Type")){let o=t.detectContentTypeHeader();o!==null&&(r["Content-Type"]=o)}return{body:t.serializeBody(),method:t.method,headers:r,credentials:i}}concatChunks(t,r){let i=new Uint8Array(r),o=0;for(let s of t)i.set(s,o),o+=s.length;return i}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),xd=class{};function Ad(){}function rT(e){e.then(Ad,Ad)}function wD(e,n){return n(e)}function iT(e,n){return(t,r)=>n.intercept(t,{handle:i=>e(i,r)})}function oT(e,n,t){return(r,i)=>tt(t,()=>n(r,o=>e(o,i)))}var sT=new A(""),Rd=new A(""),aT=new A(""),ED=new A("",{providedIn:"root",factory:()=>!0});function cT(){let e=null;return(n,t)=>{e===null&&(e=(v(sT,{optional:!0})??[]).reduceRight(iT,wD));let r=v(Gt);if(v(ED)){let o=r.add();return e(n,t).pipe(cn(()=>r.remove(o)))}else return e(n,t)}}var DD=(()=>{class e extends Li{constructor(t,r){super(),this.backend=t,this.injector=r,this.chain=null,this.pendingTasks=v(Gt),this.contributeToStability=v(ED)}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Rd),...this.injector.get(aT,[])]));this.chain=r.reduceRight((i,o)=>oT(i,o,this.injector),wD)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,i=>this.backend.handle(i)).pipe(cn(()=>this.pendingTasks.remove(r)))}else return this.chain(t,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(N(ka),N(ue))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();var lT=/^\)\]\}',?\n/;function uT(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var ID=(()=>{class e{constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new R(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ce(r.\u0275loadImpl()):O(null)).pipe(we(()=>new W(o=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((S,T)=>s.setRequestHeader(S,T.join(","))),t.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){let S=t.detectContentTypeHeader();S!==null&&s.setRequestHeader("Content-Type",S)}if(t.responseType){let S=t.responseType.toLowerCase();s.responseType=S!=="json"?S:"text"}let a=t.serializeBody(),c=null,l=()=>{if(c!==null)return c;let S=s.statusText||"OK",T=new Xt(s.getAllResponseHeaders()),F=uT(s)||t.url;return c=new Pa({headers:T,status:s.status,statusText:S,url:F}),c},d=()=>{let{headers:S,status:T,statusText:F,url:re}=l(),Z=null;T!==XS&&(Z=typeof s.response>"u"?s.responseText:s.response),T===0&&(T=Z?bD:0);let me=T>=200&&T<300;if(t.responseType==="json"&&typeof Z=="string"){let Te=Z;Z=Z.replace(lT,"");try{Z=Z!==""?JSON.parse(Z):null}catch(Ce){Z=Te,me&&(me=!1,Z={error:Ce,text:Z})}}me?(o.next(new Bi({body:Z,headers:S,status:T,statusText:F,url:re||void 0})),o.complete()):o.error(new bn({error:Z,headers:S,status:T,statusText:F,url:re||void 0}))},f=S=>{let{url:T}=l(),F=new bn({error:S,status:s.status||0,statusText:s.statusText||"Unknown Error",url:T||void 0});o.error(F)},p=!1,h=S=>{p||(o.next(l()),p=!0);let T={type:En.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(T.total=S.total),t.responseType==="text"&&s.responseText&&(T.partialText=s.responseText),o.next(T)},D=S=>{let T={type:En.UploadProgress,loaded:S.loaded};S.lengthComputable&&(T.total=S.total),o.next(T)};return s.addEventListener("load",d),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),t.reportProgress&&(s.addEventListener("progress",h),a!==null&&s.upload&&s.upload.addEventListener("progress",D)),s.send(a),o.next({type:En.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",d),s.removeEventListener("timeout",f),t.reportProgress&&(s.removeEventListener("progress",h),a!==null&&s.upload&&s.upload.removeEventListener("progress",D)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(N(qr))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),MD=new A(""),dT="XSRF-TOKEN",fT=new A("",{providedIn:"root",factory:()=>dT}),hT="X-XSRF-TOKEN",pT=new A("",{providedIn:"root",factory:()=>hT}),Fa=class{},gT=(()=>{class e{constructor(t,r,i){this.doc=t,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=xa(t,this.cookieName),this.lastCookieString=t),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(N(Ie),N(St),N(fT))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function mT(e,n){let t=e.url.toLowerCase();if(!v(MD)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=v(Fa).getToken(),i=v(pT);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),n(e)}var _D=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(_D||{});function vT(e,n){return{\u0275kind:e,\u0275providers:n}}function C2(...e){let n=[JS,ID,DD,{provide:Li,useExisting:DD},{provide:ka,useFactory:()=>v(nT,{optional:!0})??v(ID)},{provide:Rd,useValue:mT,multi:!0},{provide:MD,useValue:!0},{provide:Fa,useClass:gT}];for(let t of e)n.push(...t.\u0275providers);return Ti(n)}var CD=new A("");function b2(){return vT(_D.LegacyInterceptors,[{provide:CD,useFactory:cT},{provide:Rd,useExisting:CD,multi:!0}])}var kd=class extends _a{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Pd=class e extends kd{static makeCurrent(){aD(new e)}onAndCancel(n,t,r){return n.addEventListener(t,r),()=>{n.removeEventListener(t,r)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=yT();return t==null?null:DT(t)}resetBaseElement(){Ui=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return xa(document.cookie,n)}},Ui=null;function yT(){return Ui=Ui||document.querySelector("base"),Ui?Ui.getAttribute("href"):null}function DT(e){return new URL(e,document.baseURI).pathname}var IT=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Fd=new A(""),AD=(()=>{class e{constructor(t,r){this._zone=r,this._eventNameToPlugin=new Map,t.forEach(i=>{i.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,i){return this._findPluginFor(r).addEventListener(t,r,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(o=>o.supports(t)),!r)throw new R(5101,!1);return this._eventNameToPlugin.set(t,r),r}static{this.\u0275fac=function(r){return new(r||e)(N(Fd),N(g))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),ja=class{constructor(n){this._doc=n}},Nd="ng-app-id",RD=(()=>{class e{constructor(t,r,i,o={}){this.doc=t,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Ra(o),this.resetHostNodes()}addStyles(t){for(let r of t)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(t){for(let r of t)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let t=this.styleNodesInDOM;t&&(t.forEach(r=>r.remove()),t.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(t){this.hostNodes.add(t);for(let r of this.getAllStyles())this.addStyleToHost(t,r)}removeHost(t){this.hostNodes.delete(t)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(t){for(let r of this.hostNodes)this.addStyleToHost(r,t)}onStyleRemoved(t){let r=this.styleRef;r.get(t)?.elements?.forEach(i=>i.remove()),r.delete(t)}collectServerRenderedStyles(){let t=this.doc.head?.querySelectorAll(`style[${Nd}="${this.appId}"]`);if(t?.length){let r=new Map;return t.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(t,r){let i=this.styleRef;if(i.has(t)){let o=i.get(t);return o.usage+=r,o.usage}return i.set(t,{usage:r,elements:[]}),r}getStyleElement(t,r){let i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===t)return i.delete(r),o.removeAttribute(Nd),o;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(Nd,this.appId),t.appendChild(s),s}}addStyleToHost(t,r){let i=this.getStyleElement(t,r),o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){let t=this.hostNodes;t.clear(),t.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(N(Ie),N(Vu),N(Uu,8),N(St))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Od={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Ld=/%COMP%/g,ND="%COMP%",CT=`_nghost-${ND}`,bT=`_ngcontent-${ND}`,wT=!0,ET=new A("",{providedIn:"root",factory:()=>wT});function MT(e){return bT.replace(Ld,e)}function _T(e){return CT.replace(Ld,e)}function OD(e,n){return n.map(t=>t.replace(Ld,e))}var SD=(()=>{class e{constructor(t,r,i,o,s,a,c,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=Ra(a),this.defaultRenderer=new $i(t,s,c,this.platformIsServer)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===wt.ShadowDom&&(r=V(b({},r),{encapsulation:wt.Emulated}));let i=this.getOrCreateRenderer(t,r);return i instanceof La?i.applyToHost(t):i instanceof Hi&&i.applyStyles(),i}getOrCreateRenderer(t,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,f=this.platformIsServer;switch(r.encapsulation){case wt.Emulated:o=new La(c,l,r,this.appId,d,s,a,f);break;case wt.ShadowDom:return new jd(c,l,t,r,s,a,this.nonce,f);default:o=new Hi(c,l,r,d,s,a,f);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(N(AD),N(RD),N(Vu),N(ET),N(Ie),N(St),N(g),N(Uu))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),$i=class{constructor(n,t,r,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(n,t){return t?this.doc.createElementNS(Od[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(TD(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(TD(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new R(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,i){if(i){t=i+":"+t;let o=Od[i];o?n.setAttributeNS(o,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let i=Od[r];i?n.removeAttributeNS(i,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,i){i&(Ut.DashCase|Ut.Important)?n.style.setProperty(t,r,i&Ut.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&Ut.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r){if(typeof n=="string"&&(n=Kt().getGlobalEventTarget(this.doc,n),!n))throw new Error(`Unsupported event target ${n} for event ${t}`);return this.eventManager.addEventListener(n,t,this.decoratePreventDefault(r))}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))===!1&&t.preventDefault()}}};function TD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var jd=class extends $i{constructor(n,t,r,i,o,s,a,c){super(n,o,s,c),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=OD(i.id,i.styles);for(let d of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=d,this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Hi=class extends $i{constructor(n,t,r,i,o,s,a,c){super(n,o,s,a),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=i,this.styles=c?OD(c,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},La=class extends Hi{constructor(n,t,r,i,o,s,a,c){let l=i+"-"+r.id;super(n,t,r,o,s,a,c,l),this.contentAttr=MT(l),this.hostAttr=_T(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}},ST=(()=>{class e extends ja{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,i){return t.addEventListener(r,i,!1),()=>this.removeEventListener(t,r,i)}removeEventListener(t,r,i){return t.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),xD=["alt","control","meta","shift"],TT={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},xT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},AT=(()=>{class e extends ja{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,i){let o=e.parseEventName(r),s=e.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Kt().onAndCancel(t,o.domEventName,s))}static parseEventName(t){let r=t.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),xD.forEach(l=>{let d=r.indexOf(l);d>-1&&(r.splice(d,1),s+=l+".")}),s+=o,r.length!=0||o.length===0)return null;let c={};return c.domEventName=i,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let i=TT[t.key]||t.key,o="";return r.indexOf("code.")>-1&&(i=t.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),xD.forEach(s=>{if(s!==i){let a=xT[s];a(t)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(t,r,i){return o=>{e.matchEventFullKeyCode(o,t)&&i.runGuarded(()=>r(o))}}static _normalizeKey(t){return t==="esc"?"escape":t}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function q2(e,n){return Xy(b({rootComponent:e},RT(n)))}function RT(e){return{appProviders:[...FT,...e?.providers??[]],platformProviders:PT}}function NT(){Pd.makeCurrent()}function OT(){return new Bt}function kT(){return kv(document),document}var PT=[{provide:St,useValue:Md},{provide:Bu,useValue:NT,multi:!0},{provide:Ie,useFactory:kT,deps:[]}];var FT=[{provide:zs,useValue:"root"},{provide:Bt,useFactory:OT,deps:[]},{provide:Fd,useClass:ST,multi:!0,deps:[Ie,g,St]},{provide:Fd,useClass:AT,multi:!0,deps:[Ie]},SD,RD,AD,{provide:jr,useExisting:SD},{provide:qr,useClass:IT,deps:[]},[]];var kD=(()=>{class e{constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static{this.\u0275fac=function(r){return new(r||e)(N(Ie))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var j="primary",io=Symbol("RouteTitle"),Hd=class{constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Jr(e){return new Hd(e)}function LT(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=e[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:i}}function VT(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!At(e[t],n[t]))return!1;return!0}function At(e,n){let t=e?zd(e):void 0,r=n?zd(n):void 0;if(!t||!r||t.length!=r.length)return!1;let i;for(let o=0;o<t.length;o++)if(i=t[o],!GD(e[i],n[i]))return!1;return!0}function zd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function GD(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((i,o)=>r[o]===i)}else return e===n}function WD(e){return e.length>0?e[e.length-1]:null}function Sn(e){return Gc(e)?e:tr(e)?ce(Promise.resolve(e)):O(e)}var BT={exact:ZD,subset:QD},qD={exact:UT,subset:$T,ignored:()=>!0};function PD(e,n,t){return BT[t.paths](e.root,n.root,t.matrixParams)&&qD[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function UT(e,n){return At(e,n)}function ZD(e,n,t){if(!ir(e.segments,n.segments)||!Ua(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!ZD(e.children[r],n.children[r],t))return!1;return!0}function $T(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>GD(e[t],n[t]))}function QD(e,n,t){return YD(e,n,n.segments,t)}function YD(e,n,t,r){if(e.segments.length>t.length){let i=e.segments.slice(0,t.length);return!(!ir(i,t)||n.hasChildren()||!Ua(i,t,r))}else if(e.segments.length===t.length){if(!ir(e.segments,t)||!Ua(e.segments,t,r))return!1;for(let i in n.children)if(!e.children[i]||!QD(e.children[i],n.children[i],r))return!1;return!0}else{let i=t.slice(0,e.segments.length),o=t.slice(e.segments.length);return!ir(e.segments,i)||!Ua(e.segments,i,r)||!e.children[j]?!1:YD(e.children[j],n,o,r)}}function Ua(e,n,t){return n.every((r,i)=>qD[t](e[i].parameters,r.parameters))}var en=class{constructor(n=new J([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Jr(this.queryParams),this._queryParamMap}toString(){return GT.serialize(this)}},J=class{constructor(n,t){this.segments=n,this.children=t,this.parent=null,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return $a(this)}},rr=class{constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Jr(this.parameters),this._parameterMap}toString(){return XD(this)}};function HT(e,n){return ir(e,n)&&e.every((t,r)=>At(t.parameters,n[r].parameters))}function ir(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function zT(e,n){let t=[];return Object.entries(e.children).forEach(([r,i])=>{r===j&&(t=t.concat(n(i,r)))}),Object.entries(e.children).forEach(([r,i])=>{r!==j&&(t=t.concat(n(i,r)))}),t}var sr=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>new ei,providedIn:"root"})}}return e})(),ei=class{parse(n){let t=new Wd(n);return new en(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${zi(n.root,!0)}`,r=ZT(n.queryParams),i=typeof n.fragment=="string"?`#${WT(n.fragment)}`:"";return`${t}${r}${i}`}},GT=new ei;function $a(e){return e.segments.map(n=>XD(n)).join("/")}function zi(e,n){if(!e.hasChildren())return $a(e);if(n){let t=e.children[j]?zi(e.children[j],!1):"",r=[];return Object.entries(e.children).forEach(([i,o])=>{i!==j&&r.push(`${i}:${zi(o,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=zT(e,(r,i)=>i===j?[zi(e.children[j],!1)]:[`${i}:${zi(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[j]!=null?`${$a(e)}/${t[0]}`:`${$a(e)}/(${t.join("//")})`}}function KD(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Va(e){return KD(e).replace(/%3B/gi,";")}function WT(e){return encodeURI(e)}function Gd(e){return KD(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ha(e){return decodeURIComponent(e)}function FD(e){return Ha(e.replace(/\+/g,"%20"))}function XD(e){return`${Gd(e.path)}${qT(e.parameters)}`}function qT(e){return Object.entries(e).map(([n,t])=>`;${Gd(n)}=${Gd(t)}`).join("")}function ZT(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(i=>`${Va(t)}=${Va(i)}`).join("&"):`${Va(t)}=${Va(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var QT=/^[^\/()?;#]+/;function Vd(e){let n=e.match(QT);return n?n[0]:""}var YT=/^[^\/()?;=#]+/;function KT(e){let n=e.match(YT);return n?n[0]:""}var XT=/^[^=?&#]+/;function JT(e){let n=e.match(XT);return n?n[0]:""}var ex=/^[^&#]+/;function tx(e){let n=e.match(ex);return n?n[0]:""}var Wd=class{constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new J([],{}):new J([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[j]=new J(n,t)),r}parseSegment(){let n=Vd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new R(4009,!1);return this.capture(n),new rr(Ha(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=KT(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let i=Vd(this.remaining);i&&(r=i,this.capture(r))}n[Ha(t)]=Ha(r)}parseQueryParam(n){let t=JT(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=tx(this.remaining);s&&(r=s,this.capture(r))}let i=FD(t),o=FD(r);if(n.hasOwnProperty(i)){let s=n[i];Array.isArray(s)||(s=[s],n[i]=s),s.push(o)}else n[i]=o}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Vd(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new R(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):n&&(o=j);let s=this.parseChildren();t[o]=Object.keys(s).length===1?s[j]:new J([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new R(4011,!1)}};function JD(e){return e.segments.length>0?new J([],{[j]:e}):e}function eI(e){let n={};for(let[r,i]of Object.entries(e.children)){let o=eI(i);if(r===j&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))n[s]=a;else(o.segments.length>0||o.hasChildren())&&(n[r]=o)}let t=new J(e.segments,n);return nx(t)}function nx(e){if(e.numberOfChildren===1&&e.children[j]){let n=e.children[j];return new J(e.segments.concat(n.segments),n.children)}return e}function or(e){return e instanceof en}function rx(e,n,t=null,r=null){let i=tI(e);return nI(i,n,t,r)}function tI(e){let n;function t(o){let s={};for(let c of o.children){let l=t(c);s[c.outlet]=l}let a=new J(o.url,s);return o===e&&(n=a),a}let r=t(e.root),i=JD(r);return n??i}function nI(e,n,t,r){let i=e;for(;i.parent;)i=i.parent;if(n.length===0)return Bd(i,i,i,t,r);let o=ix(n);if(o.toRoot())return Bd(i,i,new J([],{}),t,r);let s=ox(o,i,e),a=s.processChildren?qi(s.segmentGroup,s.index,o.commands):iI(s.segmentGroup,s.index,o.commands);return Bd(i,s.segmentGroup,a,t,r)}function za(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Yi(e){return typeof e=="object"&&e!=null&&e.outlets}function Bd(e,n,t,r,i){let o={};r&&Object.entries(r).forEach(([c,l])=>{o[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;e===n?s=t:s=rI(e,n,t);let a=JD(eI(s));return new en(a,o,i)}function rI(e,n,t){let r={};return Object.entries(e.children).forEach(([i,o])=>{o===n?r[i]=t:r[i]=rI(o,n,t)}),new J(e.segments,r)}var Ga=class{constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&za(r[0]))throw new R(4003,!1);let i=r.find(Yi);if(i&&i!==WD(r))throw new R(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ix(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Ga(!0,0,e);let n=0,t=!1,r=e.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new Ga(t,n,r)}var Yr=class{constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function ox(e,n,t){if(e.isAbsolute)return new Yr(n,!0,0);if(!t)return new Yr(n,!1,NaN);if(t.parent===null)return new Yr(t,!0,0);let r=za(e.commands[0])?0:1,i=t.segments.length-1+r;return sx(t,i,e.numberOfDoubleDots)}function sx(e,n,t){let r=e,i=n,o=t;for(;o>i;){if(o-=i,r=r.parent,!r)throw new R(4005,!1);i=r.segments.length}return new Yr(r,!1,i-o)}function ax(e){return Yi(e[0])?e[0].outlets:{[j]:e}}function iI(e,n,t){if(e??=new J([],{}),e.segments.length===0&&e.hasChildren())return qi(e,n,t);let r=cx(e,n,t),i=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let o=new J(e.segments.slice(0,r.pathIndex),{});return o.children[j]=new J(e.segments.slice(r.pathIndex),e.children),qi(o,0,i)}else return r.match&&i.length===0?new J(e.segments,{}):r.match&&!e.hasChildren()?qd(e,n,t):r.match?qi(e,0,i):qd(e,n,t)}function qi(e,n,t){if(t.length===0)return new J(e.segments,{});{let r=ax(t),i={};if(Object.keys(r).some(o=>o!==j)&&e.children[j]&&e.numberOfChildren===1&&e.children[j].segments.length===0){let o=qi(e.children[j],n,t);return new J(e.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=iI(e.children[o],n,s))}),Object.entries(e.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new J(e.segments,i)}}function cx(e,n,t){let r=0,i=n,o={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(r>=t.length)return o;let s=e.segments[i],a=t[r];if(Yi(a))break;let c=`${a}`,l=r<t.length-1?t[r+1]:null;if(i>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!LD(c,l,s))return o;r+=2}else{if(!LD(c,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function qd(e,n,t){let r=e.segments.slice(0,n),i=0;for(;i<t.length;){let o=t[i];if(Yi(o)){let c=lx(o.outlets);return new J(r,c)}if(i===0&&za(t[0])){let c=e.segments[n];r.push(new rr(c.path,jD(t[0]))),i++;continue}let s=Yi(o)?o.outlets[j]:`${o}`,a=i<t.length-1?t[i+1]:null;s&&a&&za(a)?(r.push(new rr(s,jD(a))),i+=2):(r.push(new rr(s,{})),i++)}return new J(r,{})}function lx(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=qd(new J([],{}),0,r))}),n}function jD(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function LD(e,n,t){return e==t.path&&At(n,t.parameters)}var Zi="imperative",Se=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Se||{}),st=class{constructor(n,t){this.id=n,this.url=t}},Mn=class extends st{constructor(n,t,r="imperative",i=null){super(n,t),this.type=Se.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Rt=class extends st{constructor(n,t,r){super(n,t),this.urlAfterRedirects=r,this.type=Se.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ye=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Ye||{}),Wa=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Wa||{}),Jt=class extends st{constructor(n,t,r,i){super(n,t),this.reason=r,this.code=i,this.type=Se.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},_n=class extends st{constructor(n,t,r,i){super(n,t),this.reason=r,this.code=i,this.type=Se.NavigationSkipped}},Ki=class extends st{constructor(n,t,r,i){super(n,t),this.error=r,this.target=i,this.type=Se.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},qa=class extends st{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Zd=class extends st{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Qd=class extends st{constructor(n,t,r,i,o){super(n,t),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o,this.type=Se.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Yd=class extends st{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Kd=class extends st{constructor(n,t,r,i){super(n,t),this.urlAfterRedirects=r,this.state=i,this.type=Se.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xd=class{constructor(n){this.route=n,this.type=Se.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Jd=class{constructor(n){this.route=n,this.type=Se.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ef=class{constructor(n){this.snapshot=n,this.type=Se.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},tf=class{constructor(n){this.snapshot=n,this.type=Se.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},nf=class{constructor(n){this.snapshot=n,this.type=Se.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},rf=class{constructor(n){this.snapshot=n,this.type=Se.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Za=class{constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r,this.type=Se.Scroll}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},Xi=class{},ti=class{constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function ux(e,n){return e.providers&&!e._injector&&(e._injector=la(e.providers,n,`Route: ${e.path}`)),e._injector??n}function Dt(e){return e.outlet||j}function dx(e,n){let t=e.filter(r=>Dt(r)===n);return t.push(...e.filter(r=>Dt(r)!==n)),t}function oo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var of=class{get injector(){return oo(this.route?.snapshot)??this.rootInjector}set injector(n){}constructor(n){this.rootInjector=n,this.outlet=null,this.route=null,this.children=new Tn(this.rootInjector),this.attachRef=null}},Tn=(()=>{class e{constructor(t){this.rootInjector=t,this.contexts=new Map}onChildOutletCreated(t,r){let i=this.getOrCreateContext(t);i.outlet=r,this.contexts.set(t,i)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new of(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static{this.\u0275fac=function(r){return new(r||e)(N(ue))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Qa=class{constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=sf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=sf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=af(n,this._root);return t.length<2?[]:t[t.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return af(n,this._root).map(t=>t.value)}};function sf(e,n){if(e===n.value)return n;for(let t of n.children){let r=sf(e,t);if(r)return r}return null}function af(e,n){if(e===n.value)return[n];for(let t of n.children){let r=af(e,t);if(r.length)return r.unshift(n),r}return[]}var Qe=class{constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Qr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var Ya=class extends Qa{constructor(n,t){super(n),this.snapshot=t,mf(this,n)}toString(){return this.snapshot.toString()}};function oI(e){let n=fx(e),t=new ye([new rr("",{})]),r=new ye({}),i=new ye({}),o=new ye({}),s=new ye(""),a=new Le(t,r,o,s,i,j,e,n.root);return a.snapshot=n.root,new Ya(new Qe(a,[]),n)}function fx(e){let n={},t={},r={},i="",o=new Kr([],n,r,i,t,j,e,null,{});return new Xa("",new Qe(o,[]))}var Le=class{constructor(n,t,r,i,o,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(B(l=>l[io]))??O(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(B(n=>Jr(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(B(n=>Jr(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Ka(e,n,t="emptyOnly"){let r,{routeConfig:i}=e;return n!==null&&(t==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:b(b({},n.params),e.params),data:b(b({},n.data),e.data),resolve:b(b(b(b({},e.data),n.data),i?.data),e._resolvedData)}:r={params:b({},e.params),data:b({},e.data),resolve:b(b({},e.data),e._resolvedData??{})},i&&aI(i)&&(r.resolve[io]=i.title),r}var Kr=class{get title(){return this.data?.[io]}constructor(n,t,r,i,o,s,a,c,l){this.url=n,this.params=t,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Jr(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Jr(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Xa=class extends Qa{constructor(n,t){super(t),this.url=n,mf(this,t)}toString(){return sI(this._root)}};function mf(e,n){n.value._routerState=e,n.children.forEach(t=>mf(e,t))}function sI(e){let n=e.children.length>0?` { ${e.children.map(sI).join(", ")} } `:"";return`${e.value}${n}`}function Ud(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,At(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),At(n.params,t.params)||e.paramsSubject.next(t.params),VT(n.url,t.url)||e.urlSubject.next(t.url),At(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function cf(e,n){let t=At(e.params,n.params)&&HT(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||cf(e.parent,n.parent))}function aI(e){return typeof e.title=="string"||e.title===null}var hx=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=j,this.activateEvents=new ee,this.deactivateEvents=new ee,this.attachEvents=new ee,this.detachEvents=new ee,this.parentContexts=v(Tn),this.location=v(We),this.changeDetector=v(y),this.inputBinder=v(nc,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:i}=t.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new R(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new R(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new R(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new R(4013,!1);this._activatedRoute=t;let i=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new lf(t,a,i.injector);this.activated=i.createComponent(s,{index:i.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[nt]})}}return e})(),lf=class e{__ngOutletInjector(n){return new e(this.route,this.childContexts,n)}constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Le?this.route:n===Tn?this.childContexts:this.parent.get(n,t)}},nc=new A(""),VD=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,i=jn([r.queryParams,r.params,r.data]).pipe(we(([o,s,a],c)=>(a=b(b(b({},o),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(o=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=va(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(t,i)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function px(e,n,t){let r=Ji(e,n._root,t?t._root:void 0);return new Ya(r,n)}function Ji(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let i=gx(e,n,t);return new Qe(r,i)}else{if(e.shouldAttach(n.value)){let o=e.retrieve(n.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Ji(e,a)),s}}let r=mx(n.value),i=n.children.map(o=>Ji(e,o));return new Qe(r,i)}}function gx(e,n,t){return n.children.map(r=>{for(let i of t.children)if(e.shouldReuseRoute(r.value,i.value.snapshot))return Ji(e,r,i);return Ji(e,r)})}function mx(e){return new Le(new ye(e.url),new ye(e.params),new ye(e.queryParams),new ye(e.fragment),new ye(e.data),e.outlet,e.component,e)}var eo=class{constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},cI="ngNavigationCancelingError";function Ja(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=or(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=lI(!1,Ye.Redirect);return i.url=t,i.navigationBehaviorOptions=r,i}function lI(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[cI]=!0,t.cancellationCode=n,t}function vx(e){return uI(e)&&or(e.url)}function uI(e){return!!e&&e[cI]}var yx=(e,n,t,r)=>B(i=>(new uf(n,i.targetRouterState,i.currentRouterState,t,r).activate(e),i)),uf=class{constructor(n,t,r,i,o){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),Ud(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let i=Qr(t);n.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(n,t,r){let i=n.value,o=t?t.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else o&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),i=r&&n.value.component?r.children:t,o=Qr(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),i=r&&n.value.component?r.children:t,o=Qr(n);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let i=Qr(t);n.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new rf(o.value.snapshot))}),n.children.length&&this.forwardEvent(new tf(n.value.snapshot))}activateRoutes(n,t,r){let i=n.value,o=t?t.value:null;if(Ud(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Ud(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},ec=class{constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Xr=class{constructor(n,t){this.component=n,this.route=t}};function Dx(e,n,t){let r=e._root,i=n?n._root:null;return Gi(r,i,t,[r.value])}function Ix(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function ri(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!Dm(e)?e:n.get(e):r}function Gi(e,n,t,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=Qr(n);return e.children.forEach(s=>{Cx(s,o[s.value.outlet],t,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>Qi(a,t.getContext(s),i)),i}function Cx(e,n,t,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let c=bx(s,o,o.routeConfig.runGuardsAndResolvers);c?i.canActivateChecks.push(new ec(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?Gi(e,n,a?a.children:null,r,i):Gi(e,n,t,r,i),c&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Xr(a.outlet.component,s))}else s&&Qi(n,a,i),i.canActivateChecks.push(new ec(r)),o.component?Gi(e,null,a?a.children:null,r,i):Gi(e,null,t,r,i);return i}function bx(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!ir(e.url,n.url);case"pathParamsOrQueryParamsChange":return!ir(e.url,n.url)||!At(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!cf(e,n)||!At(e.queryParams,n.queryParams);case"paramsChange":default:return!cf(e,n)}}function Qi(e,n,t){let r=Qr(e),i=e.value;Object.entries(r).forEach(([o,s])=>{i.component?n?Qi(s,n.children.getContext(o),t):Qi(s,null,t):Qi(s,n,t)}),i.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Xr(n.outlet.component,i)):t.canDeactivateChecks.push(new Xr(null,i)):t.canDeactivateChecks.push(new Xr(null,i))}function so(e){return typeof e=="function"}function wx(e){return typeof e=="boolean"}function Ex(e){return e&&so(e.canLoad)}function Mx(e){return e&&so(e.canActivate)}function _x(e){return e&&so(e.canActivateChild)}function Sx(e){return e&&so(e.canDeactivate)}function Tx(e){return e&&so(e.canMatch)}function dI(e){return e instanceof at||e?.name==="EmptyError"}var Ba=Symbol("INITIAL_VALUE");function ni(){return we(e=>jn(e.map(n=>n.pipe(Pt(1),Kc(Ba)))).pipe(B(n=>{for(let t of n)if(t!==!0){if(t===Ba)return Ba;if(t===!1||xx(t))return t}return!0}),Ae(n=>n!==Ba),Pt(1)))}function xx(e){return or(e)||e instanceof eo}function Ax(e,n){return fe(t=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=t;return s.length===0&&o.length===0?O(V(b({},t),{guardsResult:!0})):Rx(s,r,i,e).pipe(fe(a=>a&&wx(a)?Nx(r,o,e,n):O(a)),B(a=>V(b({},t),{guardsResult:a})))})}function Rx(e,n,t,r){return ce(e).pipe(fe(i=>jx(i.component,i.route,t,n,r)),It(i=>i!==!0,!0))}function Nx(e,n,t,r){return ce(n).pipe(kt(i=>Cr(kx(i.route.parent,r),Ox(i.route,r),Fx(e,i.path,t),Px(e,i.route,t))),It(i=>i!==!0,!0))}function Ox(e,n){return e!==null&&n&&n(new nf(e)),O(!0)}function kx(e,n){return e!==null&&n&&n(new ef(e)),O(!0)}function Px(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return O(!0);let i=r.map(o=>Qo(()=>{let s=oo(n)??t,a=ri(o,s),c=Mx(a)?a.canActivate(n,e):tt(s,()=>a(n,e));return Sn(c).pipe(It())}));return O(i).pipe(ni())}function Fx(e,n,t){let r=n[n.length-1],o=n.slice(0,n.length-1).reverse().map(s=>Ix(s)).filter(s=>s!==null).map(s=>Qo(()=>{let a=s.guards.map(c=>{let l=oo(s.node)??t,d=ri(c,l),f=_x(d)?d.canActivateChild(r,e):tt(l,()=>d(r,e));return Sn(f).pipe(It())});return O(a).pipe(ni())}));return O(o).pipe(ni())}function jx(e,n,t,r,i){let o=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!o||o.length===0)return O(!0);let s=o.map(a=>{let c=oo(n)??i,l=ri(a,c),d=Sx(l)?l.canDeactivate(e,n,t,r):tt(c,()=>l(e,n,t,r));return Sn(d).pipe(It())});return O(s).pipe(ni())}function Lx(e,n,t,r){let i=n.canLoad;if(i===void 0||i.length===0)return O(!0);let o=i.map(s=>{let a=ri(s,e),c=Ex(a)?a.canLoad(n,t):tt(e,()=>a(n,t));return Sn(c)});return O(o).pipe(ni(),fI(r))}function fI(e){return Uc(Ee(n=>{if(typeof n!="boolean")throw Ja(e,n)}),B(n=>n===!0))}function Vx(e,n,t,r){let i=n.canMatch;if(!i||i.length===0)return O(!0);let o=i.map(s=>{let a=ri(s,e),c=Tx(a)?a.canMatch(n,t):tt(e,()=>a(n,t));return Sn(c)});return O(o).pipe(ni(),fI(r))}var to=class{constructor(n){this.segmentGroup=n||null}},no=class extends Error{constructor(n){super(),this.urlTree=n}};function Zr(e){return yr(new to(e))}function Bx(e){return yr(new R(4e3,!1))}function Ux(e){return yr(lI(!1,Ye.GuardRejected))}var df=class{constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],i=t.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return O(r);if(i.numberOfChildren>1||!i.children[j])return Bx(`${n.redirectTo}`);i=i.children[j]}}applyRedirectCommands(n,t,r,i,o){if(typeof t!="string"){let a=t,{queryParams:c,fragment:l,routeConfig:d,url:f,outlet:p,params:h,data:D,title:S}=i,T=tt(o,()=>a({params:h,data:D,queryParams:c,fragment:l,routeConfig:d,url:f,outlet:p,title:S}));if(T instanceof en)throw new no(T);t=T}let s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if(t[0]==="/")throw new no(s);return s}applyRedirectCreateUrlTree(n,t,r,i){let o=this.createSegmentGroup(n,t.root,r,i);return new en(o,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=t[a]}else r[i]=o}),r}createSegmentGroup(n,t,r,i){let o=this.createSegments(n,t.segments,r,i),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,i)}),new J(o,s)}createSegments(n,t,r,i){return t.map(o=>o.path[0]===":"?this.findPosParam(n,o,i):this.findOrReturn(o,r))}findPosParam(n,t,r){let i=r[t.path.substring(1)];if(!i)throw new R(4001,!1);return i}findOrReturn(n,t){let r=0;for(let i of t){if(i.path===n.path)return t.splice(r),i;r++}return n}},ff={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function $x(e,n,t,r,i){let o=hI(e,n,t);return o.matched?(r=ux(n,r),Vx(r,n,t,i).pipe(B(s=>s===!0?o:b({},ff)))):O(o)}function hI(e,n,t){if(n.path==="**")return Hx(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?b({},ff):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let i=(n.matcher||LT)(t,e,n);if(!i)return b({},ff);let o={};Object.entries(i.posParams??{}).forEach(([a,c])=>{o[a]=c.path});let s=i.consumed.length>0?b(b({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:t.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function Hx(e){return{matched:!0,parameters:e.length>0?WD(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function BD(e,n,t,r){return t.length>0&&Wx(e,t,r)?{segmentGroup:new J(n,Gx(r,new J(t,e.children))),slicedSegments:[]}:t.length===0&&qx(e,t,r)?{segmentGroup:new J(e.segments,zx(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new J(e.segments,e.children),slicedSegments:t}}function zx(e,n,t,r){let i={};for(let o of t)if(rc(e,n,o)&&!r[Dt(o)]){let s=new J([],{});i[Dt(o)]=s}return b(b({},r),i)}function Gx(e,n){let t={};t[j]=n;for(let r of e)if(r.path===""&&Dt(r)!==j){let i=new J([],{});t[Dt(r)]=i}return t}function Wx(e,n,t){return t.some(r=>rc(e,n,r)&&Dt(r)!==j)}function qx(e,n,t){return t.some(r=>rc(e,n,r))}function rc(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function Zx(e,n,t){return n.length===0&&!e.children[t]}var hf=class{};function Qx(e,n,t,r,i,o,s="emptyOnly"){return new pf(e,n,t,r,i,s,o).recognize()}var Yx=31,pf=class{constructor(n,t,r,i,o,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new df(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(n){return new R(4002,`'${n.segmentGroup}'`)}recognize(){let n=BD(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(B(({children:t,rootSnapshot:r})=>{let i=new Qe(r,t),o=new Xa("",i),s=rx(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(n){let t=new Kr([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),j,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,j,t).pipe(B(r=>({children:r,rootSnapshot:t})),Ot(r=>{if(r instanceof no)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof to?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,o):this.processSegment(n,t,r,r.segments,i,!0,o).pipe(B(s=>s instanceof Qe?[s]:[]))}processChildren(n,t,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return ce(o).pipe(kt(s=>{let a=r.children[s],c=dx(t,s);return this.processSegmentGroup(n,c,a,s,i)}),Yc((s,a)=>(s.push(...a),s)),an(null),Qc(),fe(s=>{if(s===null)return Zr(r);let a=pI(s);return Kx(a),O(a)}))}processSegment(n,t,r,i,o,s,a){return ce(t).pipe(kt(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,i,o,s,a).pipe(Ot(l=>{if(l instanceof to)return O(null);throw l}))),It(c=>!!c),Ot(c=>{if(dI(c))return Zx(r,i,o)?O(new hf):Zr(r);throw c}))}processSegmentAgainstRoute(n,t,r,i,o,s,a,c){return Dt(r)!==s&&(s===j||!rc(i,o,r))?Zr(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,o,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,t,r,o,s,c):Zr(i)}expandSegmentAgainstRouteUsingRedirect(n,t,r,i,o,s,a){let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:f,remainingSegments:p}=hI(t,i,o);if(!c)return Zr(t);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Yx&&(this.allowRedirects=!1));let h=new Kr(o,l,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,UD(i),Dt(i),i.component??i._loadedComponent??null,i,$D(i)),D=Ka(h,a,this.paramsInheritanceStrategy);h.params=Object.freeze(D.params),h.data=Object.freeze(D.data);let S=this.applyRedirects.applyRedirectCommands(d,i.redirectTo,f,h,n);return this.applyRedirects.lineralizeSegments(i,S).pipe(fe(T=>this.processSegment(n,r,t,T.concat(p),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,i,o,s){let a=$x(t,r,i,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(we(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(we(({routes:l})=>{let d=r._loadedInjector??n,{parameters:f,consumedSegments:p,remainingSegments:h}=c,D=new Kr(p,f,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,UD(r),Dt(r),r.component??r._loadedComponent??null,r,$D(r)),S=Ka(D,s,this.paramsInheritanceStrategy);D.params=Object.freeze(S.params),D.data=Object.freeze(S.data);let{segmentGroup:T,slicedSegments:F}=BD(t,p,h,l);if(F.length===0&&T.hasChildren())return this.processChildren(d,l,T,D).pipe(B(Z=>new Qe(D,Z)));if(l.length===0&&F.length===0)return O(new Qe(D,[]));let re=Dt(r)===o;return this.processSegment(d,l,T,F,re?j:o,!0,D).pipe(B(Z=>new Qe(D,Z instanceof Qe?[Z]:[])))}))):Zr(t)))}getChildConfig(n,t,r){return t.children?O({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?O({routes:t._loadedRoutes,injector:t._loadedInjector}):Lx(n,t,r,this.urlSerializer).pipe(fe(i=>i?this.configLoader.loadChildren(n,t).pipe(Ee(o=>{t._loadedRoutes=o.routes,t._loadedInjector=o.injector})):Ux(t))):O({routes:[],injector:n})}};function Kx(e){e.sort((n,t)=>n.value.outlet===j?-1:t.value.outlet===j?1:n.value.outlet.localeCompare(t.value.outlet))}function Xx(e){let n=e.value.routeConfig;return n&&n.path===""}function pI(e){let n=[],t=new Set;for(let r of e){if(!Xx(r)){n.push(r);continue}let i=n.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),t.add(i)):n.push(r)}for(let r of t){let i=pI(r.children);n.push(new Qe(r.value,i))}return n.filter(r=>!t.has(r))}function UD(e){return e.data||{}}function $D(e){return e.resolve||{}}function Jx(e,n,t,r,i,o){return fe(s=>Qx(e,n,t,r,s.extractedUrl,i,o).pipe(B(({state:a,tree:c})=>V(b({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function eA(e,n){return fe(t=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=t;if(!i.length)return O(t);let o=new Set(i.map(c=>c.route)),s=new Set;for(let c of o)if(!s.has(c))for(let l of gI(c))s.add(l);let a=0;return ce(s).pipe(kt(c=>o.has(c)?tA(c,r,e,n):(c.data=Ka(c,c.parent,e).resolve,O(void 0))),Ee(()=>a++),br(1),fe(c=>a===s.size?O(t):He))})}function gI(e){let n=e.children.map(t=>gI(t)).flat();return[e,...n]}function tA(e,n,t,r){let i=e.routeConfig,o=e._resolve;return i?.title!==void 0&&!aI(i)&&(o[io]=i.title),nA(o,e,n,r).pipe(B(s=>(e._resolvedData=s,e.data=Ka(e,e.parent,t).resolve,null)))}function nA(e,n,t,r){let i=zd(e);if(i.length===0)return O({});let o={};return ce(i).pipe(fe(s=>rA(e[s],n,t,r).pipe(It(),Ee(a=>{if(a instanceof eo)throw Ja(new ei,a);o[s]=a}))),br(1),qc(o),Ot(s=>dI(s)?He:yr(s)))}function rA(e,n,t,r){let i=oo(n)??r,o=ri(e,i),s=o.resolve?o.resolve(n,t):tt(i,()=>o(n,t));return Sn(s)}function $d(e){return we(n=>{let t=e(n);return t?ce(t).pipe(B(()=>n)):O(n)})}var mI=(()=>{class e{buildTitle(t){let r,i=t.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===j);return r}getResolvedTitleForRoute(t){return t.data[io]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(iA),providedIn:"root"})}}return e})(),iA=(()=>{class e extends mI{constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(N(kD))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ao=new A("",{providedIn:"root",factory:()=>({})}),oA=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=I({type:e,selectors:[["ng-component"]],standalone:!0,features:[qy],decls:1,vars:0,template:function(r,i){r&1&&cd(0,"router-outlet")},dependencies:[hx],encapsulation:2})}}return e})();function vf(e){let n=e.children&&e.children.map(vf),t=n?V(b({},e),{children:n}):b({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==j&&(t.component=oA),t}var ro=new A(""),yf=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=v(ga)}loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return O(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=Sn(t.loadComponent()).pipe(B(vI),Ee(o=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=o}),cn(()=>{this.componentLoaders.delete(t)})),i=new mr(r,()=>new ae).pipe(gr());return this.componentLoaders.set(t,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return O({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=sA(r,this.compiler,t,this.onLoadEndListener).pipe(cn(()=>{this.childrenLoaders.delete(r)})),s=new mr(o,()=>new ae).pipe(gr());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function sA(e,n,t,r){return Sn(e.loadChildren()).pipe(B(vI),fe(i=>i instanceof Ei||Array.isArray(i)?O(i):ce(n.compileModuleAsync(i))),B(i=>{r&&r(e);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(t).injector,s=o.get(ro,[],{optional:!0,self:!0}).flat()),{routes:s.map(vf),injector:o}}))}function aA(e){return e&&typeof e=="object"&&"default"in e}function vI(e){return aA(e)?e.default:e}var Df=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(cA),providedIn:"root"})}}return e})(),cA=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),yI=new A(""),DI=new A("");function lA(e,n,t){let r=e.get(DI),i=e.get(Ie);return e.get(g).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let o,s=new Promise(l=>{o=l}),a=i.startViewTransition(()=>(o(),uA(e))),{onViewTransitionCreated:c}=r;return c&&tt(e,()=>c({transition:a,from:n,to:t})),s})}function uA(e){return new Promise(n=>{ad({read:()=>setTimeout(n)},{injector:e})})}var dA=new A(""),If=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new ae,this.transitionAbortSubject=new ae,this.configLoader=v(yf),this.environmentInjector=v(ue),this.urlSerializer=v(sr),this.rootContexts=v(Tn),this.location=v(yt),this.inputBindingEnabled=v(nc,{optional:!0})!==null,this.titleStrategy=v(mI),this.options=v(ao,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=v(Df),this.createViewTransition=v(yI,{optional:!0}),this.navigationErrorHandler=v(dA,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>O(void 0),this.rootComponentType=null;let t=i=>this.events.next(new Xd(i)),r=i=>this.events.next(new Jd(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(V(b(b({},this.transitions.value),t),{id:r}))}setupNavigations(t,r,i){return this.transitions=new ye({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Zi,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Ae(o=>o.id!==0),B(o=>V(b({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),we(o=>{let s=!1,a=!1;return O(o).pipe(we(c=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Ye.SupersededByNewNavigation),He;this.currentTransition=o,this.currentNavigation={id:c.id,initialUrl:c.rawUrl,extractedUrl:c.extractedUrl,targetBrowserUrl:typeof c.extras.browserUrl=="string"?this.urlSerializer.parse(c.extras.browserUrl):c.extras.browserUrl,trigger:c.source,extras:c.extras,previousNavigation:this.lastSuccessfulNavigation?V(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),d=c.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!l&&d!=="reload"){let f="";return this.events.next(new _n(c.id,this.urlSerializer.serialize(c.rawUrl),f,Wa.IgnoredSameUrlNavigation)),c.resolve(!1),He}if(this.urlHandlingStrategy.shouldProcessUrl(c.rawUrl))return O(c).pipe(we(f=>{let p=this.transitions?.getValue();return this.events.next(new Mn(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),p!==this.transitions?.getValue()?He:Promise.resolve(f)}),Jx(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),Ee(f=>{o.targetSnapshot=f.targetSnapshot,o.urlAfterRedirects=f.urlAfterRedirects,this.currentNavigation=V(b({},this.currentNavigation),{finalUrl:f.urlAfterRedirects});let p=new qa(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(p)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(c.currentRawUrl)){let{id:f,extractedUrl:p,source:h,restoredState:D,extras:S}=c,T=new Mn(f,this.urlSerializer.serialize(p),h,D);this.events.next(T);let F=oI(this.rootComponentType).snapshot;return this.currentTransition=o=V(b({},c),{targetSnapshot:F,urlAfterRedirects:p,extras:V(b({},S),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=p,O(o)}else{let f="";return this.events.next(new _n(c.id,this.urlSerializer.serialize(c.extractedUrl),f,Wa.IgnoredByUrlHandlingStrategy)),c.resolve(!1),He}}),Ee(c=>{let l=new Zd(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),B(c=>(this.currentTransition=o=V(b({},c),{guards:Dx(c.targetSnapshot,c.currentSnapshot,this.rootContexts)}),o)),Ax(this.environmentInjector,c=>this.events.next(c)),Ee(c=>{if(o.guardsResult=c.guardsResult,c.guardsResult&&typeof c.guardsResult!="boolean")throw Ja(this.urlSerializer,c.guardsResult);let l=new Qd(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot,!!c.guardsResult);this.events.next(l)}),Ae(c=>c.guardsResult?!0:(this.cancelNavigationTransition(c,"",Ye.GuardRejected),!1)),$d(c=>{if(c.guards.canActivateChecks.length)return O(c).pipe(Ee(l=>{let d=new Yd(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}),we(l=>{let d=!1;return O(l).pipe(eA(this.paramsInheritanceStrategy,this.environmentInjector),Ee({next:()=>d=!0,complete:()=>{d||this.cancelNavigationTransition(l,"",Ye.NoDataFromResolver)}}))}),Ee(l=>{let d=new Kd(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}))}),$d(c=>{let l=d=>{let f=[];d.routeConfig?.loadComponent&&!d.routeConfig._loadedComponent&&f.push(this.configLoader.loadComponent(d.routeConfig).pipe(Ee(p=>{d.component=p}),B(()=>{})));for(let p of d.children)f.push(...l(p));return f};return jn(l(c.targetSnapshot.root)).pipe(an(null),Pt(1))}),$d(()=>this.afterPreactivation()),we(()=>{let{currentSnapshot:c,targetSnapshot:l}=o,d=this.createViewTransition?.(this.environmentInjector,c.root,l.root);return d?ce(d).pipe(B(()=>o)):O(o)}),B(c=>{let l=px(t.routeReuseStrategy,c.targetSnapshot,c.currentRouterState);return this.currentTransition=o=V(b({},c),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,o}),Ee(()=>{this.events.next(new Xi)}),yx(this.rootContexts,t.routeReuseStrategy,c=>this.events.next(c),this.inputBindingEnabled),Pt(1),Ee({next:c=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Rt(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects))),this.titleStrategy?.updateTitle(c.targetRouterState.snapshot),c.resolve(!0)},complete:()=>{s=!0}}),Xc(this.transitionAbortSubject.pipe(Ee(c=>{throw c}))),cn(()=>{!s&&!a&&this.cancelNavigationTransition(o,"",Ye.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),Ot(c=>{if(a=!0,uI(c))this.events.next(new Jt(o.id,this.urlSerializer.serialize(o.extractedUrl),c.message,c.cancellationCode)),vx(c)?this.events.next(new ti(c.url,c.navigationBehaviorOptions)):o.resolve(!1);else{let l=new Ki(o.id,this.urlSerializer.serialize(o.extractedUrl),c,o.targetSnapshot??void 0);try{let d=tt(this.environmentInjector,()=>this.navigationErrorHandler?.(l));if(d instanceof eo){let{message:f,cancellationCode:p}=Ja(this.urlSerializer,d);this.events.next(new Jt(o.id,this.urlSerializer.serialize(o.extractedUrl),f,p)),this.events.next(new ti(d.redirectTo,d.navigationBehaviorOptions))}else{this.events.next(l);let f=t.errorHandler(c);o.resolve(!!f)}}catch(d){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(d)}}return He}))}))}cancelNavigationTransition(t,r,i){let o=new Jt(t.id,this.urlSerializer.serialize(t.extractedUrl),r,i);this.events.next(o),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function fA(e){return e!==Zi}var hA=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(pA),providedIn:"root"})}}return e})(),gf=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},pA=(()=>{class e extends gf{static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),II=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:()=>v(gA),providedIn:"root"})}}return e})(),gA=(()=>{class e extends II{constructor(){super(...arguments),this.location=v(yt),this.urlSerializer=v(sr),this.options=v(ao,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=v(Df),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new en,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=oI(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&t(r.url,r.state)})}handleRouterEvent(t,r){if(t instanceof Mn)this.stateMemento=this.createStateMemento();else if(t instanceof _n)this.rawUrlTree=r.initialUrl;else if(t instanceof qa){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else t instanceof Xi?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):t instanceof Jt&&(t.code===Ye.GuardRejected||t.code===Ye.NoDataFromResolver)?this.restoreHistory(r):t instanceof Ki?this.restoreHistory(r,!0):t instanceof Rt&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,r){let i=t instanceof en?this.urlSerializer.serialize(t):t;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let o=this.browserPageId,s=b(b({},r.extras.state),this.generateNgRouterState(r.id,o));this.location.replaceState(i,"",s)}else{let o=b(b({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",o)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.currentUrlTree===t.finalUrl&&o===0&&(this.resetState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(t),this.resetUrlToCurrentUrlTree())}resetState(t){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Wi=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(Wi||{});function CI(e,n){e.events.pipe(Ae(t=>t instanceof Rt||t instanceof Jt||t instanceof Ki||t instanceof _n),B(t=>t instanceof Rt||t instanceof _n?Wi.COMPLETE:(t instanceof Jt?t.code===Ye.Redirect||t.code===Ye.SupersededByNewNavigation:!1)?Wi.REDIRECTING:Wi.FAILED),Ae(t=>t!==Wi.REDIRECTING),Pt(1)).subscribe(()=>{n()})}function mA(e){throw e}var vA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},yA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},_e=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=v(pa),this.stateManager=v(II),this.options=v(ao,{optional:!0})||{},this.pendingTasks=v(Gt),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=v(If),this.urlSerializer=v(sr),this.location=v(yt),this.urlHandlingStrategy=v(Df),this._events=new ae,this.errorHandler=this.options.errorHandler||mA,this.navigated=!1,this.routeReuseStrategy=v(hA),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=v(ro,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!v(nc,{optional:!0}),this.eventsSubscription=new de,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof Jt&&r.code!==Ye.Redirect&&r.code!==Ye.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Rt)this.navigated=!0;else if(r instanceof ti){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c=b({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||fA(i.source)},s);this.scheduleNavigation(a,Zi,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}IA(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Zi,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(t,"popstate",r)},0)})}navigateToSyncWithBrowser(t,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let c=b({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(o.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(vf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,d=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":d=b(b({},this.currentUrlTree.queryParams),o);break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=o||null}d!==null&&(d=this.removeEmptyProps(d));let f;try{let p=i?i.snapshot:this.routerState.snapshot.root;f=tI(p)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),f=this.currentUrlTree.root}return nI(f,t,d,l??null)}navigateByUrl(t,r={skipLocationChange:!1}){let i=or(t)?t:this.parseUrl(t),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,Zi,null,r)}navigate(t,r={skipLocationChange:!1}){return DA(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let i;if(r===!0?i=b({},vA):r===!1?i=b({},yA):i=r,or(t))return PD(this.currentUrlTree,t,i);let o=this.parseUrl(t);return PD(this.currentUrlTree,o,i)}removeEmptyProps(t){return Object.entries(t).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(t,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((f,p)=>{a=f,c=p});let d=this.pendingTasks.add();return CI(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:o,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function DA(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new R(4008,!1)}function IA(e){return!(e instanceof Xi)&&!(e instanceof ti)}var Cf=(()=>{class e{constructor(t,r,i,o,s,a){this.router=t,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new ae,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=t.events.subscribe(l=>{l instanceof Rt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(or(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let r=this.href===null?null:zv(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(t,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,t,r):i.removeAttribute(o,t)}get urlTree(){return this.routerLinkInput===null?null:or(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)(u(_e),u(Le),zt("tabindex"),u(vn),u(m),u(Ze))}}static{this.\u0275dir=H({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Tt("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",In],skipLocationChange:[2,"skipLocationChange","skipLocationChange",In],replaceUrl:[2,"replaceUrl","replaceUrl",In],routerLink:"routerLink"},standalone:!0,features:[sd,nt]})}}return e})();var tc=class{},dU=(()=>{class e{preload(t,r){return r().pipe(Ot(()=>O(null)))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var CA=(()=>{class e{constructor(t,r,i,o,s){this.router=t,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Ae(t=>t instanceof Rt),kt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=la(o.providers,t,`Route: ${o.path}`));let s=o._injector??t,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return ce(i).pipe(Ir())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(t,r):i=O(null);let o=i.pipe(fe(s=>s===null?O(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ce([o,s]).pipe(Ir())}else return o})}static{this.\u0275fac=function(r){return new(r||e)(N(_e),N(ga),N(ue),N(tc),N(yf))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),bI=new A(""),bA=(()=>{class e{constructor(t,r,i,o,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Mn?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Rt?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof _n&&t.code===Wa.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Za&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Za(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){oy()}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function fU(e,...n){return Ti([{provide:ro,multi:!0,useValue:e},[],{provide:Le,useFactory:wI,deps:[_e]},{provide:Pi,multi:!0,useFactory:EI},n.map(t=>t.\u0275providers)])}function wI(e){return e.routerState.root}function ii(e,n){return{\u0275kind:e,\u0275providers:n}}function EI(){let e=v(le);return n=>{let t=e.get(xt);if(n!==t.components[0])return;let r=e.get(_e),i=e.get(MI);e.get(bf)===1&&r.initialNavigation(),e.get(_I,null,z.Optional)?.setUpPreloading(),e.get(bI,null,z.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var MI=new A("",{factory:()=>new ae}),bf=new A("",{providedIn:"root",factory:()=>1});function wA(){return ii(2,[{provide:bf,useValue:0},{provide:Wr,multi:!0,deps:[le],useFactory:n=>{let t=n.get(cD,Promise.resolve());return()=>t.then(()=>new Promise(r=>{let i=n.get(_e),o=n.get(MI);CI(i,()=>{r(!0)}),n.get(If).afterPreactivation=()=>(r(!0),o.closed?O(void 0):o),i.initialNavigation()}))}}])}function EA(){return ii(3,[{provide:Wr,multi:!0,useFactory:()=>{let n=v(_e);return()=>{n.setUpLocationChangeListener()}}},{provide:bf,useValue:2}])}var _I=new A("");function MA(e){return ii(0,[{provide:_I,useExisting:CA},{provide:tc,useExisting:e}])}function hU(){return ii(6,[{provide:Ze,useClass:bd}])}function _A(){return ii(8,[VD,{provide:nc,useExisting:VD}])}function SA(e){let n=[{provide:yI,useValue:lA},{provide:DI,useValue:b({skipNextTransition:!!e?.skipInitialTransition},e)}];return ii(9,n)}var HD=new A("ROUTER_FORROOT_GUARD"),TA=[yt,{provide:sr,useClass:ei},_e,Tn,{provide:Le,useFactory:wI,deps:[_e]},yf,[]],pU=(()=>{class e{constructor(t){}static forRoot(t,r){return{ngModule:e,providers:[TA,[],{provide:ro,multi:!0,useValue:t},{provide:HD,useFactory:NA,deps:[[_e,new Hs,new Cu]]},{provide:ao,useValue:r||{}},r?.useHash?AA():RA(),xA(),r?.preloadingStrategy?MA(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?OA(r):[],r?.bindToComponentInputs?_A().\u0275providers:[],r?.enableViewTransitions?SA().\u0275providers:[],kA()]}}static forChild(t){return{ngModule:e,providers:[{provide:ro,multi:!0,useValue:t}]}}static{this.\u0275fac=function(r){return new(r||e)(N(HD,8))}}static{this.\u0275mod=ht({type:e})}static{this.\u0275inj=ft({})}}return e})();function xA(){return{provide:bI,useFactory:()=>{let e=v(hD),n=v(g),t=v(ao),r=v(If),i=v(sr);return t.scrollOffset&&e.setOffset(t.scrollOffset),new bA(i,r,e,n,t)}}}function AA(){return{provide:Ze,useClass:bd}}function RA(){return{provide:Ze,useClass:Cd}}function NA(e){return"guarded"}function OA(e){return[e.initialNavigation==="disabled"?EA().\u0275providers:[],e.initialNavigation==="enabledBlocking"?wA().\u0275providers:[]]}var zD=new A("");function kA(){return[{provide:zD,useFactory:EI},{provide:Pi,multi:!0,useExisting:zD}]}var FI=(()=>{class e{constructor(t,r){this._renderer=t,this._elementRef=r,this.onChange=i=>{},this.onTouched=()=>{}}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static{this.\u0275fac=function(r){return new(r||e)(u(vn),u(m))}}static{this.\u0275dir=H({type:e})}}return e})(),PA=(()=>{class e extends FI{static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,features:[oe]})}}return e})(),cr=new A("");var FA={provide:cr,useExisting:et(()=>jI),multi:!0};function jA(){let e=Kt()?Kt().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var LA=new A(""),jI=(()=>{class e extends FI{constructor(t,r,i){super(t,r),this._compositionMode=i,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!jA())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static{this.\u0275fac=function(r){return new(r||e)(u(vn),u(m),u(LA,8))}}static{this.\u0275dir=H({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,i){r&1&&Me("input",function(s){return i._handleInput(s.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(s){return i._compositionEnd(s.target.value)})},features:[Ue([FA]),oe]})}}return e})();function po(e){return e==null||(typeof e=="string"||Array.isArray(e))&&e.length===0}var xn=new A(""),LI=new A("");function VA(e){return n=>{if(po(n.value)||po(e))return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function BA(e){return n=>{if(po(n.value)||po(e))return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function UA(e){return po(e.value)?{required:!0}:null}function TI(e){return null}function VI(e){return e!=null}function BI(e){return tr(e)?ce(e):e}function UI(e){let n={};return e.forEach(t=>{n=t!=null?b(b({},n),t):n}),Object.keys(n).length===0?null:n}function $I(e,n){return n.map(t=>t(e))}function $A(e){return!e.validate}function HI(e){return e.map(n=>$A(n)?n:t=>n.validate(t))}function HA(e){if(!e)return null;let n=e.filter(VI);return n.length==0?null:function(t){return UI($I(t,n))}}function Ef(e){return e!=null?HA(HI(e)):null}function zA(e){if(!e)return null;let n=e.filter(VI);return n.length==0?null:function(t){let r=$I(t,n).map(BI);return Wc(r).pipe(B(UI))}}function Mf(e){return e!=null?zA(HI(e)):null}function xI(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function GA(e){return e._rawValidators}function WA(e){return e._rawAsyncValidators}function wf(e){return e?Array.isArray(e)?e:[e]:[]}function oc(e,n){return Array.isArray(e)?e.includes(n):e===n}function AI(e,n){let t=wf(n);return wf(e).forEach(i=>{oc(t,i)||t.push(i)}),t}function RI(e,n){return wf(n).filter(t=>!oc(e,t))}var sc=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Ef(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Mf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},ai=class extends sc{get formDirective(){return null}get path(){return null}},ar=class extends sc{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},ac=class{constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},qA={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},RU=V(b({},qA),{"[class.ng-submitted]":"isSubmitted"}),NU=(()=>{class e extends ac{constructor(t){super(t)}static{this.\u0275fac=function(r){return new(r||e)(u(ar,2))}}static{this.\u0275dir=H({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,i){r&2&&ua("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},features:[oe]})}}return e})(),OU=(()=>{class e extends ac{constructor(t){super(t)}static{this.\u0275fac=function(r){return new(r||e)(u(ai,10))}}static{this.\u0275dir=H({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&ua("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[oe]})}}return e})();var co="VALID",ic="INVALID",oi="PENDING",lo="DISABLED",ci=class{},cc=class extends ci{constructor(n,t){super(),this.value=n,this.source=t}},fo=class extends ci{constructor(n,t){super(),this.pristine=n,this.source=t}},ho=class extends ci{constructor(n,t){super(),this.touched=n,this.source=t}},si=class extends ci{constructor(n,t){super(),this.status=n,this.source=t}};function zI(e){return(dc(e)?e.validators:e)||null}function ZA(e){return Array.isArray(e)?Ef(e):e||null}function GI(e,n){return(dc(n)?n.asyncValidators:e)||null}function QA(e){return Array.isArray(e)?Mf(e):e||null}function dc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function YA(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new R(1e3,"");if(!r[t])throw new R(1001,"")}function KA(e,n,t){e._forEachChild((r,i)=>{if(t[i]===void 0)throw new R(1002,"")})}var lc=class{constructor(n,t){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Fi(()=>this.statusReactive()),this.statusReactive=Ri(void 0),this._pristine=Fi(()=>this.pristineReactive()),this.pristineReactive=Ri(!0),this._touched=Fi(()=>this.touchedReactive()),this.touchedReactive=Ri(!1),this._events=new ae,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return qt(this.statusReactive)}set status(n){qt(()=>this.statusReactive.set(n))}get valid(){return this.status===co}get invalid(){return this.status===ic}get pending(){return this.status==oi}get disabled(){return this.status===lo}get enabled(){return this.status!==lo}get pristine(){return qt(this.pristineReactive)}set pristine(n){qt(()=>this.pristineReactive.set(n))}get dirty(){return!this.pristine}get touched(){return qt(this.touchedReactive)}set touched(n){qt(()=>this.touchedReactive.set(n))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(AI(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(AI(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(RI(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(RI(n,this._rawAsyncValidators))}hasValidator(n){return oc(this._rawValidators,n)}hasAsyncValidator(n){return oc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(V(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ho(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new ho(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(V(b({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new fo(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new fo(!0,r))}markAsPending(n={}){this.status=oi;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new si(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(V(b({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=lo,this.errors=null,this._forEachChild(i=>{i.disable(V(b({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new cc(this.value,r)),this._events.next(new si(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(V(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=co,this._forEachChild(r=>{r.enable(V(b({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(V(b({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===co||this.status===oi)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new cc(this.value,t)),this._events.next(new si(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(V(b({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?lo:co}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=oi,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let r=BI(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,i)=>r&&r._find(i),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new si(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new ee,this.statusChanges=new ee}_calculateStatus(){return this._allControlsDisabled()?lo:this.errors?ic:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(oi)?oi:this._anyControlsHaveStatus(ic)?ic:co}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),i&&this._events.next(new fo(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new ho(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){dc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=ZA(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=QA(this._rawAsyncValidators)}},uc=class extends lc{constructor(n,t,r){super(zI(t),GI(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){KA(this,!0,n),Object.keys(n).forEach(r=>{YA(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let i=this.controls[r];i&&i.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,i)=>{r.reset(n?n[i]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,i)=>((r.enabled||this.disabled)&&(t[i]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((i,o)=>{r=t(r,i,o)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var _f=new A("CallSetDisabledState",{providedIn:"root",factory:()=>Sf}),Sf="always";function XA(e,n){return[...n.path,e]}function WI(e,n,t=Sf){qI(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),eR(e,n),nR(e,n),tR(e,n),JA(e,n)}function NI(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function JA(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function qI(e,n){let t=GA(e);n.validator!==null?e.setValidators(xI(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=WA(e);n.asyncValidator!==null?e.setAsyncValidators(xI(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();NI(n._rawValidators,i),NI(n._rawAsyncValidators,i)}function eR(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&ZI(e,n)})}function tR(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&ZI(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function ZI(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function nR(e,n){let t=(r,i)=>{n.valueAccessor.writeValue(r),i&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function rR(e,n){e==null,qI(e,n)}function iR(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function oR(e){return Object.getPrototypeOf(e.constructor)===PA}function sR(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function aR(e,n){if(!n)return null;Array.isArray(n);let t,r,i;return n.forEach(o=>{o.constructor===jI?t=o:oR(o)?r=o:i=o}),i||r||t||null}var cR={provide:ai,useExisting:et(()=>lR)},uo=Promise.resolve(),lR=(()=>{class e extends ai{get submitted(){return qt(this.submittedReactive)}constructor(t,r,i){super(),this.callSetDisabledState=i,this._submitted=Fi(()=>this.submittedReactive()),this.submittedReactive=Ri(!1),this._directives=new Set,this.ngSubmit=new ee,this.form=new uc({},Ef(t),Mf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){uo.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),WI(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){uo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){uo.then(()=>{let r=this._findContainer(t.path),i=new uc({});rR(i,t),r.registerControl(t.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){uo.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){uo.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),sR(this.form,this._directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static{this.\u0275fac=function(r){return new(r||e)(u(xn,10),u(LI,10),u(_f,8))}}static{this.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&Me("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[Ue([cR]),oe]})}}return e})();function OI(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function kI(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var uR=class extends lc{constructor(n=null,t,r){super(zI(t),GI(r,t)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),dc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(kI(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){OI(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){OI(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){kI(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var dR={provide:ar,useExisting:et(()=>fR)},PI=Promise.resolve(),fR=(()=>{class e extends ar{constructor(t,r,i,o,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new uR,this._registered=!1,this.name="",this.update=new ee,this._parent=t,this._setValidators(r),this._setAsyncValidators(i),this.valueAccessor=aR(this,o)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),iR(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){WI(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){PI.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,i=r!==0&&In(r);PI.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?XA(t,this._parent):[t]}static{this.\u0275fac=function(r){return new(r||e)(u(ai,9),u(xn,10),u(LI,10),u(cr,10),u(y,8),u(_f,8))}}static{this.\u0275dir=H({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[Ue([dR]),oe,nt]})}}return e})(),PU=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();function QI(e){return typeof e=="number"?e:parseFloat(e)}var Tf=(()=>{class e{constructor(){this._validator=TI}ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):TI,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=H({type:e,features:[nt]})}}return e})(),hR={provide:xn,useExisting:et(()=>xf),multi:!0},xf=(()=>{class e extends Tf{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=t=>QI(t),this.createValidator=t=>BA(t)}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&Tt("max",i._enabled?i.max:null)},inputs:{max:"max"},features:[Ue([hR]),oe]})}}return e})(),pR={provide:xn,useExisting:et(()=>Af),multi:!0},Af=(()=>{class e extends Tf{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=t=>QI(t),this.createValidator=t=>VA(t)}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&Tt("min",i._enabled?i.min:null)},inputs:{min:"min"},features:[Ue([pR]),oe]})}}return e})(),gR={provide:xn,useExisting:et(()=>mR),multi:!0};var mR=(()=>{class e extends Tf{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=In,this.createValidator=t=>UA}enabled(t){return t}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})()}static{this.\u0275dir=H({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,i){r&2&&Tt("required",i._enabled?"":null)},inputs:{required:"required"},features:[Ue([gR]),oe]})}}return e})();var vR=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ht({type:e})}static{this.\u0275inj=ft({})}}return e})();var FU=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:_f,useValue:t.callSetDisabledState??Sf}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ht({type:e})}static{this.\u0275inj=ft({imports:[vR]})}}return e})();var VU=(e,n,t,r,i)=>DR(e[1],n[1],t[1],r[1],i).map(o=>yR(e[0],n[0],t[0],r[0],o)),yR=(e,n,t,r,i)=>{let o=3*n*Math.pow(i-1,2),s=-3*t*i+3*t+r*i,a=e*Math.pow(i-1,3);return i*(o+i*s)-a},DR=(e,n,t,r,i)=>(e-=i,n-=i,t-=i,r-=i,CR(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),IR=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},CR=(e,n,t,r)=>{if(e===0)return IR(n,t,r);n/=e,t/=e,r/=e;let i=(3*t-n*n)/3,o=(2*n*n*n-9*n*t+27*r)/27;if(i===0)return[Math.pow(-o,.3333333333333333)];if(o===0)return[Math.sqrt(-i),-Math.sqrt(-i)];let s=Math.pow(o/2,2)+Math.pow(i/3,3);if(s===0)return[Math.pow(o/2,.5)-n/3];if(s>0)return[Math.pow(-(o/2)+Math.sqrt(s),.3333333333333333)-Math.pow(o/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(i/3),3)),c=Math.acos(-(o/(2*Math.sqrt(Math.pow(-(i/3),3))))),l=2*Math.pow(a,1/3);return[l*Math.cos(c/3)-n/3,l*Math.cos((c+2*Math.PI)/3)-n/3,l*Math.cos((c+4*Math.PI)/3)-n/3]};var fc=e=>KI(e),ui=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),fc(e).includes(n)),KI=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=bR(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},bR=e=>{let n=$e.get("platform");return Object.keys(YI).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):YI[t](e)})},wR=e=>hc(e)&&!JI(e),Rf=e=>!!(lr(e,/iPad/i)||lr(e,/Macintosh/i)&&hc(e)),ER=e=>lr(e,/iPhone/i),MR=e=>lr(e,/iPhone|iPod/i)||Rf(e),XI=e=>lr(e,/android|sink/i),_R=e=>XI(e)&&!lr(e,/mobile/i),SR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),i=Math.max(n,t);return r>390&&r<520&&i>620&&i<800},TR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),i=Math.max(n,t);return Rf(e)||_R(e)||r>460&&r<820&&i>780&&i<1400},hc=e=>NR(e,"(any-pointer:coarse)"),xR=e=>!hc(e),JI=e=>e0(e)||t0(e),e0=e=>!!(e.cordova||e.phonegap||e.PhoneGap),t0=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},AR=e=>lr(e,/electron/i),RR=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},lr=(e,n)=>n.test(e.navigator.userAgent),NR=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},YI={ipad:Rf,iphone:ER,ios:MR,android:XI,phablet:SR,tablet:TR,cordova:e0,capacitor:t0,electron:AR,pwa:RR,mobile:hc,mobileweb:wR,desktop:xR,hybrid:JI},li,Nf=e=>e&&Np(e)||li,OR=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},i=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Op(t)),{persistConfig:!1}),r.config),Pp(t)),e);$e.reset(i),$e.getBoolean("persistConfig")&&kp(t,i),KI(t),r.config=$e,r.mode=li=$e.get("mode",n.documentElement.getAttribute("mode")||(ui(t,"ios")?"ios":"md")),$e.set("mode",li),n.documentElement.setAttribute("mode",li),n.documentElement.classList.add(li),$e.getBoolean("_testing")&&$e.set("animated",!1);let o=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);Rp(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;o(a)&&Io('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return li})};var GU=e=>{try{if(e instanceof Pf)return e.value;if(!kR()||typeof e!="string"||e==="")return e;if(e.includes("onload="))return"";let n=document.createDocumentFragment(),t=document.createElement("div");n.appendChild(t),t.innerHTML=e,FR.forEach(s=>{let a=n.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):n.removeChild(l);let d=kf(l);for(let f=0;f<d.length;f++)Of(d[f])}});let r=kf(n);for(let s=0;s<r.length;s++)Of(r[s]);let i=document.createElement("div");i.appendChild(n);let o=i.querySelector("div");return o!==null?o.innerHTML:i.innerHTML}catch(n){return Co("sanitizeDOMString",n),""}},Of=e=>{if(e.nodeType&&e.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(e.attributes instanceof NamedNodeMap)){e.remove();return}for(let t=e.attributes.length-1;t>=0;t--){let r=e.attributes.item(t),i=r.name;if(!PR.includes(i.toLowerCase())){e.removeAttribute(i);continue}let o=r.value,s=e[i];(o!=null&&o.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&e.removeAttribute(i)}let n=kf(e);for(let t=0;t<n.length;t++)Of(n[t])},kf=e=>e.children!=null?e.children:e.childNodes,kR=()=>{var e;let n=window,t=(e=n?.Ionic)===null||e===void 0?void 0:e.config;return t?t.get?t.get("sanitizerEnabled",!0):t.sanitizerEnabled===!0||t.sanitizerEnabled===void 0:!0},PR=["class","id","href","src","name","slot"],FR=["script","style","iframe","meta","link","object","embed"],Pf=class{constructor(n){this.value=n}};var WU=!1;var ZU=(e,n)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},n):n,jR=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],QU=e=>{let n={};return jR(e).forEach(t=>n[t]=!0),n};var JU=(e,n,t,r,i,o)=>ve(void 0,null,function*(){var s;if(e)return e.attachViewToDom(n,t,i,r);if(!o&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),i&&Object.assign(a,i),n.appendChild(a),yield new Promise(c=>nn(a,c)),a}),e$=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},n0=()=>{let e,n;return{attachViewToDom:(c,l,...d)=>ve(void 0,[c,l,...d],function*(i,o,s={},a=[]){var f,p;e=i;let h;if(o){let S=typeof o=="string"?(f=e.ownerDocument)===null||f===void 0?void 0:f.createElement(o):o;a.forEach(T=>S.classList.add(T)),Object.assign(S,s),e.appendChild(S),h=S,yield new Promise(T=>nn(S,T))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(h=e.children[0]).classList.contains("ion-delegate-host")){let T=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");T.classList.add("ion-delegate-host"),a.forEach(F=>T.classList.add(F)),T.append(...e.children),e.appendChild(T),h=T}let D=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),D.appendChild(e),h??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var mo='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',r0=(e,n)=>{let t=e.querySelector(mo);a0(t,n??e)},i0=(e,n)=>{let t=Array.from(e.querySelectorAll(mo)),r=t.length>0?t[t.length-1]:null;a0(r,n??e)},a0=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(mo)||e),t){let i=t.closest("ion-radio-group");i?i.setFocus():Cc(t)}else n.focus()},Ff=0,LR=0,pc=new WeakMap,jf=e=>({create(t){return $R(e,t)},dismiss(t,r,i){return WR(document,t,r,e,i)},getTop(){return ve(this,null,function*(){return go(document,e)})}});var VR=jf("ion-loading"),BR=jf("ion-modal");var UR=jf("ion-popover");var u$=e=>{typeof document<"u"&&GR(document);let n=Ff++;e.overlayIndex=n},d$=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++LR}`),e.id),$R=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),l0(document).appendChild(t),new Promise(r=>nn(t,r))}):Promise.resolve(),HR=e=>e.classList.contains("overlay-hidden"),o0=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(mo)||e),t?Cc(t):n.focus()},zR=(e,n)=>{let t=go(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(JR))return;let i=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")o0(t.lastFocus,t);else{let s=Lp(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;r0(a,t),c===n.activeElement&&i0(a,t),t.lastFocus=n.activeElement}}},o=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")o0(t.lastFocus,t);else{let s=t.lastFocus;r0(t),s===n.activeElement&&i0(t),t.lastFocus=n.activeElement}};t.shadowRoot?o():i()},GR=e=>{Ff===0&&(Ff=1,e.addEventListener("focus",n=>{zR(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=go(e);t?.backdropDismiss&&n.detail.register(Bp,()=>{t.dismiss(void 0,s0)})}),Vp()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=go(e);t?.backdropDismiss&&t.dismiss(void 0,s0)}}))},WR=(e,n,t,r,i)=>{let o=go(e,r,i);return o?o.dismiss(n,t):Promise.reject("overlay does not exist")},qR=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),gc=(e,n)=>qR(e,n).filter(t=>!HR(t)),go=(e,n,t)=>{let r=gc(e,n);return t===void 0?r[r.length-1]:r.find(i=>i.id===t)},c0=(e=!1)=>{let t=l0(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},f$=(e,n,t,r,i)=>ve(void 0,null,function*(){var o,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(c0(!0),document.body.classList.add(Sc)),KR(e.el),d0(e.el),e.presented=!0,e.willPresent.emit(),(o=e.willPresentShorthand)===null||o===void 0||o.emit();let a=Nf(e),c=e.enterAnimation?e.enterAnimation:$e.get(n,a==="ios"?t:r);(yield u0(e,c,e.el,i))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&ZR(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),ZR=e=>ve(void 0,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(mo)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),h$=(e,n,t,r,i,o,s)=>ve(void 0,null,function*(){var a,c;if(!e.presented)return!1;let d=(tn!==void 0?gc(tn):[]).filter(p=>p.tagName!=="ION-TOAST");d.length===1&&d[0].id===e.el.id&&(c0(!1),document.body.classList.remove(Sc)),e.presented=!1;try{d0(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let p=Nf(e),h=e.leaveAnimation?e.leaveAnimation:$e.get(r,p==="ios"?i:o);t!==YR&&(yield u0(e,h,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(pc.get(e)||[]).forEach(S=>S.destroy()),pc.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){Co(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),XR(),!0}),l0=e=>e.querySelector("ion-app")||e.body,u0=(e,n,t,r)=>ve(void 0,null,function*(){t.classList.remove("overlay-hidden");let i=e.el,o=n(i,r);(!e.animated||!$e.getBoolean("animated",!0))&&o.duration(0),e.keyboardClose&&o.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=pc.get(e)||[];return pc.set(e,[...s,o]),yield o.play(),!0}),p$=(e,n)=>{let t,r=new Promise(i=>t=i);return QR(e,n,i=>{t(i.detail)}),r},QR=(e,n,t)=>{let r=i=>{jp(e,n,r),t(i)};Fp(e,n,r)};var s0="backdrop",YR="gesture",g$=39,m$=e=>{let n=!1,t,r=n0(),i=(a=!1)=>{if(t&&!a)return{delegate:t,inline:n};let{el:c,hasController:l,delegate:d}=e;return n=c.parentNode!==null&&!l,t=n?d||r:d,{inline:n,delegate:t}};return{attachViewToDom:a=>ve(void 0,null,function*(){let{delegate:c}=i(!0);if(c)return yield c.attachViewToDom(e.el,a);let{hasController:l}=e;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=i();a&&e.el!==void 0&&a.removeViewFromDom(e.el.parentElement,e.el)}}},v$=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,i)=>{n();let o=i!==void 0?document.getElementById(i):null;if(!o){Io(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${i}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(o,r)},removeClickListener:n}},d0=e=>{tn!==void 0&&ui("android")&&e.setAttribute("aria-hidden","true")},KR=e=>{var n;if(tn===void 0)return;let t=gc(tn);for(let r=t.length-1;r>=0;r--){let i=t[r],o=(n=t[r+1])!==null&&n!==void 0?n:e;(o.hasAttribute("aria-hidden")||o.tagName!=="ION-TOAST")&&i.setAttribute("aria-hidden","true")}},XR=()=>{if(tn===void 0)return;let e=gc(tn);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},JR="ion-disable-focus-trap";var eN=["tabsInner"];var g0=(()=>{class e{doc;_readyPromise;win;backButton=new ae;keyboardDidShow=new ae;keyboardDidHide=new ae;pause=new ae;resume=new ae;resize=new ae;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(o,s){return this.subscribe(a=>a.register(o,c=>r.run(()=>s(c))))},di(this.pause,t,"pause",r),di(this.resume,t,"resume",r),di(this.backButton,t,"ionBackButton",r),di(this.resize,this.win,"resize",r),di(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),di(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let i;this._readyPromise=new Promise(o=>{i=o}),this.win?.cordova?t.addEventListener("deviceready",()=>{i("cordova")},{once:!0}):i("dom")})}is(t){return ui(this.win,t)}platforms(){return fc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return tN(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(N(Ie),N(g))};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),tN=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},di=(e,n,t,r)=>{n&&n.addEventListener(t,i=>{r.run(()=>{let o=i?.detail;e.next(o)})})},Rn=(()=>{class e{location;serializer;router;topOutlet;direction=f0;animated=h0;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,i,o){this.location=r,this.serializer=i,this.router=o,o&&o.events.subscribe(s=>{if(s instanceof Mn){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ve(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,i,o){this.direction=t,this.animated=nN(t,r,i),this.animationBuilder=o}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,i=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=f0,this.animated=h0,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:i}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let i=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(i.queryParams=b({},r.queryParams)),r.fragment!==void 0&&(i.fragment=r.fragment),this.router.navigateByUrl(i,r)}}static \u0275fac=function(r){return new(r||e)(N(g0),N(yt),N(sr),N(_e,8))};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),nN=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},f0="auto",h0=void 0,yo=(()=>{class e{get(t,r){let i=Lf();return i?i.get(t,r):null}getBoolean(t,r){let i=Lf();return i?i.getBoolean(t,r):!1}getNumber(t,r){let i=Lf();return i?i.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mc=new A("USERCONFIG"),Lf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},vo=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},Nn=(()=>{class e{zone=v(g);applicationRef=v(xt);config=v(mc);create(t,r,i){return new Bf(t,r,this.applicationRef,this.zone,i,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),Bf=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,i,o,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=i,this.elementReferenceKey=o,this.enableSignalsSupport=s}attachViewToDom(n,t,r,i){return this.zone.run(()=>new Promise(o=>{let s=b({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=rN(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,i,this.elementReferenceKey,this.enableSignalsSupport);o(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let i=this.elRefMap.get(t);if(i){i.destroy(),this.elRefMap.delete(t);let o=this.elEventsMap.get(t);o&&(o(),this.elEventsMap.delete(t))}r()}))}},rN=(e,n,t,r,i,o,s,a,c,l,d,f)=>{let p=le.create({providers:oN(c),parent:t}),h=Jy(a,{environmentInjector:n,elementInjector:p}),D=h.instance,S=h.location.nativeElement;if(c)if(d&&D[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),f===!0&&h.setInput!==void 0){let F=c,{modal:re,popover:Z}=F,me=Ic(F,["modal","popover"]);for(let Te in me)h.setInput(Te,me[Te]);re!==void 0&&Object.assign(D,{modal:re}),Z!==void 0&&Object.assign(D,{popover:Z})}else Object.assign(D,c);if(l)for(let re of l)S.classList.add(re);let T=m0(e,D,S);return s.appendChild(S),r.attachView(h.hostView),i.set(S,h),o.set(S,T),S},iN=[bc,wc,Ec,Mc,_c],m0=(e,n,t)=>e.run(()=>{let r=iN.filter(i=>typeof n[i]=="function").map(i=>{let o=s=>n[i](s.detail);return t.addEventListener(i,o),()=>t.removeEventListener(i,o)});return()=>r.forEach(i=>i())}),p0=new A("NavParamsToken"),oN=e=>[{provide:p0,useValue:e},{provide:vo,useFactory:sN,deps:[p0]}],sN=e=>new vo(e),aN=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(i){this.z.runOutsideAngular(()=>this.el[r]=i)}})})},cN=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,i))}})},Qf=(e,n,t)=>{t.forEach(r=>e[r]=Ln(n,r))};function vc(e){return function(t){let{defineCustomElementFn:r,inputs:i,methods:o}=e;return r!==void 0&&r(),i&&aN(t,i),o&&cN(t,o),t}}var lN=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],uN=["present","dismiss","onDidDismiss","onWillDismiss"],v0=(()=>{let e=class Uf{z;template;isCmpOpen=!1;el;constructor(t,r,i){this.z=i,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Qf(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Uf)(u(y),u(m),u(g))};static \u0275dir=H({type:Uf,selectors:[["ion-popover"]],contentQueries:function(r,i,o){if(r&1&&Dn(o,_t,5),r&2){let s;gt(s=mt())&&(i.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"}})};return e=w([vc({inputs:lN,methods:uN})],e),e})(),dN=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],fN=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],y0=(()=>{let e=class $f{z;template;isCmpOpen=!1;el;constructor(t,r,i){this.z=i,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Qf(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||$f)(u(y),u(m),u(g))};static \u0275dir=H({type:$f,selectors:[["ion-modal"]],contentQueries:function(r,i,o){if(r&1&&Dn(o,_t,5),r&2){let s;gt(s=mt())&&(i.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"}})};return e=w([vc({inputs:dN,methods:fN})],e),e})(),hN=(e,n,t)=>t==="root"?D0(e,n):t==="forward"?pN(e,n):gN(e,n),D0=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),pN=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),gN=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):D0(e,n),Hf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},I0=(e,n)=>n?e.stackId!==n.stackId:!0,mN=(e,n)=>{if(!e)return;let t=C0(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},C0=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),b0=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},zf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,i,o,s){this.containerEl=t,this.router=r,this.navCtrl=i,this.zone=o,this.location=s,this.tabsPrefix=n!==void 0?C0(n):void 0}createView(n,t){let r=Hf(this.router,t),i=n?.location?.nativeElement,o=m0(this.zone,n.instance,i);return{id:this.nextId++,stackId:mN(this.tabsPrefix,r),unlistenEvents:o,element:i,ref:n,url:r}}getExistingView(n){let t=Hf(this.router,n),r=this.views.find(i=>i.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:i,animationBuilder:o}=t,s=this.activeView,a=I0(n,s);a&&(r="back",i=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():d.navigations?.value&&(l=d.navigations.value),l?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let f=this.views.includes(n),p=this.insertView(n,r);f||n.ref.changeDetectorRef.detectChanges();let h=n.animationBuilder;return o===void 0&&r==="back"&&!a&&h!==void 0&&(o=h),s&&(s.animationBuilder=o),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,i,this.canGoBack(1),!1,o).then(()=>vN(n,p,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:i,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let i=r[r.length-n-1],o=i.url,s=i.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(o=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(o,V(b({},i.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],i=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,i))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&w0(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(b0),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=hN(this.views,n,t),this.views.slice()}transition(n,t,r,i,o,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:i,progressAnimation:o,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ve(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},vN=(e,n,t,r,i)=>typeof requestAnimationFrame=="function"?new Promise(o=>{requestAnimationFrame(()=>{w0(e,n,t,r,i),o()})}):Promise.resolve(),w0=(e,n,t,r,i)=>{i.run(()=>t.filter(o=>!n.includes(o)).forEach(b0)),n.forEach(o=>{let a=r.path().split("?")[0].split("#")[0];if(o!==e&&o.url!==a){let c=o.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),o.ref.changeDetectorRef.detach()}})},Yf=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new ye(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;stackWillChange=new ee;stackDidChange=new ee;activateEvents=new ee;deactivateEvents=new ee;parentContexts=v(Tn);location=v(We);environmentInjector=v(ue);inputBinder=v(E0,{optional:!0});supportsBindingToComponentInputs=!0;config=v(yo);navCtrl=v(Rn);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,i,o,s,a,c,l){this.parentOutlet=l,this.nativeEl=o.nativeElement,this.name=t||j,this.tabsPrefix=r==="true"?Hf(s,c):void 0,this.stackCtrl=new zf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,i),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>nn(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let i=this.activatedView.savedData.get("primary");if(i&&r.route&&(i.route=b({},r.route)),this.activatedView.savedExtras={},r.route){let o=r.route.snapshot;this.activatedView.savedExtras.queryParams=o.queryParams,this.activatedView.savedExtras.fragment=o.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let i,o=this.stackCtrl.getExistingView(t);if(o){i=this.activated=o.ref;let a=o.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(i.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,l=new ye(null),d=this.createActivatedRouteProxy(l,t),f=new Gf(d,c,this.location.injector),p=a.routeConfig.component??a.component;i=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:f,environmentInjector:r??this.environmentInjector}),l.next(i.instance),o=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(i.instance,d),this.currentActivatedRoute$.next({component:i.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=o,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:o,tabSwitch:I0(o,s)}),this.stackCtrl.setActive(o).then(a=>{this.activateEvents.emit(i.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let i=new Le;return i._futureSnapshot=r._futureSnapshot,i._routerState=r._routerState,i.snapshot=r.snapshot,i.outlet=r.outlet,i.component=r.component,i._paramMap=this.proxyObservable(t,"paramMap"),i._queryParamMap=this.proxyObservable(t,"queryParamMap"),i.url=this.proxyObservable(t,"url"),i.params=this.proxyObservable(t,"params"),i.queryParams=this.proxyObservable(t,"queryParams"),i.fragment=this.proxyObservable(t,"fragment"),i.data=this.proxyObservable(t,"data"),i}proxyObservable(t,r){return t.pipe(Ae(i=>!!i),we(i=>this.currentActivatedRoute$.pipe(Ae(o=>o!==null&&o.component===i),we(o=>o&&o.activatedRoute[r]),Zc())))}updateActivatedRouteProxy(t,r){let i=this.proxyMap.get(t);if(!i)throw new Error("Could not find activated route proxy for view");i._futureSnapshot=r._futureSnapshot,i._routerState=r._routerState,i.snapshot=r.snapshot,i.outlet=r.outlet,i.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(zt("name"),zt("tabs"),u(yt),u(m),u(_e),u(g),u(Le),u(e,12))};static \u0275dir=H({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]})}return e})(),Gf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Le?this.route:n===Tn?this.childContexts:this.parent.get(n,t)}},E0=new A(""),yN=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,i=jn([r.queryParams,r.params,r.data]).pipe(we(([o,s,a],c)=>(a=b(b(b({},o),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(o=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=va(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(t,i)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),M0=()=>({provide:E0,useFactory:DN,deps:[_e]});function DN(e){return e?.componentInputBindingEnabled?new yN:null}var IN=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],_0=(()=>{let e=class Wf{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,i,o,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=i,this.r=o,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||Wf)(u(Yf,8),u(Rn),u(yo),u(m),u(g),u(y))};static \u0275dir=H({type:Wf,hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"}})};return e=w([vc({inputs:IN})],e),e})(),S0=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,i,o,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=i,this.router=o,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(u(Ze),u(Rn),u(m),u(_e),u(Cf,8))};static \u0275dir=H({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,i){r&1&&Me("click",function(s){return i.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[nt]})}return e})(),T0=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,i,o,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=i,this.router=o,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(u(Ze),u(Rn),u(m),u(_e),u(Cf,8))};static \u0275dir=H({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,i){r&1&&Me("click",function(){return i.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[nt]})}return e})(),CN=["animated","animation","root","rootParams","swipeGesture"],bN=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],x0=(()=>{let e=class qf{z;el;constructor(t,r,i,o,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=o.create(r,i),Qf(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||qf)(u(m),u(ue),u(le),u(Nn),u(g),u(y))};static \u0275dir=H({type:qf,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"}})};return e=w([vc({inputs:CN,methods:bN})],e),e})(),A0=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new ee;ionTabsDidChange=new ee;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let i=t.stackId;r&&i!==void 0&&this.ionTabsWillChange.emit({tab:i})}onStackDidChange({enteringView:t,tabSwitch:r}){let i=t.stackId;r&&i!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=i),this.ionTabsDidChange.emit({tab:i}))}select(t){let r=typeof t=="string",i=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(i),this.tabSwitch();return}let o=this.outlet.getActiveStackId()===i,s=`${this.outlet.tabsPrefix}/${i}`;if(r||t.stopPropagation(),o){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let l=this.outlet.getRootView(i),d=l&&s===l.url&&l.savedExtras;return this.navCtrl.navigateRoot(s,V(b({},d),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(i),c=a?.url||s,l=a?.savedExtras;return this.navCtrl.navigateRoot(c,V(b({},l),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let i=this.tabs.find(o=>o.tab===t);if(!i){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=i,this.ionTabsWillChange.emit({tab:t}),i.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(u(Rn))};static \u0275dir=H({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,i){if(r&1&&ki(eN,7,m),r&2){let o;gt(o=mt())&&(i.tabsInner=o.first)}},hostBindings:function(r,i){r&1&&Me("ionTabButtonClick",function(s){return i.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"}})}return e})(),Kf=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),Do=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,ur(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),ur(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),ur(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(ar)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>ur(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(o=>{if(typeof r[o]<"u"){let s=r[o].bind(r);r[o]=(...a)=>{s(...a),ur(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,hostBindings:function(r,i){r&1&&Me("ionBlur",function(s){return i._handleBlurEvent(s.target)})}})}return e})(),ur=e=>{Kf(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=wN(n);Vf(n,r);let i=n.closest("ion-item");i&&(t?Vf(i,[...r,"item-has-value"]):Vf(i,r))})},wN=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let i=n.item(r);i!==null&&EN(i,"ng-")&&t.push(`ion-${i.substring(3)}`)}return t},Vf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},EN=(e,n)=>e.substring(0,n.length)===n,Zf=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,i=t.params,o=Object.keys(r),s=Object.keys(i);if(o.length!==s.length)return!1;for(let a of o)if(i[a]!==r[a])return!1;return!0}},An=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};function N0(){var e=[];if(typeof window<"u"){var n=window;(!n.customElements||n.Element&&(!n.Element.prototype.closest||!n.Element.prototype.matches||!n.Element.prototype.remove||!n.Element.prototype.getRootNode))&&e.push(import("./chunk-5X4HMWFG.js"));var t=function(){try{var r=new URL("b","http://a");return r.pathname="c%20d",r.href==="http://a/c%20d"&&r.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||n.NodeList&&!n.NodeList.prototype.forEach||!n.fetch||!t()||typeof WeakMap>"u")&&e.push(import("./chunk-LQX7KJ2R.js"))}return Promise.all(e)}var O0=Tc;var k0=(e,n)=>ve(void 0,null,function*(){if(!(typeof window>"u"))return yield O0(),Up(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),n)});var M=["*"],FN=["outletContent"],jN=["outlet"],LN=[[["","slot","top"]],"*",[["ion-tab"]]],VN=["[slot=top]","*","ion-tab"];function BN(e,n){if(e&1){let t=$y();zr(0,"ion-router-outlet",5,1),Me("stackWillChange",function(i){Su(t);let o=Oi();return Tu(o.onStackWillChange(i))})("stackDidChange",function(i){Su(t);let o=Oi();return Tu(o.onStackDidChange(i))}),Gr()}}function UN(e,n){e&1&&C(0,2,["*ngIf","tabs.length > 0"])}function $N(e,n){if(e&1&&(zr(0,"div",1),ha(1,2),Gr()),e&2){let t=Oi();sa(),yn("ngTemplateOutlet",t.template)}}function HN(e,n){if(e&1&&ha(0,1),e&2){let t=Oi();yn("ngTemplateOutlet",t.template)}}var zN=(()=>{class e extends Do{constructor(t,r){super(t,r)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,ur(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(r,i){r&1&&Me("ionChange",function(s){return i._handleIonChange(s.target)})},features:[Ue([{provide:cr,useExisting:e,multi:!0}]),oe]})}return e})(),GN=(()=>{class e extends Do{el;constructor(t,r){super(t,r),this.el=r}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(r=>{t(r===""?null:parseFloat(r))}):super.registerOnChange(t)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(r,i){r&1&&Me("ionInput",function(s){return i.handleInputEvent(s.target)})},features:[Ue([{provide:cr,useExisting:e,multi:!0}]),oe]})}return e})(),WN=(()=>{class e extends Do{constructor(t,r){super(t,r)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(r,i){r&1&&Me("ionChange",function(s){return i._handleChangeEvent(s.target)})},features:[Ue([{provide:cr,useExisting:e,multi:!0}]),oe]})}return e})(),qN=(()=>{class e extends Do{constructor(t,r){super(t,r)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(le),u(m))};static \u0275dir=H({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(r,i){r&1&&Me("ionInput",function(s){return i._handleInputEvent(s.target)})},features:[Ue([{provide:cr,useExisting:e,multi:!0}]),oe]})}return e})(),ZN=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(i){this.z.runOutsideAngular(()=>this.el[r]=i)},configurable:!0})})},QN=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,i))}})},Y=(e,n,t)=>{t.forEach(r=>e[r]=Ln(n,r))};function _(e){return function(t){let{defineCustomElementFn:r,inputs:i,methods:o}=e;return r!==void 0&&r(),i&&ZN(t,i),o&&QN(t,o),t}}var YN=(()=>{let e=class Xf{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Xf)(u(y),u(m),u(g))};static \u0275cmp=I({type:Xf,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),KN=(()=>{let e=class Jf{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||Jf)(u(y),u(m),u(g))};static \u0275cmp=I({type:Jf,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),XN=(()=>{let e=class eh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||eh)(u(y),u(m),u(g))};static \u0275cmp=I({type:eh,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),JN=(()=>{let e=class th{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||th)(u(y),u(m),u(g))};static \u0275cmp=I({type:th,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),eO=(()=>{let e=class nh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||nh)(u(y),u(m),u(g))};static \u0275cmp=I({type:nh,selectors:[["ion-app"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({methods:["setFocus"]})],e),e})(),tO=(()=>{let e=class rh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||rh)(u(y),u(m),u(g))};static \u0275cmp=I({type:rh,selectors:[["ion-avatar"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),nO=(()=>{let e=class ih{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionBackdropTap"])}static \u0275fac=function(r){return new(r||ih)(u(y),u(m),u(g))};static \u0275cmp=I({type:ih,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["stopPropagation","tappable","visible"]})],e),e})(),rO=(()=>{let e=class oh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||oh)(u(y),u(m),u(g))};static \u0275cmp=I({type:oh,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),iO=(()=>{let e=class sh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||sh)(u(y),u(m),u(g))};static \u0275cmp=I({type:sh,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),oO=(()=>{let e=class ah{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(r){return new(r||ah)(u(y),u(m),u(g))};static \u0275cmp=I({type:ah,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),sO=(()=>{let e=class ch{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||ch)(u(y),u(m),u(g))};static \u0275cmp=I({type:ch,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),aO=(()=>{let e=class lh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||lh)(u(y),u(m),u(g))};static \u0275cmp=I({type:lh,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["collapse"]})],e),e})(),cO=(()=>{let e=class uh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||uh)(u(y),u(m),u(g))};static \u0275cmp=I({type:uh,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),lO=(()=>{let e=class dh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dh)(u(y),u(m),u(g))};static \u0275cmp=I({type:dh,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["mode"]})],e),e})(),uO=(()=>{let e=class fh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||fh)(u(y),u(m),u(g))};static \u0275cmp=I({type:fh,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode","translucent"]})],e),e})(),dO=(()=>{let e=class hh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||hh)(u(y),u(m),u(g))};static \u0275cmp=I({type:hh,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),fO=(()=>{let e=class ph{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ph)(u(y),u(m),u(g))};static \u0275cmp=I({type:ph,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),hO=(()=>{let e=class gh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||gh)(u(y),u(m),u(g))};static \u0275cmp=I({type:gh,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),pO=(()=>{let e=class mh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||mh)(u(y),u(m),u(g))};static \u0275cmp=I({type:mh,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","disabled","mode","outline"]})],e),e})(),gO=(()=>{let e=class vh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||vh)(u(y),u(m),u(g))};static \u0275cmp=I({type:vh,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),mO=(()=>{let e=class yh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||yh)(u(y),u(m),u(g))};static \u0275cmp=I({type:yh,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),vO=(()=>{let e=class Dh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Dh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Dh,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),yO=(()=>{let e=class Ih{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ih)(u(y),u(m),u(g))};static \u0275cmp=I({type:Ih,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","datetime","disabled","mode"]})],e),e})(),DO=(()=>{let e=class Ch{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ch)(u(y),u(m),u(g))};static \u0275cmp=I({type:Ch,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),IO=(()=>{let e=class bh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||bh)(u(y),u(m),u(g))};static \u0275cmp=I({type:bh,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),CO=(()=>{let e=class wh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wh)(u(y),u(m),u(g))};static \u0275cmp=I({type:wh,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["activated","side"]})],e),e})(),bO=(()=>{let e=class Eh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Eh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Eh,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["collapse","mode","translucent"]})],e),e})(),wO=(()=>{let e=class Mh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Mh,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["fixed"]})],e),e})(),EO=(()=>{let e=class _h{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_h)(u(y),u(m),u(g))};static \u0275cmp=I({type:_h,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["collapse","mode","translucent"]})],e),e})(),MO=(()=>{let e=class Sh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Sh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Sh,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),_O=(()=>{let e=class Th{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(r){return new(r||Th)(u(y),u(m),u(g))};static \u0275cmp=I({type:Th,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["alt","src"]})],e),e})(),SO=(()=>{let e=class xh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInfinite"])}static \u0275fac=function(r){return new(r||xh)(u(y),u(m),u(g))};static \u0275cmp=I({type:xh,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),TO=(()=>{let e=class Ah{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ah)(u(y),u(m),u(g))};static \u0275cmp=I({type:Ah,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["loadingSpinner","loadingText"]})],e),e})(),xO=(()=>{let e=class Rh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Rh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Rh,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),AO=(()=>{let e=class Nh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Nh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Nh,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),RO=(()=>{let e=class Oh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Oh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Oh,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),NO=(()=>{let e=class kh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||kh)(u(y),u(m),u(g))};static \u0275cmp=I({type:kh,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode","sticky"]})],e),e})(),OO=(()=>{let e=class Ph{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ph)(u(y),u(m),u(g))};static \u0275cmp=I({type:Ph,selectors:[["ion-item-group"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),kO=(()=>{let e=class Fh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Fh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Fh,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),PO=(()=>{let e=class jh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSwipe"])}static \u0275fac=function(r){return new(r||jh)(u(y),u(m),u(g))};static \u0275cmp=I({type:jh,selectors:[["ion-item-options"]],inputs:{side:"side"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["side"]})],e),e})(),FO=(()=>{let e=class Lh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionDrag"])}static \u0275fac=function(r){return new(r||Lh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Lh,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),jO=(()=>{let e=class Vh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Vh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Vh,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode","position"]})],e),e})(),LO=(()=>{let e=class Bh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Bh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Bh,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),VO=(()=>{let e=class Uh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Uh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Uh,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","lines","mode"]})],e),e})(),BO=(()=>{let e=class $h{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||$h)(u(y),u(m),u(g))};static \u0275cmp=I({type:$h,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),UO=(()=>{let e=class Hh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(r){return new(r||Hh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Hh,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),$O=(()=>{let e=class zh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||zh)(u(y),u(m),u(g))};static \u0275cmp=I({type:zh,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),HO=(()=>{let e=class Gh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Gh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Gh,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["autoHide","menu"]})],e),e})(),zO=(()=>{let e=class Wh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Wh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Wh,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),GO=(()=>{let e=class qh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||qh)(u(y),u(m),u(g))};static \u0275cmp=I({type:qh,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),WO=(()=>{let e=class Zh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Zh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Zh,selectors:[["ion-picker"]],inputs:{mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["mode"]})],e),e})(),qO=(()=>{let e=class Qh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||Qh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Qh,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),ZO=(()=>{let e=class Yh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Yh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Yh,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","disabled","value"]})],e),e})(),QO=(()=>{let e=class Kh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Kh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Kh,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),YO=(()=>{let e=class Xh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Xh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Xh,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),KO=(()=>{let e=class Jh{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Jh)(u(y),u(m),u(g))};static \u0275cmp=I({type:Jh,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),XO=(()=>{let e=class ep{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||ep)(u(y),u(m),u(g))};static \u0275cmp=I({type:ep,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),JO=(()=>{let e=class tp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(r){return new(r||tp)(u(y),u(m),u(g))};static \u0275cmp=I({type:tp,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),e1=(()=>{let e=class np{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(r){return new(r||np)(u(y),u(m),u(g))};static \u0275cmp=I({type:np,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),t1=(()=>{let e=class rp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||rp)(u(y),u(m),u(g))};static \u0275cmp=I({type:rp,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),n1=(()=>{let e=class ip{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ip)(u(y),u(m),u(g))};static \u0275cmp=I({type:ip,selectors:[["ion-reorder"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),r1=(()=>{let e=class op{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionItemReorder"])}static \u0275fac=function(r){return new(r||op)(u(y),u(m),u(g))};static \u0275cmp=I({type:op,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled"],methods:["complete"]})],e),e})(),i1=(()=>{let e=class sp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||sp)(u(y),u(m),u(g))};static \u0275cmp=I({type:sp,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["type"],methods:["addRipple"]})],e),e})(),o1=(()=>{let e=class ap{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ap)(u(y),u(m),u(g))};static \u0275cmp=I({type:ap,selectors:[["ion-row"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),s1=(()=>{let e=class cp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||cp)(u(y),u(m),u(g))};static \u0275cmp=I({type:cp,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),a1=(()=>{let e=class lp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||lp)(u(y),u(m),u(g))};static \u0275cmp=I({type:lp,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),c1=(()=>{let e=class up{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||up)(u(y),u(m),u(g))};static \u0275cmp=I({type:up,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),l1=(()=>{let e=class dp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dp)(u(y),u(m),u(g))};static \u0275cmp=I({type:dp,selectors:[["ion-segment-content"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),u1=(()=>{let e=class fp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(r){return new(r||fp)(u(y),u(m),u(g))};static \u0275cmp=I({type:fp,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled"]})],e),e})(),d1=(()=>{let e=class hp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||hp)(u(y),u(m),u(g))};static \u0275cmp=I({type:hp,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),f1=(()=>{let e=class pp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||pp)(u(y),u(m),u(g))};static \u0275cmp=I({type:pp,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["header","multiple","options"]})],e),e})(),h1=(()=>{let e=class gp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gp)(u(y),u(m),u(g))};static \u0275cmp=I({type:gp,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled","value"]})],e),e})(),p1=(()=>{let e=class mp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||mp)(u(y),u(m),u(g))};static \u0275cmp=I({type:mp,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated"]})],e),e})(),g1=(()=>{let e=class vp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||vp)(u(y),u(m),u(g))};static \u0275cmp=I({type:vp,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","duration","name","paused"]})],e),e})(),m1=(()=>{let e=class yp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(r){return new(r||yp)(u(y),u(m),u(g))};static \u0275cmp=I({type:yp,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["contentId","disabled","when"]})],e),e})(),P0=(()=>{let e=class Dp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Dp)(u(y),u(m),u(g))};static \u0275cmp=I({type:Dp,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Ip=(()=>{let e=class Cp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Cp)(u(y),u(m),u(g))};static \u0275cmp=I({type:Cp,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),v1=(()=>{let e=class bp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||bp)(u(y),u(m),u(g))};static \u0275cmp=I({type:bp,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),y1=(()=>{let e=class wp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wp)(u(y),u(m),u(g))};static \u0275cmp=I({type:wp,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),D1=(()=>{let e=class Ep{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Ep)(u(y),u(m),u(g))};static \u0275cmp=I({type:Ep,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),I1=(()=>{let e=class Mp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mp)(u(y),u(m),u(g))};static \u0275cmp=I({type:Mp,selectors:[["ion-thumbnail"]],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({})],e),e})(),C1=(()=>{let e=class _p{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_p)(u(y),u(m),u(g))};static \u0275cmp=I({type:_p,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","size"]})],e),e})(),b1=(()=>{let e=class Sp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Sp)(u(y),u(m),u(g))};static \u0275cmp=I({type:Sp,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),w1=(()=>{let e=class Tp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Tp)(u(y),u(m),u(g))};static \u0275cmp=I({type:Tp,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),E1=(()=>{let e=class xp{z;el;constructor(t,r,i){this.z=i,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||xp)(u(y),u(m),u(g))};static \u0275cmp=I({type:xp,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})};return e=w([_({inputs:["color","mode"]})],e),e})(),yc=(()=>{class e extends Yf{parentOutlet;outletContent;constructor(t,r,i,o,s,a,c,l){super(t,r,i,o,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(r){return new(r||e)(zt("name"),zt("tabs"),u(yt),u(m),u(_e),u(g),u(Le),u(e,12))};static \u0275cmp=I({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(r,i){if(r&1&&ki(FN,7,We),r&2){let o;gt(o=mt())&&(i.outletContent=o.first)}},features:[oe],ngContentSelectors:M,decls:3,vars:0,consts:[["outletContent",""]],template:function(r,i){r&1&&(E(),da(0,null,0),C(2),fa())},encapsulation:2})}return e})(),M1=(()=>{class e extends A0{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-tabs"]],contentQueries:function(r,i,o){if(r&1&&(Dn(o,Ip,5),Dn(o,Ip,4),Dn(o,P0,4)),r&2){let s;gt(s=mt())&&(i.tabBar=s.first),gt(s=mt())&&(i.tabBars=s),gt(s=mt())&&(i.tabs=s)}},viewQuery:function(r,i){if(r&1&&ki(jN,5,yc),r&2){let o;gt(o=mt())&&(i.outlet=o.first)}},features:[oe],ngContentSelectors:VN,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(r,i){r&1&&(E(LN),C(0),zr(1,"div",2,0),Ni(3,BN,2,0,"ion-router-outlet",3)(4,UN,1,0,"ng-content",4),Gr(),C(5,1)),r&2&&(sa(3),yn("ngIf",i.tabs.length===0),sa(),yn("ngIf",i.tabs.length>0))},dependencies:[Aa,yc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),_1=(()=>{class e extends _0{constructor(t,r,i,o,s,a){super(t,r,i,o,s,a)}static \u0275fac=function(r){return new(r||e)(u(yc,8),u(Rn),u(yo),u(m),u(g),u(y))};static \u0275cmp=I({type:e,selectors:[["ion-back-button"]],features:[oe],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})}return e})(),S1=(()=>{class e extends x0{constructor(t,r,i,o,s,a){super(t,r,i,o,s,a)}static \u0275fac=function(r){return new(r||e)(u(m),u(ue),u(le),u(Nn),u(g),u(y))};static \u0275cmp=I({type:e,selectors:[["ion-nav"]],features:[oe],ngContentSelectors:M,decls:1,vars:0,template:function(r,i){r&1&&(E(),C(0))},encapsulation:2,changeDetection:0})}return e})(),T1=(()=>{class e extends S0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],features:[oe]})}return e})(),x1=(()=>{class e extends T0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],features:[oe]})}return e})(),A1=(()=>{class e extends y0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-modal"]],features:[oe],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(r,i){r&1&&Ni(0,$N,2,1,"div",0),r&2&&yn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Aa,wd],encapsulation:2,changeDetection:0})}return e})(),R1=(()=>{class e extends v0{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275cmp=I({type:e,selectors:[["ion-popover"]],features:[oe],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(r,i){r&1&&Ni(0,HN,1,1,"ng-container",0),r&2&&yn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Aa,wd],encapsulation:2,changeDetection:0})}return e})(),N1={provide:xn,useExisting:et(()=>F0),multi:!0},F0=(()=>{class e extends xf{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&Tt("max",i._enabled?i.max:null)},features:[Ue([N1]),oe]})}return e})(),O1={provide:xn,useExisting:et(()=>j0),multi:!0},j0=(()=>{class e extends Af{static \u0275fac=(()=>{let t;return function(i){return(t||(t=ke(e)))(i||e)}})();static \u0275dir=H({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,i){r&2&&Tt("min",i._enabled?i.min:null)},features:[Ue([O1]),oe]})}return e})(),sz=(()=>{class e extends An{constructor(){super(xc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),az=(()=>{class e{create(t){return bo(t)}easingTime(t,r,i,o,s){return wo(t,r,i,o,s)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var cz=(()=>{class e extends An{constructor(){super(Ac)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var k1=(()=>{class e extends An{angularDelegate=v(Nn);injector=v(le);environmentInjector=v(ue);constructor(){super(Rc)}create(t){return super.create(V(b({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();var Ap=class extends An{angularDelegate=v(Nn);injector=v(le);environmentInjector=v(ue);constructor(){super(Nc)}create(n){return super.create(V(b({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},lz=(()=>{class e extends An{constructor(){super(Oc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),P1=(e,n,t)=>()=>{let r=n.defaultView;if(r&&typeof window<"u"){kc(V(b({},e),{_zoneGate:o=>t.run(o)}));let i="__zone_symbol__addEventListener"in n.body?"__zone_symbol__addEventListener":"addEventListener";return N0().then(()=>k0(r,{exclude:["ion-tabs"],syncQueue:!0,raf:Kf,jmp:o=>t.runOutsideAngular(o),ael(o,s,a,c){o[i](s,a,c)},rel(o,s,a,c){o.removeEventListener(s,a,c)}}))}},F1=[YN,KN,XN,JN,eO,tO,nO,rO,iO,oO,sO,aO,cO,lO,uO,dO,fO,hO,pO,gO,mO,vO,yO,DO,IO,CO,bO,wO,EO,MO,_O,SO,TO,xO,AO,RO,NO,OO,kO,PO,FO,jO,LO,VO,BO,UO,$O,HO,zO,GO,WO,qO,ZO,QO,YO,KO,XO,JO,e1,t1,n1,r1,i1,o1,s1,a1,c1,l1,u1,d1,f1,h1,p1,g1,m1,P0,Ip,v1,y1,D1,I1,C1,b1,w1,E1],uz=[...F1,A1,R1,zN,GN,WN,qN,M1,yc,_1,S1,T1,x1,j0,F0],dz=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:mc,useValue:t},{provide:Wr,useFactory:P1,multi:!0,deps:[mc,Ie,g]},Nn,M0()]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ht({type:e});static \u0275inj=ft({providers:[k1,Ap],imports:[Ed]})}return e})();export{ye as a,w as b,yr as c,$0 as d,eC as e,G0 as f,Ot as g,yC as h,x as i,ft as j,N as k,v as l,I as m,ht as n,Ti as o,hb as p,ue as q,Su as r,Tu as s,zt as t,le as u,ee as v,g as w,Bt as x,m as y,xB as z,Yw as A,sa as B,u as C,We as D,oe as E,Ni as F,yn as G,By as H,ua as I,zr as J,Gr as K,cd as L,da as M,fa as N,$y as O,Me as P,Oi as Q,E as R,C as S,P_ as T,ki as U,gt as V,mt as W,OB as X,L_ as Y,Gy as Z,V_ as _,B_ as $,kB as aa,U_ as ba,qy as ca,PB as da,FB as ea,jB as fa,LB as ga,Wr as ha,y as ia,Ie as ja,yt as ka,s2 as la,a2 as ma,Aa as na,c2 as oa,Ed as pa,Xt as qa,JS as ra,sT as sa,C2 as ta,b2 as ua,q2 as va,Le as wa,hA as xa,_e as ya,dU as za,fU as Aa,MA as Ba,hU as Ca,pU as Da,VU as Ea,ui as Fa,Nf as Ga,OR as Ha,GU as Ia,WU as Ja,ZU as Ka,QU as La,JU as Ma,e$ as Na,n0 as Oa,r0 as Pa,VR as Qa,BR as Ra,UR as Sa,u$ as Ta,d$ as Ua,f$ as Va,h$ as Wa,p$ as Xa,s0 as Ya,YR as Za,g$ as _a,m$ as $a,v$ as ab,JR as bb,NU as cb,OU as db,lR as eb,fR as fb,PU as gb,mR as hb,FU as ib,g0 as jb,mc as kb,Nn as lb,vc as mb,Yf as nb,M0 as ob,Zf as pb,An as qb,zN as rb,WN as sb,qN as tb,JN as ub,rO as vb,sO as wb,aO as xb,cO as yb,lO as zb,uO as Ab,fO as Bb,hO as Cb,pO as Db,mO as Eb,DO as Fb,IO as Gb,bO as Hb,EO as Ib,MO as Jb,xO as Kb,RO as Lb,jO as Mb,LO as Nb,GO as Ob,e1 as Pb,t1 as Qb,s1 as Rb,a1 as Sb,c1 as Tb,g1 as Ub,Ip as Vb,v1 as Wb,y1 as Xb,C1 as Yb,E1 as Zb,M1 as _b,_1 as $b,A1 as ac,sz as bc,az as cc,cz as dc,k1 as ec,lz as fc,dz as gc};
