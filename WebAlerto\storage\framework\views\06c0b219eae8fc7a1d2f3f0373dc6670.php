<?php $__env->startSection('content'); ?>
<!-- Add Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
    crossorigin=""/>

<!-- Add Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Stats Section -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <?php if(auth()->user()->hasRole('super_admin')): ?>
        <!-- Center Search & Filters Form -->
        <form method="GET" action="<?php echo e(route('components.dashboard')); ?>" class="bg-white/80 backdrop-blur-sm rounded-xl shadow border border-sky-200 p-3 mb-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-2 items-end">
                <!-- Barangay Filter Only -->
                <div class="col-span-1 md:col-span-2">
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-map text-sky-600 mr-1"></i>Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" <?php echo e(($selectedBarangay ?? 'All') === 'All' ? 'selected' : ''); ?>>All Barangays</option>
                        <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($barangay); ?>" <?php echo e($selectedBarangay == $barangay ? 'selected' : ''); ?>><?php echo e($barangay); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
            <!-- Clear Filters Button -->
            <?php if($selectedBarangay && $selectedBarangay !== 'All'): ?>
                <div class="mt-2 text-center">
                    <a href="<?php echo e(route('components.dashboard')); ?>" class="inline-flex items-center px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-xs">
                        <i class="fas fa-times mr-1"></i>Clear Filter
                    </a>
                </div>
            <?php endif; ?>
        </form>
        <?php endif; ?>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Monthly Trends Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Monthly Trends</h3>
                        <p class="text-sm text-gray-600">
                            <?php if(auth()->user()->hasRole('super_admin')): ?>
                                Alerts & Centers (6 months)
                                <?php if($selectedBarangay): ?>
                                    - <?php echo e($selectedBarangay); ?>

                                <?php else: ?>
                                    - All Barangays
                                <?php endif; ?>
                            <?php else: ?>
                                Alerts & Centers (6 months) - <?php echo e(auth()->user()->barangay); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>

            <!-- Disaster Type Distribution Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Disaster Types</h3>
                        <p class="text-sm text-gray-600">
                            <?php if(auth()->user()->hasRole('super_admin')): ?>
                                Active Centers Distribution
                                <?php if($selectedBarangay): ?>
                                    - <?php echo e($selectedBarangay); ?>

                                <?php else: ?>
                                    - All Barangays
                                <?php endif; ?>
                            <?php else: ?>
                                Active Centers Distribution - <?php echo e(auth()->user()->barangay); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="disasterTypeChart"></canvas>
                </div>
            </div>

            <!-- Barangay Statistics Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">
                            <?php if(auth()->user()->hasRole('super_admin')): ?>
                                <?php if($selectedBarangay): ?>
                                    <?php echo e($selectedBarangay); ?> Stats
                                <?php else: ?>
                                    Barangay Stats
                                <?php endif; ?>
                            <?php else: ?>
                                <?php echo e(auth()->user()->barangay); ?> Stats
                            <?php endif; ?>
                        </h3>
                        <p class="text-sm text-gray-600">
                            <?php if(auth()->user()->hasRole('super_admin')): ?>
                                <?php if($selectedBarangay): ?>
                                    Centers & Alerts
                                <?php else: ?>
                                    Centers & Alerts by Area
                                <?php endif; ?>
                            <?php else: ?>
                                Centers & Alerts
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-bar text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="barangayStatsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Alerts Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-bell text-white text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Recent Alerts</h2>
                        <p class="text-gray-600 mt-1">Latest emergency notifications</p>
                    </div>
                </div>
                <a href="<?php echo e(route('components.notification.index')); ?>" 
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                    View All
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <?php if(count($recentAlerts) > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $recentAlerts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="group p-4 rounded-xl border-1/2 transition-all duration-200 hover:shadow-md
                            <?php if($alert->type == 'Flood'): ?> bg-blue-50 border-blue-500 hover:bg-blue-100
                            <?php elseif($alert->type == 'Typhoon'): ?> bg-red-50 border-red-500 hover:bg-red-100
                            <?php elseif($alert->type == 'Earthquake'): ?> bg-yellow-50 border-yellow-500 hover:bg-yellow-100
                            <?php else: ?> bg-gray-50 border-gray-500 hover:bg-gray-100 <?php endif; ?>">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 text-2xl">
                                    <?php if($alert->type == 'Flood'): ?> 💧
                                    <?php elseif($alert->type == 'Typhoon'): ?> 🌀
                                    <?php elseif($alert->type == 'Earthquake'): ?> ⛰️
                                    <?php else: ?> ℹ️ <?php endif; ?>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="font-semibold text-gray-900 text-sm leading-tight"><?php echo e($alert->message); ?></p>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            <?php if($alert->type == 'Flood'): ?> bg-blue-100 text-blue-800
                                            <?php elseif($alert->type == 'Typhoon'): ?> bg-red-100 text-red-800
                                            <?php elseif($alert->type == 'Earthquake'): ?> bg-yellow-100 text-yellow-800
                                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                            <?php echo e($alert->type); ?>

                                        </span>
                                        <span class="text-xs text-gray-500"><?php echo e($alert->time_ago); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <div class="text-gray-400 text-4xl mb-3">📭</div>
                    <p class="text-gray-500 font-medium">No recent alerts</p>
                    <p class="text-gray-400 text-sm mt-1">All quiet for now</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Admin Registration Section (System Administrator Only) -->
        <?php if(auth()->user()->hasRole('super_admin')): ?>
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-plus text-white text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Admin Registration</h2>
                        <p class="text-gray-600 mt-1">Register new admin accounts for barangays</p>
                    </div>
                </div>
                <button id="showRegistrationForm" 
                        class="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-plus"></i>
                    Register Admin
                </button>
            </div>
            
            <!-- Registration Form (Hidden by default) -->
            <div id="registrationForm" class="hidden">
                <div class="bg-white rounded-xl p-8 border border-sky-200 shadow-xl max-w-md mx-auto">
                    <h3 class="text-2xl font-bold text-sky-800 mb-6 flex items-center gap-2">
                        <i class="fas fa-user-plus text-sky-500"></i>
                        Register New Admin
                    </h3>
                    <form id="adminRegistrationForm" class="space-y-6">
                        <?php echo csrf_field(); ?>
                        <div class="space-y-4">
                            <div>
                                <label for="email" class="block text-sm font-semibold text-sky-700 mb-1">Email Address *</label>
                                <input type="email" name="email" id="email" class="w-full border-sky-200 rounded-lg shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-sky-50 text-sky-900" required autocomplete="off">
                            </div>
                            <div>
                                <label for="barangay" class="block text-sm font-semibold text-sky-700 mb-1">Barangay Assignment *</label>
                                <select name="barangay" id="barangay" class="w-full border-sky-200 rounded-lg shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-sky-50 text-sky-900" required>
                                    <option value="">Select Barangay</option>
                                </select>
                            </div>
                            <div>
                                <label for="role" class="block text-sm font-semibold text-sky-700 mb-1">Role *</label>
                                <select name="role" id="role" class="w-full border-sky-200 rounded-lg shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-sky-50 text-sky-900" required>
                                    <option value="">Select Role</option>
                                </select>
                            </div>
                        </div>
                        <div id="roleInfo" class="hidden bg-sky-50 border border-sky-200 rounded-lg p-4 mt-2">
                            <h4 class="text-sm font-semibold text-sky-800 mb-2">Role Access Information:</h4>
                            <p id="roleDescription" class="text-sm text-sky-700"></p>
                        </div>
                        <div class="flex justify-end gap-3 pt-4">
                            <button type="button" id="cancelRegistration" class="px-4 py-2 border border-sky-200 rounded-lg text-sky-700 bg-white hover:bg-sky-50 transition-colors">Cancel</button>
                            <button type="submit" class="px-6 py-2 bg-sky-500 hover:bg-sky-600 text-white rounded-lg font-semibold shadow transition-all duration-200">
                                <i class="fas fa-user-plus mr-2"></i>
                                Register Admin
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Access Info -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-blue-500 rounded-lg">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-blue-900">City Hall Admin</h4>
                            <p class="text-sm text-blue-700">City-wide monitoring access</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-green-500 rounded-lg">
                            <i class="fas fa-map-marker-alt text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-900">Barangay Admins</h4>
                            <p class="text-sm text-green-700">Barangay-specific access</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center gap-3">
                        <div class="p-2 bg-purple-500 rounded-lg">
                            <i class="fas fa-envelope text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-purple-900">Email Invitations</h4>
                            <p class="text-sm text-purple-700">Automatic invitation emails</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Map Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 mb-8">
            <!-- Map Controls Header -->
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-sm">
                        <i class="fas fa-map-marked-alt text-white text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-gray-900">Evacuation Centers Map</h2>
                        <p class="text-xs text-gray-600 mt-1">View all center locations</p>
                    </div>
                </div>
            </div>
            <!-- Legend Overlay (matches map.blade.php) -->
            <div class="absolute top-32 right-12 z-20 bg-white/90 rounded-xl shadow-lg border border-sky-200 p-4 flex flex-col items-center" style="min-width: 220px;">
                <div class="grid grid-cols-2 gap-x-6 gap-y-2">
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #22c55e;"></div><span class="text-xs text-gray-600">Typhoon</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #3b82f6;"></div><span class="text-xs text-gray-600">Flood</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #ef4444;"></div><span class="text-xs text-gray-600">Fire</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #f59e42;"></div><span class="text-xs text-gray-600">Earthquake</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #a16207;"></div><span class="text-xs text-gray-600">Landslide</span></div>
                    <div class="flex items-center gap-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #9333ea;"></div><span class="text-xs text-gray-600">Others</span></div>
                    <div class="flex items-center gap-2 col-span-2"><div class="w-4 h-4 rounded-full shadow-sm" style="background-color: #6b7280;"></div><span class="text-xs text-gray-600">Multi-disaster</span></div>
                </div>
            </div>
            <div class="relative w-full flex-1">
                <div id="map" class="w-full rounded-xl overflow-hidden shadow-lg relative" style="z-index: 1; height: 500px; min-height: 500px;"></div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Add Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
    crossorigin=""></script>

<!-- Add external map.js for consistency with map blade -->
<script src="<?php echo e(asset('js/map.js')); ?>"></script>

<script>
window.isDashboardMap = true;
document.addEventListener('DOMContentLoaded', function () {
    // Chart.js Configuration
    Chart.defaults.font.family = 'Poppins, sans-serif';
    Chart.defaults.color = '#6b7280';
    
    // Monthly Trends Chart
    const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
    const monthlyData = <?php echo json_encode($monthlyData, 15, 512) ?>;
    
    if (monthlyData.alerts.some(value => value > 0) || monthlyData.centers.some(value => value > 0)) {
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: monthlyData.months,
                datasets: [
                    {
                        label: 'Emergency Alerts',
                        data: monthlyData.alerts,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Active Centers',
                        data: monthlyData.centers,
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        monthlyCtx.canvas.style.display = 'none';
        document.getElementById('monthlyTrendsChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No trends data for this period</p>
                </div>
            </div>
        `;
    }

    // Disaster Type Distribution Chart
    const disasterCtx = document.getElementById('disasterTypeChart').getContext('2d');
    const disasterData = <?php echo json_encode($disasterTypeData, 15, 512) ?>;
    
    if (disasterData.data.length > 0 && disasterData.data.some(value => value > 0)) {
        new Chart(disasterCtx, {
            type: 'doughnut',
            data: {
                labels: disasterData.labels,
                datasets: [{
                    data: disasterData.data,
                    backgroundColor: disasterData.colors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        disasterCtx.canvas.style.display = 'none';
        document.getElementById('disasterTypeChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No disaster type data</p>
                </div>
            </div>
        `;
    }

    // Barangay Statistics Chart
    const barangayCtx = document.getElementById('barangayStatsChart').getContext('2d');
    const barangayData = <?php echo json_encode($barangayStatsData, 15, 512) ?>;
    
    if (barangayData.length > 0 && barangayData.some(item => item.activeCenters > 0 || item.totalAlerts > 0)) {
        new Chart(barangayCtx, {
            type: 'bar',
            data: {
                labels: barangayData.map(item => item.barangay),
                datasets: [
                    {
                        label: 'Active Centers',
                        data: barangayData.map(item => item.activeCenters),
                        backgroundColor: 'rgba(34, 197, 94, 0.8)',
                        borderColor: '#22c55e',
                        borderWidth: 1
                    },
                    {
                        label: 'Total Alerts',
                        data: barangayData.map(item => item.totalAlerts),
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: '#ef4444',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        barangayCtx.canvas.style.display = 'none';
        document.getElementById('barangayStatsChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No barangay statistics data</p>
                </div>
            </div>
        `;
    }

    // Initialize map with data from Laravel (using same approach as map blade)
    const centers = <?php echo json_encode($evacuationCenters, 15, 512) ?>;
    const isAdmin = <?php echo e(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('admin') ? 'true' : 'false'); ?>;
    
    // Initialize map functionality (using external map.js)
    if (typeof initializeMap === 'function') {
        initializeMap(centers, isAdmin);
    }
    
    // Initialize location search functionality
    if (typeof initializeLocationSearch === 'function') {
        initializeLocationSearch();
    }
    
    // Initialize barangay filter functionality
    if (typeof initializeBarangayFilter === 'function') {
        initializeBarangayFilter();
    }

    // Dashboard-specific functionality
    <?php if(auth()->user()->hasRole('super_admin')): ?>
    // Admin registration form functionality
    const registrationForm = document.getElementById('adminRegistrationForm');
    const roleInfo = document.getElementById('roleInfo');
    const adminRegistrationForm = document.getElementById('adminRegistrationForm');

    if (registrationForm && roleInfo && adminRegistrationForm) {
        // Handle role selection
        const roleSelect = document.getElementById('role');
        roleSelect.addEventListener('change', function() {
            if (this.value === 'admin') {
                roleInfo.classList.remove('hidden');
            } else {
                roleInfo.classList.add('hidden');
            }
        });

        // Handle form submission
        adminRegistrationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Registering...';
            submitBtn.disabled = true;

            fetch('/admin-invitations/register-admin', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Admin Registered Successfully!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        // Reset form and hide it
                        adminRegistrationForm.reset();
                        registrationForm.classList.add('hidden');
                        roleInfo.classList.add('hidden');
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Registration Failed',
                        text: data.message || 'An error occurred during registration.',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Registration Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonColor: '#ef4444'
                });
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    <?php endif; ?>

    var barangaySelect = document.getElementById('barangay');
    if (barangaySelect) {
        // Submit on change
        barangaySelect.addEventListener('change', function () {
            this.form.submit();
        });
        // Submit on click (even if the same value is clicked again)
        barangaySelect.addEventListener('click', function () {
            // For browsers that don't trigger change if the same value is selected
            // This will submit the form on every click
            this.form.submit();
        });
    }
});
</script>

<style>
    body {
        font-family: 'Poppins', sans-serif;
    }
    #map {
        height: 500px !important;
        width: 100% !important;
        z-index: 1;
        position: relative;
        overflow: hidden;
        min-height: 500px;
        flex: 1;
    }
    .leaflet-container {
        position: relative !important;
        outline: none;
        overflow: hidden !important;
        background: white !important;
        width: 100% !important;
        height: 100% !important;
    }
    /* Map Section Container */
    .map-section-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }
    .leaflet-popup-content {
        margin: 0;
        padding: 1rem;
        font-family: 'Poppins', sans-serif;
    }
    .leaflet-popup-content-wrapper {
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e0f2fe;
        background: white;
    }
    .leaflet-popup-tip {
        background: white;
    }
    .suggestion-item {
        transition: background-color 0.2s;
    }

    .suggestion-item:hover {
        background-color: #f0f9ff;
    }

    .selected-marker {
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Legend Overlay Styling */
    .map-section-container .legend-overlay {
        position: absolute;
        top: 2rem;
        right: 3rem;
        z-index: 20;
        background: rgba(255,255,255,0.95);
        border-radius: 1rem;
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        border: 1px solid #bae6fd;
        padding: 1.25rem 1.5rem;
        min-width: 220px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
</style>
<?php $__env->stopSection(); ?>




<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/components/dashboard.blade.php ENDPATH**/ ?>