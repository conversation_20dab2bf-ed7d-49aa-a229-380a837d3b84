import{a as i}from"./chunk-WPPT3EJF.js";import{i as r,k as n,qa as l,ra as c}from"./chunk-SFXIJNIZ.js";import{a as s,b as a}from"./chunk-2R6CW7ES.js";var p=(()=>{class e{constructor(){}getApiUrl(){return i.apiUrl}getHealthCheckUrl(){return i.healthCheckUrl||i.apiUrl.replace("/api","/up")}getEndpoint(t){let o=this.getApiUrl(),g=t.startsWith("/")?t.slice(1):t;return`${o}/${g}`}getAuthEndpoint(t=""){let o=t.startsWith("/")?t.slice(1):t;return this.getEndpoint(`auth/${o}`)}static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275prov=r({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var U=(()=>{class e{get apiUrl(){return this.apiConfig.getApiUrl()}constructor(t,o){this.http=t,this.apiConfig=o,console.log("Auth Service initialized"),console.log("API URL:",this.apiUrl)}getHeaders(){return new l({"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"})}login(t){return console.log("\u{1F510} Making login request to:",`${this.apiUrl}/auth/login`),console.log("\u{1F4E7} Credentials:",{email:t.email,password:"***"}),this.http.post(`${this.apiUrl}/auth/login`,t,{headers:this.getHeaders()})}register(t){return console.log("\u{1F4DD} Making registration request to:",`${this.apiUrl}/auth/signup`),console.log("\u{1F464} Registration data:",a(s({},t),{password:"***",password_confirmation:"***"})),this.http.post(`${this.apiUrl}/auth/signup`,t,{headers:this.getHeaders()})}setToken(t){localStorage.setItem("token",t),console.log("\u{1F511} Token stored successfully")}getToken(){return localStorage.getItem("token")}isAuthenticated(){return!!this.getToken()}logout(){localStorage.removeItem("token"),localStorage.removeItem("user"),localStorage.removeItem("offline_credentials"),console.log("\u{1F6AA} Auth service - User logged out")}static{this.\u0275fac=function(o){return new(o||e)(n(c),n(p))}}static{this.\u0275prov=r({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();export{U as a};
