<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
            'title',
            'first_name',
            'middle_name',
            'last_name' ,
            'suffix',
            'position',
            'barangay',
            'email',
            'password',
            'role',
            'status'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the device tokens for the user.
     */
    public function deviceTokens()
    {
        return $this->hasMany(DeviceToken::class);
    }

    /**
     * Route notifications for the FCM channel.
     *
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return string|null
     */
    public function routeNotificationForFcm($notification)
    {
        return $this->device_token;
    }

    /**
     * Check if user has access to a specific barangay
     */
    public function hasBarangayAccess($barangay)
    {
        return $this->barangay === $barangay;
    }

    /**
     * Scope a query to only include users from a specific barangay
     */
    public function scopeFromBarangay($query, $barangay)
    {
        return $query->where('barangay', $barangay);
    }

    /**
     * Get all data related to user's barangay
     */
    public function getBarangayData($model)
    {
        return $model->where('barangay', $this->barangay);
    }

    public function hasRole($role)
    {
        return strtolower($this->role) === strtolower($role);
    }

    public function canManageUser(User $user)
    {
        if (strtolower($this->role) === 'super_admin') return true;

        if (strtolower($this->role) === 'chairman' && $this->barangay === $user->barangay) return true;
        if (strtolower($this->role) === 'officer' && $this->barangay === $user->barangay) return true;
        if (strtolower($this->role) === 'assistant' && $this->barangay === $user->barangay) return true;
        return false;
    }

    /**
     * Check if user has access to a specific barangay data
     */
    public function canAccessBarangay($barangay)
    {
        if (strtolower($this->role) === 'super_admin') return true;

        return $this->barangay === $barangay; // Barangay users can only access their own barangay
    }

    /**
     * Check if user has city-wide monitoring access
     */
    public function hasCityWideAccess()
    {

        return strtolower($this->role) === 'super_admin';

    }

    /**
     * Get accessible barangays for the user
     */
    public function getAccessibleBarangays()
    {
        if ($this->hasCityWideAccess()) {

            // Get all barangays from the Barangay model instead of hardcoded list
            return \App\Models\Barangay::pluck('name')->toArray();

        }
        return [$this->barangay]; // Only their own barangay
    }
}

