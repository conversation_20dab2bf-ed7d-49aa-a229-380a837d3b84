<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Header Section -->
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-sky-200 p-4">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-md">
                        <i class="fas fa-bell text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Notification Center</h1>
                        <p class="text-sm text-gray-600 mt-1">Manage and monitor emergency alerts</p>
                    </div>
                </div>
                <a href="<?php echo e(route('components.notification.create')); ?>"
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg font-medium shadow-md transition-all duration-200">
                    <i class="fas fa-plus"></i>
                    Create Notification
                </a>
            </div>
        </div>
    </div>

    <!-- Filter Section for Admin Users -->
    <?php if(auth()->user()->hasRole('super_admin')): ?>
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div class="bg-white/80 backdrop-blur-sm rounded-lg shadow-md border border-sky-200 p-4">
            <form action="<?php echo e(route('components.notification.index')); ?>" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-1">Search Notifications</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" placeholder="Search by title, message, or barangay..." value="<?php echo e($searchQuery ?? ''); ?>"
                               class="w-full pl-10 pr-4 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                
                <div>
                    <label for="barangay" class="block text-sm font-semibold text-gray-700 mb-1">Filter by Barangay</label>
                    <select name="barangay" id="barangay" onchange="this.form.submit()" class="w-full pl-3 pr-10 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <option value="">All Barangays</option>
                        <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($barangay); ?>" <?php echo e(($selectedBarangay ?? '') == $barangay ? 'selected' : ''); ?>>
                                <?php echo e($barangay); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="md:col-span-3 flex justify-end items-center gap-2">
                    <button type="submit" class="inline-flex items-center justify-center gap-2 bg-sky-500 text-white px-4 py-2 rounded-xl font-semibold shadow-lg transition-all hover:bg-sky-600">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <?php if(request('search') || request('barangay')): ?>
                        <a href="<?php echo e(route('components.notification.index')); ?>" class="inline-flex items-center justify-center px-4 py-2 bg-gray-500 text-white rounded-xl font-semibold shadow-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <!-- Dashboard Cards Section -->
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Statistics Chart Card -->
            <div class="lg:col-span-1">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-sky-200 p-4 h-full">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg">
                            <i class="fas fa-chart-bar text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Notification Analytics</h3>
                    </div>
                    <div class="relative h-72">
                        <canvas id="notifChart" class="w-full h-full"></canvas>
                        <div id="monthlyCategoryCounts" class="hidden">
                            <?php echo e($monthlyCategoryCounts->toJson()); ?>

                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Alerts Card -->
            <div class="lg:col-span-1">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-sky-200 p-4 h-full">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Recent Alerts</h3>
                    </div>
                    <div class="space-y-2 max-h-72 overflow-y-auto">
                        <?php if(count($recentAlerts) > 0): ?>
                            <?php $__currentLoopData = $recentAlerts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="group p-3 rounded-lg transition-all duration-200 hover:shadow-md
                                    <?php if($alert->type == 'Flood'): ?> bg-blue-50 border-blue-500 hover:bg-blue-100
                                    <?php elseif($alert->type == 'Typhoon'): ?> bg-red-50 border-red-500 hover:bg-red-100
                                    <?php elseif($alert->type == 'Earthquake'): ?> bg-yellow-50 border-yellow-500 hover:bg-yellow-100
                                    <?php else: ?> bg-gray-50 border-gray-500 hover:bg-gray-100 <?php endif; ?>">
                                    <div class="flex items-start gap-3">
                                        <div class="flex-shrink-0 text-2xl">
                                            <?php if($alert->type == 'Flood'): ?> 💧
                                            <?php elseif($alert->type == 'Typhoon'): ?> 🌀
                                            <?php elseif($alert->type == 'Earthquake'): ?> ⛰️
                                            <?php else: ?> ℹ️ <?php endif; ?>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="font-semibold text-gray-900 text-sm leading-tight"><?php echo e($alert->message); ?></p>
                                            <div class="flex items-center justify-between mt-2">
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                    <?php if($alert->type == 'Flood'): ?> bg-blue-100 text-blue-800
                                                    <?php elseif($alert->type == 'Typhoon'): ?> bg-red-100 text-red-800
                                                    <?php elseif($alert->type == 'Earthquake'): ?> bg-yellow-100 text-yellow-800
                                                    <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                    <?php echo e($alert->type); ?>

                                                </span>
                                                <span class="text-xs text-gray-500"><?php echo e($alert->time_ago); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="text-center py-6">
                                <div class="text-gray-400 text-3xl mb-2">📭</div>
                                <p class="text-gray-500 font-medium">No recent alerts</p>
                                <p class="text-gray-400 text-xs mt-1">All quiet for now</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Notification History Section -->
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-sky-200 overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-history text-white text-xl"></i>
                        <h2 class="text-xl font-bold text-white">Notification History</h2>
                    </div>
                    <div class="text-white/80 text-sm">
                        Total: <?php echo e($notifications->total() ?? 0); ?> notifications
                    </div>
                </div>
            </div>

            <?php if($notifications->isEmpty()): ?>
                <div class="p-12 text-center">
                    <div class="text-gray-400 text-6xl mb-4">📋</div>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No notifications found</h3>
                    <p class="text-gray-500 mb-6">Get started by creating your first notification</p>
                    <a href="<?php echo e(route('components.notification.create')); ?>"
                       class="inline-flex items-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200">
                        <i class="fas fa-plus"></i>
                        Create First Notification
                    </a>
                </div>
            <?php else: ?>
                <!-- Table Container -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50/80">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-heading text-sky-600"></i>
                                        Title
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-tag text-sky-600"></i>
                                        Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-message text-sky-600"></i>
                                        Message
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-exclamation-circle text-sky-600"></i>
                                        Severity
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-calendar text-sky-600"></i>
                                        Created
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check-circle text-sky-600"></i>
                                        Status
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                    <div class="flex items-center justify-center gap-2">
                                        <i class="fas fa-cog text-sky-600"></i>
                                        Actions
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white/60 divide-y divide-gray-200">
                            <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-sky-50/80 transition-all duration-200 group">
                                <td class="px-6 py-4">
                                    <div class="font-semibold text-gray-900 group-hover:text-sky-800 transition-colors">
                                        <?php echo e($notification->title); ?>

                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <?php
                                        $categoryConfig = [
                                            'typhoon' => ['bg-green-100 text-green-800', 'fas fa-wind', 'Typhoon'],
                                            'flood' => ['bg-blue-100 text-blue-800', 'fas fa-water', 'Flood'],
                                            'fire' => ['bg-red-100 text-red-800', 'fas fa-fire', 'Fire'],
                                            'earthquake' => ['bg-orange-100 text-orange-800', 'fas fa-house-crack', 'Earthquake'],
                                            'landslide' => ['bg-amber-100 text-amber-800', 'fas fa-mountain', 'Landslide'],
                                            'emergency' => ['bg-red-100 text-red-800', 'fas fa-exclamation-triangle', 'Emergency'],
                                            'evacuation' => ['bg-purple-100 text-purple-800', 'fas fa-people-arrows', 'Evacuation'],
                                        ];
                                        $category = strtolower($notification->category);
                                        $config = $categoryConfig[$category] ?? ['bg-gray-100 text-gray-800', 'fas fa-circle', ucfirst($notification->category)];
                                    ?>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?php echo e($config[0]); ?>">
                                        <i class="<?php echo e($config[1]); ?> text-xs mr-1"></i>
                                        <?php echo e($config[2]); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-gray-600 max-w-xs truncate" title="<?php echo e($notification->message); ?>">
                                        <?php echo e(Str::limit($notification->message, 50)); ?>

                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <?php
                                        $severityConfig = [
                                            'low' => ['bg-green-100 text-green-800', 'fas fa-info-circle', 'Low'],
                                            'medium' => ['bg-yellow-100 text-yellow-800', 'fas fa-exclamation-triangle', 'Medium'],
                                            'high' => ['bg-red-100 text-red-800', 'fas fa-exclamation-circle', 'High'],
                                            'critical' => ['bg-pink-100 text-pink-800', 'fas fa-skull-crossbones', 'Critical'],
                                        ];
                                        $severity = strtolower($notification->severity);
                                        $config = $severityConfig[$severity] ?? ['bg-gray-100 text-gray-800', 'fas fa-question', 'Unknown'];
                                    ?>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?php echo e($config[0]); ?>">
                                        <i class="<?php echo e($config[1]); ?> text-xs mr-1"></i>
                                        <?php echo e($config[2]); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 text-gray-600">
                                    <div class="text-sm"><?php echo e($notification->created_at->format('M j, Y')); ?></div>
                                    <div class="text-xs text-gray-400"><?php echo e($notification->created_at->format('g:i A')); ?></div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                        <?php echo e($notification->sent ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                        <i class="fas <?php echo e($notification->sent ? 'fa-check' : 'fa-clock'); ?> text-xs mr-1"></i>
                                        <?php echo e($notification->sent ? 'Sent' : 'Pending'); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 text-center">
                                    <div class="flex justify-center gap-2">
                                        <!-- View Button -->
                                        <button
                                            class="inline-flex items-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200"
                                            onclick="openModal(<?php echo e($notification->id); ?>, '<?php echo e(addslashes($notification->title)); ?>', '<?php echo e(addslashes($notification->category)); ?>', '<?php echo e(addslashes($notification->message)); ?>', '<?php echo e($notification->sent ? 'Sent' : 'Pending'); ?>')">
                                            <i class="fas fa-eye text-xs"></i>
                                            View
                                        </button>

                                        <!-- Resend Button (only for sent notifications) -->
                                        <?php if(Auth::user()->hasRole('super_admin') || $notification->user_id === Auth::user()->id): ?>
                                            <?php if($notification->sent): ?>
                                                <form action="<?php echo e(route('components.notification.resend', $notification->id)); ?>" method="POST" class="inline-block">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit"
                                                            class="inline-flex items-center gap-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200"
                                                            onclick="return confirm('Are you sure you want to resend this notification?')">
                                                        <i class="fas fa-paper-plane text-xs"></i>
                                                        Resend
                                                    </button>
                                                </form>
                                            <?php endif; ?>

                                            <!-- Delete Button -->
                                            <button onclick="openDeleteModal(<?php echo e($notification->id); ?>, '<?php echo e(addslashes($notification->title)); ?>')"
                                                    class="inline-flex items-center gap-1 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                                <i class="fas fa-trash text-xs"></i>
                                                Delete
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="bg-gray-50/80 px-6 py-4 border-t border-gray-200">
                    <?php echo e($notifications->links('pagination::tailwind')); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Enhanced Modal -->
<div id="notificationModal" class="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden p-4">
    <div class="bg-white rounded-2xl w-full max-w-2xl shadow-2xl transform transition-all scale-95 overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-white/20 rounded-lg">
                        <i class="fas fa-bell text-white"></i>
                    </div>
                    <h2 id="modalTitle" class="text-xl font-bold text-white"></h2>
                </div>
                <button onclick="closeModal()" class="text-white/80 hover:text-white transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex items-center gap-3 p-4 bg-sky-50 rounded-xl">
                        <div class="p-2 bg-sky-100 rounded-lg">
                            <i class="fas fa-tag text-sky-600"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-600">Category</div>
                            <div id="modalCategory" class="text-lg font-semibold text-gray-900 flex items-center gap-2"></div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-4 bg-green-50 rounded-xl">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-600">Status</div>
                            <div id="modalStatus" class="text-lg font-semibold text-gray-900"></div>
                        </div>
                    </div>
                </div>

                <div class="md:col-span-1">
                    <div class="p-4 bg-gray-50 rounded-xl h-full">
                        <div class="flex items-start gap-3">
                            <div class="p-2 bg-gray-100 rounded-lg">
                                <i class="fas fa-message text-gray-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-600 mb-2">Message</div>
                                <div id="modalMessage" class="text-gray-900 leading-relaxed"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end">
            <button onclick="closeModal()"
                    class="inline-flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200">
                <i class="fas fa-times"></i>
                Close
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden p-4">
    <div class="bg-white rounded-2xl w-full max-w-md shadow-2xl transform transition-all scale-95 overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-red-600 to-rose-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-white/20 rounded-lg">
                        <i class="fas fa-trash text-white"></i>
                    </div>
                    <h2 class="text-xl font-bold text-white">Delete Notification</h2>
                </div>
                <button onclick="closeDeleteModal()" class="text-white/80 hover:text-white transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure?</h3>
                <p class="text-sm text-gray-500 mb-4">
                    You are about to delete the notification "<span id="deleteNotificationTitle" class="font-medium"></span>".
                    This action cannot be undone.
                </p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
            <button onclick="closeDeleteModal()"
                    class="inline-flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200">
                <i class="fas fa-times"></i>
                Cancel
            </button>
            <form id="deleteForm" method="POST" class="inline-block">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit"
                        class="inline-flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    function openModal(id, title, category, message, status) {
        // Category configuration matching the table
        const categoryConfig = {
            'typhoon': ['fas fa-wind', 'Typhoon'],
            'flood': ['fas fa-water', 'Flood'],
            'fire': ['fas fa-fire', 'Fire'],
            'earthquake': ['fas fa-house-crack', 'Earthquake'],
            'landslide': ['fas fa-mountain', 'Landslide'],
            'emergency': ['fas fa-exclamation-triangle', 'Emergency'],
            'evacuation': ['fas fa-people-arrows', 'Evacuation'],
        };

        const config = categoryConfig[category.toLowerCase()] || ['fas fa-circle', category];

        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalCategory').innerHTML = `<i class="${config[0]} text-sm mr-1"></i>${config[1]}`;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('modalStatus').textContent = status;
        document.getElementById('notificationModal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('notificationModal').classList.add('hidden');
    }

    function openDeleteModal(id, title) {
        document.getElementById('deleteNotificationTitle').textContent = title;
        document.getElementById('deleteForm').action = `/notification/${id}`;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
    }
</script>
<script>
    window.monthlyCategoryCounts = <?php echo $monthlyCategoryCounts->toJson(); ?>;
</script>
<script src="<?php echo e(asset('js/notification.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const barangaySelect = document.getElementById('barangay');

    if (barangaySelect) {
        barangaySelect.addEventListener('change', function() {
            const selectedBarangay = this.value;
            const currentUrl = new URL(window.location.href);
            
            if (selectedBarangay) {
                currentUrl.searchParams.set('barangay', selectedBarangay);
            } else {
                currentUrl.searchParams.delete('barangay');
            }
            
            window.location.href = currentUrl.toString();
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/components/notification/index.blade.php ENDPATH**/ ?>