WebAlerto Admin Account - Access Granted

Dear <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>,

Your administrator account for WebAlerto Emergency Management System has been created by the system administrator. You have been granted access to manage emergency alerts and evacuation resources for <?php echo e($user->barangay); ?> Barangay.

This is an official system notification from your organization's emergency management team.

LOGIN CREDENTIALS:
==================
Email: <?php echo e($user->email); ?>

Temporary Password: <?php echo e($temporaryPassword); ?>

Role: <?php echo e(ucfirst(str_replace('_', ' ', $user->role))); ?>

Position: <?php echo e($user->position); ?>


IMPORTANT SECURITY NOTICE:
=========================
- This is a temporary password. Please change it immediately after your first login.
- Do not share these credentials with anyone.
- Always log out when finished using the system.

ACCESS YOUR ACCOUNT:
===================
Login URL: <?php echo e($loginUrl); ?>


NEED HELP?
==========
If you have any questions or need assistance, please contact the system administrator.

System: WebAlerto Emergency Management
Environment: <?php echo e(config('app.env') === 'production' ? 'Production' : 'Development'); ?>


---
This email was sent automatically by the WebAlerto system.
© <?php echo e(date('Y')); ?> WebAlerto Emergency Alert & Management System
<?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/emails/admin-registration-text.blade.php ENDPATH**/ ?>