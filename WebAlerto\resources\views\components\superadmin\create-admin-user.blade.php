@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Header Section -->
    <div class="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12 mb-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
            <div class="flex items-center gap-4">
                <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                    <i class="fas fa-user-plus text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create Admin User</h1>
                    <p class="text-gray-600 mt-1">Add a new administrator to the WebAlerto system</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Messages -->
    @if($errors->any())
        <div class="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12 mb-6">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl shadow-lg">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <strong>Please correct the following errors:</strong>
                </div>
                <ul class="list-disc list-inside space-y-1">
                    @foreach($errors->all() as $error)
                        <li class="text-sm">{{ $error }}</li>
                    @endforeach
                </ul>
                <div class="mt-3 p-2 bg-red-50 rounded text-xs">
                    <strong>Debug Info:</strong> Form validation failed. Check the fields marked in red below.
                </div>
            </div>
        </div>
    @endif

    <!-- Success Messages -->
    @if(session('success'))
        <div class="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12 mb-6">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    {{ session('success') }}
                </div>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12 mb-6">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {{ session('error') }}
                </div>
            </div>
        </div>
    @endif

    <!-- Form Section -->
    <div class="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Form Header -->
            <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
                <div class="flex items-center gap-3">
                    <i class="fas fa-form text-white text-xl"></i>
                    <h2 class="text-xl font-bold text-white">Admin User Information</h2>
                </div>
            </div>

            <!-- Form Content -->
            <div class="p-8">
                <form action="{{ route('superadmin.store-admin-user') }}" method="POST" class="space-y-6">
                    @csrf

                    <!-- Personal Information Section -->
                    <div class="bg-gray-50/50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-user text-sky-600"></i>
                            Personal Information
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Title -->
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                                <select name="title" id="title" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white">
                                    <option value="">Select Title</option>
                                    <option value="Atty." {{ old('title') == 'Atty.' ? 'selected' : '' }}>Atty.</option>
                                    <option value="Engr." {{ old('title') == 'Engr.' ? 'selected' : '' }}>Engr.</option>
                                    <option value="Arch." {{ old('title') == 'Arch.' ? 'selected' : '' }}>Arch.</option>
                                    <option value="Dr." {{ old('title') == 'Dr.' ? 'selected' : '' }}>Dr.</option>
                                    <option value="Dir." {{ old('title') == 'Dir.' ? 'selected' : '' }}>Dir.</option>
                                </select>
                            </div>

                            <!-- First Name -->
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                <input type="text" name="first_name" id="first_name" value="{{ old('first_name') }}" required
                                       class="w-full px-4 py-3 border {{ $errors->has('first_name') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all">
                                @error('first_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Middle Name -->
                            <div>
                                <label for="middle_name" class="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                                <input type="text" name="middle_name" id="middle_name" value="{{ old('middle_name') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all">
                            </div>

                            <!-- Last Name -->
                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                <input type="text" name="last_name" id="last_name" value="{{ old('last_name') }}" required
                                       class="w-full px-4 py-3 border {{ $errors->has('last_name') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all">
                                @error('last_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                            <!-- Suffix -->
                            <div>
                                <label for="suffix" class="block text-sm font-medium text-gray-700 mb-2">Suffix</label>
                                <select name="suffix" id="suffix" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white">
                                    <option value="">Select Suffix</option>
                                    <option value="Jr." {{ old('suffix') == 'Jr.' ? 'selected' : '' }}>Jr.</option>
                                    <option value="Sr." {{ old('suffix') == 'Sr.' ? 'selected' : '' }}>Sr.</option>
                                    <option value="II" {{ old('suffix') == 'II' ? 'selected' : '' }}>II</option>
                                    <option value="III" {{ old('suffix') == 'III' ? 'selected' : '' }}>III</option>
                                    <option value="IV" {{ old('suffix') == 'IV' ? 'selected' : '' }}>IV</option>
                                    <option value="V" {{ old('suffix') == 'V' ? 'selected' : '' }}>V</option>
                                </select>
                            </div>

                            <!-- Email -->
                            <div class="md:col-span-2">
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                <div class="relative">
                                    <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                           class="w-full px-4 py-3 border {{ $errors->has('email') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all"
                                           onblur="checkEmailAvailability(this.value)">
                                    <div id="email-status" class="absolute right-3 top-1/2 transform -translate-y-1/2"></div>
                                </div>
                                <div id="email-message" class="text-xs mt-1"></div>
                                @error('email')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information Section -->
                    <div class="bg-gray-50/50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-briefcase text-sky-600"></i>
                            Professional Information
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Position -->
                            <div>
                                <label for="position" class="block text-sm font-medium text-gray-700 mb-2">Position *</label>
                                <input type="text" name="position" id="position" value="{{ old('position') }}" required
                                       placeholder="e.g., BDRRMO Director, System Administrator"
                                       class="w-full px-4 py-3 border {{ $errors->has('position') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all">
                                @error('position')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Barangay/Location -->
                            <div>
                                <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">Barangay/Location *</label>
                                <select name="barangay" id="barangay" required
                                        class="w-full px-4 py-3 border {{ $errors->has('barangay') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white">
                                    <option value="">Select Barangay</option>
                                    @foreach($barangays as $barangay)
                                        <option value="{{ $barangay }}" {{ old('barangay') == $barangay ? 'selected' : '' }}>
                                            {{ $barangay }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('barangay')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Role -->
                        <div class="mt-4">
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Admin Role *</label>
                            <select name="role" id="role" required
                                    class="w-full px-4 py-3 border {{ $errors->has('role') ? 'border-red-500' : 'border-gray-300' }} rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white">
                                <option value="">Select Role</option>
                                <option value="super_admin" {{ old('role') == 'super_admin' ? 'selected' : '' }}>Super Administrator</option>
                                <option value="chairman" {{ old('role') == 'chairman' ? 'selected' : '' }}>BDRRMO Chairman</option>
                                <option value="officer" {{ old('role') == 'officer' ? 'selected' : '' }}>BDRRMO Officer</option>
                            </select>
                            @error('role')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-sm text-gray-600 mt-1">
                                <strong>Super Administrator:</strong> Full system access including settings and admin user management.<br>
                                <strong>BDRRMO Chairman/Officer:</strong> Barangay-specific access for disaster management.
                            </p>
                        </div>
                    </div>

                    <!-- Important Notice -->
                    <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                        <div class="flex items-start gap-3">
                            <i class="fas fa-info-circle text-blue-600 text-xl mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-blue-900 mb-2">Important Information</h4>
                                <ul class="text-blue-800 text-sm space-y-1">
                                    <li>• A temporary password will be automatically generated for the new admin user</li>
                                    <li>• Registration details will be sent to the provided email address via SMTP</li>
                                    <li>• The new admin will be required to change their password on first login</li>
                                    <li>• Make sure the email address is valid and accessible by the new admin</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <a href="{{ route('superadmin.admin-users') }}" 
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 flex items-center gap-2">
                            <i class="fas fa-arrow-left"></i>
                            Back to Admin Users
                        </a>
                        
                        <button type="submit" 
                                class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 flex items-center gap-2">
                            <i class="fas fa-user-plus"></i>
                            Create Admin User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
async function checkEmailAvailability(email) {
    const statusDiv = document.getElementById('email-status');
    const messageDiv = document.getElementById('email-message');

    if (!email || !email.includes('@')) {
        statusDiv.innerHTML = '';
        messageDiv.innerHTML = '';
        return;
    }

    // Show loading
    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin text-gray-400"></i>';
    messageDiv.innerHTML = '<span class="text-gray-500">Checking availability...</span>';

    try {
        const response = await fetch('/superadmin/check-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ email: email })
        });

        const data = await response.json();

        if (data.available) {
            statusDiv.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
            messageDiv.innerHTML = '<span class="text-green-600">Email is available</span>';
        } else {
            statusDiv.innerHTML = '<i class="fas fa-times-circle text-red-500"></i>';
            messageDiv.innerHTML = `<span class="text-red-600">Email already registered to: ${data.user_info}</span>`;
        }
    } catch (error) {
        statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-500"></i>';
        messageDiv.innerHTML = '<span class="text-yellow-600">Could not check availability</span>';
    }
}
</script>

@endsection
