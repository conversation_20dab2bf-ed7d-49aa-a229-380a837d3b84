// This file contains the JavaScript logic for both the Add and Edit Evacuation Center pages.
// It handles map initialization, geocoding, marker placement, and form interactions.

// Pass data from PHP to this script by defining `window.EvacuationCenterData` in the Blade template.
const { isAdmin, userBarangay, hasOldLocation, oldLatitude, oldLongitude } = window.EvacuationCenterData || {};

let map;
let marker;
let searchTimeout;
let currentSearchId = 0; // To prevent race conditions
let mapLoadAttempts = 0;
const MAX_LOAD_ATTEMPTS = 3;
let userLocation = null;

// Disaster type color legend and badge classes
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Custom': '',
    'Multi-disaster': '#6b7280'
};
const disasterTypeBadgeClasses = {
    'Typhoon': 'bg-green-100 text-green-800',
    'Flood': 'bg-blue-100 text-blue-800',
    'Fire': 'bg-red-100 text-red-800',
    'Earthquake': 'bg-orange-100 text-orange-800',
    'Landslide': 'bg-yellow-100 text-yellow-800',
    'Multi-disaster': 'bg-gray-200 text-gray-800'
};

// --- Disaster Type Selection ---
function getSelectedDisasters() {
    return Array.from(document.querySelectorAll('input[name="disaster_type[]"]:checked')).map(cb => cb.value);
}

function updateSelectedDisastersDisplay() {
    const selected = getSelectedDisasters();
    const container = document.getElementById('selectedDisasters');
    if (!container) return;
    container.innerHTML = '';
    if (selected.length === 0) {
        container.innerHTML = '<span class="text-gray-400 text-sm">No disaster type selected</span>';
    } else {
        selected.forEach(type => {
            const span = document.createElement('span');
            if (type === 'Others') {
                const customField = document.getElementById('custom_disaster_type');
                if (customField && customField.value.trim()) {
                    span.className = 'px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                    span.textContent = customField.value.trim();
                } else {
                    span.className = 'px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                    span.textContent = 'Others';
                }
            } else {
                span.className = `px-2 py-1 rounded-full text-xs font-medium ${disasterTypeBadgeClasses[type] || 'bg-gray-200 text-gray-800'}`;
                span.textContent = type;
            }
            container.appendChild(span);
        });
    }
}

// --- Map and Marker Logic ---
function updateMarker(lat, lng, geocodeResult = null) {
    if (!map) return;
    if (marker) map.removeLayer(marker);

    lat = parseFloat(parseFloat(lat).toFixed(8));
    lng = parseFloat(parseFloat(lng).toFixed(8));

    const selectedTypes = getSelectedDisasters();
    let markerColor = '#6b7280'; // Default gray

    if (selectedTypes.length === 1) {
        markerColor = disasterTypeColors[selectedTypes[0]] || '#6b7280';
    } else if (selectedTypes.length > 1) {
        markerColor = disasterTypeColors['Multi-disaster'];
    }

    const markerIcon = L.divIcon({
        html: `<div style="background-color: ${markerColor}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>`,
        className: 'custom-div-icon',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
    });

    marker = L.marker([lat, lng], { draggable: true, icon: markerIcon }).addTo(map);

    marker.on('dragend', function(e) {
        const position = e.target.getLatLng();
        // Pass 'true' for isDragging to prevent panning
        updateMarkerPosition(position.lat, position.lng, null, true);
    });
    
    // Pan to the new location and update the address fields
    updateMarkerPosition(lat, lng, geocodeResult);
}

function updateMarkerPosition(lat, lng, geocodeResult = null, isDragging = false) {
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    document.getElementById('selectedLatitude').textContent = lat.toFixed(8);
    document.getElementById('selectedLongitude').textContent = lng.toFixed(8);
    
    // Only pan the map if we're not in the middle of a drag operation.
    if (map && !isDragging) {
        map.panTo([lat, lng]);
    }

    if (geocodeResult) {
        populateLocationFields(geocodeResult);
    } else {
        reverseGeocode(lat, lng);
    }
}

function initializeMap() {
    const mapContainer = document.getElementById('map');
    if (!mapContainer || map) return;

    try {
        document.getElementById('mapLoadingIndicator').classList.remove('hidden');
        map = L.map('map', {
            zoomControl: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            boxZoom: false,
            keyboard: true,
            dragging: true,
            touchZoom: true
        });

        const setViewOrDefault = (lat, lng, zoom = 13) => {
            map.setView([lat, lng], zoom);
            // On the edit page, the marker should be placed immediately.
            // The `hasOldLocation` is always true on the edit page.
            if (window.EvacuationCenterData.isEditPage) {
                 setTimeout(() => updateMarker(lat, lng), 100);
            } else if (hasOldLocation) {
                 setTimeout(() => updateMarker(oldLatitude, oldLongitude), 500);
            }
        };

        if (hasOldLocation) {
            setViewOrDefault(oldLatitude, oldLongitude, 16);
        } else if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    userLocation = { lat: position.coords.latitude, lng: position.coords.longitude };
                    setViewOrDefault(userLocation.lat, userLocation.lng);
                },
                () => { // Geolocation failed or denied
                    setViewOrDefault(10.3157, 123.8854); 
                }
            );
        } else { // No geolocation support
            setViewOrDefault(10.3157, 123.8854);
        }

        // Use a more reliable tile layer with better error handling
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
            subdomains: 'abc',
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        map.on('click', e => updateMarker(e.latlng.lat, e.latlng.lng));

        // Delay invalidateSize to ensure the container is rendered
        setTimeout(() => {
            map.invalidateSize();
            // Pan to the marker's location again after invalidation to ensure it's centered
            if (marker) {
                map.panTo(marker.getLatLng());
            } else if (hasOldLocation) {
                map.panTo([oldLatitude, oldLongitude]);
            }
        }, 100);
        
        setTimeout(() => document.getElementById('mapLoadingIndicator').classList.add('hidden'), 1000);
    } catch (error) {
        console.error('Error initializing map:', error);
        document.getElementById('mapError').classList.remove('hidden');
        document.getElementById('mapLoadingIndicator').classList.add('hidden');
    }
}

function retryMapLoad() {
    if (mapLoadAttempts < MAX_LOAD_ATTEMPTS) {
        mapLoadAttempts++;
        document.getElementById('mapError').classList.add('hidden');
        if (map) map.remove();
        map = null;
        initializeMap();
    } else {
        alert('Failed to load map after multiple attempts.');
    }
}


// --- Geocoding (Search and Reverse) ---
function geocodeAddress(address, isDirectSearch = false, searchId = null) {
    let url = `/api/geocode?address=${encodeURIComponent(address)}`;
    if (userLocation) {
        url += `&lat=${userLocation.lat}&lng=${userLocation.lng}`;
    }
    
    const searchResults = document.getElementById('searchResults');
    
    if (!isDirectSearch) {
        searchResults.innerHTML = '<div class="p-4 text-gray-500">Searching...</div>';
        searchResults.classList.remove('hidden');
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            // If a new search has been initiated, ignore the results of this old one
            if (searchId !== null && searchId < currentSearchId) {
                return;
            }
            
            searchResults.innerHTML = '';

            if (!data.features || data.features.length === 0) {
                searchResults.innerHTML = '<div class="p-4 text-gray-500">No results found</div>';
                searchResults.classList.remove('hidden');
                return;
            }

            if (isDirectSearch) {
                const result = data.features[0];
                map.setView([result.center[1], result.center[0]], 16);
                // Always trigger reverse geocoding to get proper address details
                updateMarker(result.center[1], result.center[0], null);
                searchResults.classList.add('hidden');
            } else {
                // Show all results without filtering
                data.features.forEach(result => addSearchResult(result, false));
                searchResults.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Geocoding error:', error);
            searchResults.innerHTML = '<div class="p-4 text-center text-red-500">Search failed. Please try again.</div>';
            searchResults.classList.remove('hidden');
        });
}
function addSearchResult(result, isRecommended) {
    const searchResults = document.getElementById('searchResults');
    const div = document.createElement('div');
    div.className = 'p-3 hover:bg-sky-50 cursor-pointer border-b border-gray-100 transition-colors';
    
    // The 'text' from our new API response is the full name, which is what we want.
    const fullAddress = result.text || 'No address available';
    
    div.innerHTML = `
        <div class="font-medium text-gray-900">${fullAddress}</div>
    `;
    
    div.addEventListener('click', () => {
        const lat = result.center[1];
        const lon = result.center[0];
        const selectedText = result.text || ''; // The main text from the search result
        const placeName = result.place_name || ''; // The full place name

        // Set the view and update the marker immediately
        map.setView([lat, lon], 16);
        updateMarker(lat, lon, null);

        // Use the search result data directly to avoid duplication
        // Parse the place_name to extract address components
        const addressComponents = placeName.split(', ');

        // Create a structured data object from the search result
        const searchData = {
            building_name: selectedText,
            street: '',
            barangay: '',
            city: '',
            province: '',
            postal_code: '',
            full_address: placeName
        };

        // Try to parse address components from the place_name
        if (addressComponents.length >= 2) {
            // Usually format is: "Building/Street, Barangay, City, Province, Country"
            if (addressComponents.length >= 4) {
                searchData.barangay = addressComponents[1]?.trim() || '';
                searchData.city = addressComponents[2]?.trim() || '';
                searchData.province = addressComponents[3]?.trim() || '';
            } else if (addressComponents.length >= 3) {
                searchData.city = addressComponents[1]?.trim() || '';
                searchData.province = addressComponents[2]?.trim() || '';
            } else {
                searchData.city = addressComponents[1]?.trim() || '';
            }
        }

        // Populate fields with the parsed search data
        populateLocationFields(searchData);

        // Set search bar to show the selected text (not the full place name)
        document.getElementById('search').value = selectedText;
        searchResults.classList.add('hidden');
    });
    searchResults.appendChild(div);
}

function reverseGeocode(lat, lng) {
    fetch(`/api/reverse-geocode?lat=${lat}&lng=${lng}`)
        .then(response => response.json())
        .then(data => {
            if (data && !data.error) {
                populateLocationFields(data);
            } else {
                document.getElementById('selectedAddress').textContent = `Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`;
                console.error('Reverse geocoding error:', data.error);
            }
        })
        .catch(error => console.error('Reverse geocoding error:', error));
}

function populateLocationFields(data) {
    // Normalize and clean up the data to prevent duplicates
    const cleanData = {
        building_name: (data.building_name || '').trim(),
        street: (data.street || '').trim(),
        barangay: (data.barangay || '').trim(),
        city: (data.city || '').trim(),
        province: (data.province || '').trim(),
        postal_code: (data.postal_code || '').trim()
    };

    // Helper function to check if a value is a substring of another (case-insensitive)
    const isSubstringOf = (value1, value2) => {
        if (!value1 || !value2) return false;
        const v1 = value1.toLowerCase().trim();
        const v2 = value2.toLowerCase().trim();
        return v1.includes(v2) || v2.includes(v1);
    };

    // Helper function to check if a value is already represented in existing parts
    const isAlreadyRepresented = (value, existingParts) => {
        if (!value) return true;
        return existingParts.some(part => isSubstringOf(value, part));
    };

    // Build address parts intelligently to avoid duplication
    let addressParts = [];

    // Start with the most specific information
    if (cleanData.building_name) {
        addressParts.push(cleanData.building_name);
    }

    // Add street only if it's not already represented in building name
    if (cleanData.street && !isAlreadyRepresented(cleanData.street, addressParts)) {
        addressParts.push(cleanData.street);
    }

    // Add barangay only if it's not already represented
    if (cleanData.barangay && !isAlreadyRepresented(cleanData.barangay, addressParts)) {
        addressParts.push(cleanData.barangay);
    }

    // Add city only if it's not already represented
    if (cleanData.city && !isAlreadyRepresented(cleanData.city, addressParts)) {
        addressParts.push(cleanData.city);
    }

    // Add province only if it's not already represented
    if (cleanData.province && !isAlreadyRepresented(cleanData.province, addressParts)) {
        addressParts.push(cleanData.province);
    }

    // Create a clean, concise address display
    const displayAddress = addressParts.length > 0
        ? addressParts.join(', ')
        : data.full_address || `Lat: ${document.getElementById('latitude').value}, Lng: ${document.getElementById('longitude').value}`;

    // Update the display
    document.getElementById('selectedAddress').textContent = displayAddress;

    // Update form fields with original data (not cleaned, to preserve exact values)
    document.getElementById('street_name').value = data.street || '';
    // Only set barangay from geocode for super_admin (not admin or barangay users)
    // This prevents accidental overwrite for barangay DRRMO users
    if (window.EvacuationCenterData.isAdmin === 'true' && window.EvacuationCenterData.userBarangay === 'City Hall') {
        document.getElementById('barangay').value = data.barangay || '';
    }
    document.getElementById('city').value = data.city || '';
    document.getElementById('province').value = data.province || '';
    document.getElementById('postal_code').value = data.postal_code || '';

    // Update building_name field if it exists
    const buildingNameField = document.getElementById('building_name');
    if (buildingNameField) {
        buildingNameField.value = data.building_name || '';
    }

    const provinceWarning = document.getElementById('provinceWarning');
    if (provinceWarning) {
        provinceWarning.classList.toggle('hidden', !!data.province);
    }
}


// --- Form and Step Handling ---
document.addEventListener('DOMContentLoaded', function() {
    const evacuationForm = document.getElementById('evacuationCenterForm');
    const isAddPage = !!document.getElementById('nextStep');
    window.EvacuationCenterData.isEditPage = !isAddPage;

    // --- Initialize Map ---
    // On the edit page, initialize the map immediately after the DOM is loaded.
    // On the add page, it's initialized when the user moves to step 2.
    if (!isAddPage) {
        initializeMap();
    } else { // Logic for the Add Page
        const step1 = document.getElementById('step1');
        const step2 = document.getElementById('step2');
        const nextStepBtn = document.getElementById('nextStep');

        if (nextStepBtn) {
            nextStepBtn.addEventListener('click', () => {
                // Validate step 1 before proceeding
                const requiredFields = [
                    { id: 'name', label: 'Center Name' },
                    { id: 'capacity', label: 'Capacity' },
                    { id: 'contact', label: 'Contact Number' },
                    { id: 'status', label: 'Status' }
                ];
                
                let isValid = true;
                let firstInvalidField = null;
                
                requiredFields.forEach(field => {
                    const element = document.getElementById(field.id);
                    const value = element.value.trim();
                    
                    if (!value) {
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = element;
                        element.classList.add('border-red-500', 'focus:border-red-500');
                    } else {
                        element.classList.remove('border-red-500', 'focus:border-red-500');
                    }
                });
                
                const disasterCheckboxes = document.querySelectorAll('input[name="disaster_type[]"]:checked');
                const disasterContainer = document.querySelector('.disaster-type-container');
                if (disasterCheckboxes.length === 0) {
                    isValid = false;
                    disasterContainer.classList.add('border-red-500');
                } else {
                    disasterContainer.classList.remove('border-red-500');
                }
                
                if (!isValid) {
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return;
                }

                // Hide step 1 and show step 2
                step1.classList.add('hidden');
                step2.classList.remove('hidden');
                
                // Initialize map only when moving to step 2
                if (!map) {
                    initializeMap();
                } else {
                    // If map already exists, it was hidden. We MUST invalidate its size.
                    setTimeout(() => {
                        map.invalidateSize();
                        if (marker) {
                            map.panTo(marker.getLatLng());
                        }
                    }, 10);
                }
                
                step2.scrollIntoView({ behavior: 'smooth', block: 'start' });
                
                // Ensure search input is properly initialized
                setTimeout(() => {
                    const searchInput = document.getElementById('search');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }, 200);
            });
        }
        
        // Also handle returning to a page with validation errors
        if (hasOldLocation) {
            step1.classList.add('hidden');
            step2.classList.remove('hidden');
            if (!map) {
                initializeMap();
            }
        }

        const prevStepBtn = document.getElementById('prevStep');
        if (prevStepBtn) {
            prevStepBtn.addEventListener('click', () => {
                document.getElementById('step1').classList.remove('hidden');
                document.getElementById('step2').classList.add('hidden');
                document.getElementById('step1').scrollIntoView({ behavior: 'smooth', block: 'start' });
            });
        }
    }

    // --- Disaster Type Checkboxes ---
    const othersCheckbox = document.querySelector('input[name="disaster_type[]"][value="Others"]');
    const customInputContainer = document.getElementById('customDisasterInput');
    const customInputField = document.getElementById('custom_disaster_type');

    function handleDisasterTypeChange() {
        updateSelectedDisastersDisplay();
        // If a marker already exists on the map, update its color to reflect the new selection
        if (marker) {
            const position = marker.getLatLng();
            updateMarker(position.lat, position.lng);
        }
    }

    document.querySelectorAll('input[name="disaster_type[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', handleDisasterTypeChange);
    });

    if (othersCheckbox && customInputContainer && customInputField) {
        othersCheckbox.addEventListener('change', function() {
            if (this.checked) {
                customInputContainer.classList.remove('hidden');
                customInputField.focus();
            } else {
                customInputContainer.classList.add('hidden');
                customInputField.value = ''; // Clear input on uncheck
            }
        });
    }

    if (customInputField) {
        customInputField.addEventListener('input', updateSelectedDisastersDisplay);
    }

    // Initial display update on page load for all forms
    updateSelectedDisastersDisplay();

    // --- Other Initializations ---
    const searchInput = document.getElementById('search');
    const searchButton = document.getElementById('searchButton');
    
    if (searchButton && searchInput) {
        searchButton.addEventListener('click', () => {
            const query = searchInput.value.trim();
            if(query) geocodeAddress(query, true);
        });
        
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            const query = searchInput.value.trim();
            
            // Clear results if query is empty
            if (query.length === 0) {
                const searchResults = document.getElementById('searchResults');
                searchResults.classList.add('hidden');
                return;
            }
            
            // Show searching indicator immediately for short queries
            if (query.length >= 1) {
                const searchResults = document.getElementById('searchResults');
                searchResults.innerHTML = '<div class="p-4 text-gray-500">Searching...</div>';
                searchResults.classList.remove('hidden');
                
                currentSearchId++;
                const thisSearchId = currentSearchId;
                
                // Debounce the search to avoid too many API calls
                searchTimeout = setTimeout(() => {
                    geocodeAddress(query, false, thisSearchId);
                }, 300);
            }
        });
        
        searchInput.addEventListener('keydown', e => {
            if (e.key === 'Enter') { 
                e.preventDefault(); 
                clearTimeout(searchTimeout);
                const query = searchInput.value.trim();
                if(query) geocodeAddress(query, true);
            }
        });
        
        // Ensure search input is properly initialized on add page
        if (isAddPage && hasOldLocation) {
            setTimeout(() => {
                searchInput.focus();
            }, 100);
        }
    }

    const contactInput = document.getElementById('contact');
    if (contactInput) {
        contactInput.setAttribute('type', 'tel');
        contactInput.addEventListener('input', e => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    }

    document.addEventListener('click', e => {
        const searchResults = document.getElementById('searchResults');
        const searchContainer = searchInput.closest('.relative');
        if (searchResults && !searchContainer.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

    const saveButton = document.getElementById('saveButton');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            // Validate location fields before submitting
            const requiredLocationFields = [
                { id: 'latitude', label: 'Latitude' },
                { id: 'longitude', label: 'Longitude' },
                { id: 'street_name', label: 'Street Name' },
                { id: 'city', label: 'City' },
                { id: 'province', label: 'Province' }
            ];
            let missing = [];
            requiredLocationFields.forEach(field => {
                const el = document.getElementById(field.id);
                if (!el || !el.value.trim()) {
                    missing.push(field.label);
                    if (el) el.classList.add('border-red-500');
                } else {
                    if (el) el.classList.remove('border-red-500');
                }
            });
            if (missing.length > 0) {
                e.preventDefault();
                alert('Please select a location on the map. The following fields are required: ' + missing.join(', '));
                // Scroll to error message if present
                const errorBox = document.querySelector('.mb-6.bg-red-50');
                if (errorBox) errorBox.scrollIntoView({ behavior: 'smooth', block: 'center' });
                return false;
            }
        });
    }
});