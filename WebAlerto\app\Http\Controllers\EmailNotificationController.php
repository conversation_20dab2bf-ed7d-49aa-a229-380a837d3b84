<?php

namespace App\Http\Controllers;

use App\Models\User;

use App\Models\Barangay;

use App\Services\EmailNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Auth;


class EmailNotificationController extends Controller
{
    protected $emailService;

    public function __construct(EmailNotificationService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Send notification to all admin users
     */
    public function notifyAllAdmins(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'event_type' => 'required|in:new_alert,system_update,user_registration,evacuation_center_update,system_maintenance,general',
            'event_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check permissions - only super admins and admins can send notifications
        $currentUser = auth()->user();
        if (!$currentUser->hasRole(['super_admin', 'admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to send notifications'
            ], 403);
        }

        try {
            $result = $this->emailService->notifyAllAdmins(
                $request->title,
                $request->message,
                $request->event_type,
                $request->event_data ?? []
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification sent to all admin users successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send notification'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send notification to all admins', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the notification'
            ], 500);
        }
    }

    /**
     * Send notification to specific barangay admins
     */
    public function notifyBarangayAdmins(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'barangay' => 'required|string|max:100',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'event_type' => 'required|in:new_alert,system_update,user_registration,evacuation_center_update,system_maintenance,general',
            'event_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check permissions
        $currentUser = auth()->user();
        if (!$currentUser->canAccessBarangay($request->barangay)) {
            return response()->json([
                'success' => false,
                'message' => 'You can only send notifications to barangays you have access to'
            ], 403);
        }

        try {
            $result = $this->emailService->notifyBarangayAdmins(
                $request->barangay,
                $request->title,
                $request->message,
                $request->event_type,
                $request->event_data ?? []
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => "Notification sent to {$request->barangay} admins successfully"
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send notification'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send notification to barangay admins', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the notification'
            ], 500);
        }
    }

    /**
     * Send notification to specific user
     */
    public function notifyUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'event_type' => 'required|in:new_alert,system_update,user_registration,evacuation_center_update,system_maintenance,general',
            'event_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check permissions
        $currentUser = auth()->user();
        $targetUser = User::find($request->user_id);

        if (!$currentUser->canManageUser($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'You can only send notifications to users you have permission to manage'
            ], 403);
        }

        try {
            $result = $this->emailService->notifyUser(
                $targetUser,
                $request->title,
                $request->message,
                $request->event_type,
                $request->event_data ?? []
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification sent to user successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send notification'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send notification to user', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the notification'
            ], 500);
        }
    }

    /**
     * Get admin users for notification targeting
     */
    public function getAdminUsers(Request $request)
    {
        $currentUser = auth()->user();
        $query = User::whereIn('role', ['super_admin', 'admin', 'chairman', 'officer', 'assistant'])
                     ->where('status', 'Active');

        // Filter by accessible barangays
        if (!$currentUser->hasCityWideAccess()) {
            $query->where('barangay', $currentUser->barangay);
        }

        // Filter by role
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        // Filter by barangay (only if user has city-wide access)
        if ($request->has('barangay') && $currentUser->canAccessBarangay($request->barangay)) {
            $query->where('barangay', $request->barangay);
        }

        $users = $query->select('id', 'first_name', 'last_name', 'email', 'role', 'barangay', 'position')
                       ->orderBy('first_name')
                       ->get();

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Get barangays for notification targeting
     */
    public function getBarangays()
    {
        $currentUser = auth()->user();
        
        if ($currentUser->hasCityWideAccess()) {

            // City Hall admin and System Administrator can see all barangays from the database
            $barangays = Barangay::pluck('name')->toArray();

        } else {
            // Barangay users can only see their own barangay
            $barangays = [$currentUser->barangay];
        }

        return response()->json([
            'success' => true,
            'data' => $barangays
        ]);
    }

    /**
     * Test email configuration
     */
    public function testEmailConfig()
    {
        // Check permissions - only super admins can test email config
        $currentUser = auth()->user();
        if (!$currentUser->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to test email configuration'
            ], 403);
        }

        try {
            // Send test notification to the current user
            $result = $this->emailService->notifyUser(
                $currentUser,
                'ALERTO Email Test',
                'This is a test email to verify your SMTP configuration is working correctly.',
                'system_update'
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Test email sent successfully. Please check your inbox.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send test email. Please check your SMTP configuration.'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send test email', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the test email: ' . $e->getMessage()
            ], 500);
        }
    }
}
