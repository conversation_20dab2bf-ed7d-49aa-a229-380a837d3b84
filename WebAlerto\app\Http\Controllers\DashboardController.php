<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evacuation;
use App\Models\User;
use App\Models\Notification;
use App\Models\Barangay;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if admin user is active
        $adminRoles = ['admin', 'super_admin', 'chairman', 'officer', 'assistant'];
        if (in_array($user->role, $adminRoles) && $user->status !== 'Active') {
            Auth::logout();
            return redirect()->route('login')->withErrors([
                'email' => 'Your account has been deactivated. Please contact the system administrator.'
            ]);
        }

        // Get all barangays for the filter dropdown
        $barangays = Barangay::pluck('name')->unique();
        
        // Get selected barangay from request (for super admin filtering)
        $selectedBarangay = $request->input('barangay');
        
        // Get search and filter parameters for map section
        $searchQuery = $request->input('search');
        $disasterFilter = $request->input('disaster_type', 'All');

        // Base queries with role-based filtering
        $notificationQuery = Notification::query();
        $evacuationQuery = Evacuation::query();

        // Apply role-based filtering
        if (!$user->hasRole('super_admin')) {
            // Barangay user - only their barangay
            $notificationQuery->where('barangay', $user->barangay);
            $evacuationQuery->where('barangay', $user->barangay);
        } else if ($selectedBarangay) {
            // Super_admin with selected barangay filter
            $notificationQuery->where('barangay', $selectedBarangay);
            $evacuationQuery->where('barangay', $selectedBarangay);
        }
        // Super_admin without filter - all barangays (no additional where clause)

        // Apply search filter for map section
        if ($searchQuery) {
            $evacuationQuery->where('name', 'like', '%' . $searchQuery . '%');
        }

        // Apply disaster filter for map section
        if ($disasterFilter !== 'All') {
            $evacuationQuery->whereJsonContains('disaster_type', $disasterFilter);
        }

        // Get total counts
        $totalAlerts = $notificationQuery->count();
        $activeCenters = (clone $evacuationQuery)->where('status', 'Active')->count();

        // Get recent alerts (last 5)
        $recentAlerts = (clone $notificationQuery)->orderBy('created_at', 'desc')
                                  ->take(5)
                                  ->get()
                                  ->map(function($notification) {
                                      return (object) [
                                          'type' => $notification->category,
                                          'message' => $notification->title,
                                          'location' => $notification->barangay,
                                          'time_ago' => $notification->created_at->diffForHumans()
                                      ];
                                  });

        // Get evacuation centers with coordinates (using same approach as MappingSystemController)
        $evacuationQuery = Evacuation::query();

        // Apply role-based filtering (same as MappingSystemController)
        if (!$user->hasRole('super_admin')) {
            $evacuationQuery->where('barangay', $user->barangay);
        } else if ($selectedBarangay && $selectedBarangay !== 'All') {
            $evacuationQuery->where('barangay', $selectedBarangay);
        }
        // Super_admin: no filter, get all if no barangay selected

        $evacuationCenters = $evacuationQuery->get()->map(function($center) {
            // Create a formatted address from the separate location fields (same as MappingSystemController)
            $addressParts = [
                $center->building_name,
                $center->street_name,
                $center->barangay,
                $center->city,
                $center->province,
            ];
            
            // Clean up parts and remove duplicates/empties
            $addressParts = array_map('trim', $addressParts);
            $addressParts = array_filter($addressParts);
            $addressParts = array_unique($addressParts);
            
            $address = implode(', ', $addressParts);
            
            // Ensure "Philippines" is at the end, but only once
            $address = trim(str_ireplace('Philippines', '', $address), ' ,');
            $address .= ', Philippines';
            
            return [
                'id' => $center->id,
                'name' => $center->name,
                'address' => $address,
                'latitude' => $center->latitude,
                'longitude' => $center->longitude,
                'capacity' => $center->capacity,
                'status' => $center->status,
                'disaster_type' => $center->disaster_type,
                'contact' => $center->contact,
                'barangay' => $center->barangay
            ];
        });

        $totalCenters = Evacuation::count();
        $totalBarangays = count($barangays);

        // Chart Data - Monthly Trends (Last 6 months)
        $monthlyData = $this->getMonthlyTrends($user, $selectedBarangay);
        
        // Chart Data - Disaster Type Distribution
        $disasterTypeData = $this->getDisasterTypeDistribution($user, $selectedBarangay);
        
        // Chart Data - Barangay-wise Statistics
        $barangayStatsData = $this->getBarangayStatistics($user, $barangays, $selectedBarangay);

        return view('components.dashboard', compact(
            'totalAlerts',
            'activeCenters',
            'recentAlerts',
            'evacuationCenters',
            'barangays',
            'selectedBarangay',
            'totalCenters',
            'totalBarangays',
            'monthlyData',
            'disasterTypeData',
            'barangayStatsData',
            'searchQuery',
            'disasterFilter'
        ));
    }

    private function getMonthlyTrends($user, $selectedBarangay)
    {
        $months = [];
        $alertData = [];
        $centerData = [];
        
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthName = $date->format('M Y');
            $months[] = $monthName;
            
            // Get alerts for this month based on user role
            $alertQuery = Notification::query();
            if (!$user->hasRole('super_admin')) {
                // Barangay user - only their barangay
                $alertQuery->where('barangay', $user->barangay);
            } else if ($selectedBarangay) {
                // Super_admin with selected barangay filter
                $alertQuery->where('barangay', $selectedBarangay);
            }
            // Super_admin without filter - all barangays (no additional where clause)
            
            $alertCount = $alertQuery
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $alertData[] = $alertCount;
            
            // Get active centers for this month based on user role
            $centerQuery = Evacuation::query();
            if (!$user->hasRole('super_admin')) {
                // Barangay user - only their barangay
                $centerQuery->where('barangay', $user->barangay);
            } else if ($selectedBarangay) {
                // Super_admin with selected barangay filter
                $centerQuery->where('barangay', $selectedBarangay);
            }
            // Super_admin without filter - all barangays (no additional where clause)
            
            $centerCount = $centerQuery
                ->where('status', 'Active')
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $centerData[] = $centerCount;
        }
        
        return [
            'months' => $months,
            'alerts' => $alertData,
            'centers' => $centerData
        ];
    }

    private function getDisasterTypeDistribution($user, $selectedBarangay)
    {
        $disasterQuery = Evacuation::query();
        
        // Apply role-based filtering
        if (!$user->hasRole('super_admin')) {
            // Barangay user - only their barangay
            $disasterQuery->where('barangay', $user->barangay);
        } else if ($selectedBarangay) {
            // Super_admin with selected barangay filter
            $disasterQuery->where('barangay', $selectedBarangay);
        }
        // Super_admin without filter - all barangays (no additional where clause)
        
        // Get all active evacuation centers
        $centers = $disasterQuery
            ->where('status', 'Active')
            ->get();
        
        // Process disaster types from JSON arrays
        $disasterTypeCounts = [];
        $colors = [
            'Typhoon' => '#22c55e',
            'Flood' => '#3b82f6',
            'Fire' => '#ef4444',
            'Earthquake' => '#f59e42',
            'Landslide' => '#a16207',
            'Others' => '#9333ea'
        ];
        
        foreach ($centers as $center) {
            $disasterTypes = $center->disaster_type;
            
            // Handle both array and string formats
            if (is_string($disasterTypes)) {
                $disasterTypes = json_decode($disasterTypes, true);
            }
            
            if (is_array($disasterTypes)) {
                foreach ($disasterTypes as $type) {
                    // Handle "Others: custom_type" format
                    if (strpos($type, 'Others:') === 0) {
                        $type = 'Others';
                    }
                    
                    if (!isset($disasterTypeCounts[$type])) {
                        $disasterTypeCounts[$type] = 0;
                    }
                    $disasterTypeCounts[$type]++;
                }
            } else {
                // Handle single string value
                $type = $disasterTypes;
                if (strpos($type, 'Others:') === 0) {
                    $type = 'Others';
                }
                
                if (!isset($disasterTypeCounts[$type])) {
                    $disasterTypeCounts[$type] = 0;
                }
                $disasterTypeCounts[$type]++;
            }
        }
        
        $labels = [];
        $data = [];
        $chartColors = [];
        
        foreach ($disasterTypeCounts as $type => $count) {
            $labels[] = $type;
            $data[] = $count;
            $chartColors[] = $colors[$type] ?? '#6b7280'; // Default gray for unknown types
        }
        
        return [
            'labels' => $labels,
            'data' => $data,
            'colors' => $chartColors
        ];
    }

    private function getBarangayStatistics($user, $specificBarangays, $selectedBarangay)
    {
        $barangayStats = [];
        
        foreach ($specificBarangays as $barangay) {

            // Skip if super_admin has selected a specific barangay and this isn't it

            if ($selectedBarangay && $selectedBarangay !== $barangay) {
                continue;
            }
            
            // Get evacuation centers count for this barangay
            $centerQuery = Evacuation::where('barangay', $barangay);

            if (!$user->hasRole('super_admin')) {

                // Barangay user - only their barangay
                if ($user->barangay !== $barangay) {
                    continue;
                }
            }
            
            $totalCenters = $centerQuery->count();
            $activeCenters = $centerQuery->where('status', 'Active')->count();
            
            // Get notifications count for this barangay
            $notificationQuery = Notification::where('barangay', $barangay);

            if (!$user->hasRole('super_admin')) {

                // Barangay user - only their barangay
                if ($user->barangay !== $barangay) {
                    continue;
                }
            }
            
            $totalNotifications = $notificationQuery->count();
            
            $barangayStats[] = [
                'name' => $barangay,
                'total_centers' => $totalCenters,
                'active_centers' => $activeCenters,
                'total_notifications' => $totalNotifications
            ];
        }
        
        return $barangayStats;
    }

    /**
     * Get admin registration data for dashboard
     */
    public function getAdminRegistrationData()
    {
        $user = Auth::user();
        
        // Only System Administrators can access registration data
        if (!$user->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Only System Administrators can access registration data.'
            ], 403);
        }

        // Get available barangays for dropdown

        $barangays = Barangay::pluck('name')->toArray();


        // Get roles with descriptions
        $roles = [
            'admin' => 'City DRRMO Director (City-wide monitoring, access to all barangays)',
            'chairman' => 'BDRRMO Chairman (Barangay-specific access)',
            'officer' => 'BDRRMO Officer (Barangay-specific access)',
            'assistant' => 'BDRRMO Assistant (Barangay-specific access)'
        ];

        // Get access information
        $accessInfo = [
            'super_admin' => 'System Administrator - Full system access',
            'admin' => 'City Hall Admin - City-wide monitoring, access to all barangays',
            'chairman' => 'BDRRMO Chairman - Barangay-specific access only',
            'officer' => 'BDRRMO Officer - Barangay-specific access only',
            'assistant' => 'BDRRMO Assistant - Barangay-specific access only'
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'barangays' => $barangays,
                'roles' => $roles,
                'access_info' => $accessInfo
            ]
        ]);
    }
}



