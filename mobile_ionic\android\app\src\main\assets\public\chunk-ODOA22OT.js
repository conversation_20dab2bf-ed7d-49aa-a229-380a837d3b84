import{a as z}from"./chunk-QZ5PEPJK.js";import"./chunk-I4SN7ED3.js";import{a as L}from"./chunk-LSM7X32V.js";import"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{$b as N,B as r,C as M,Eb as b,F as x,G as s,I as f,Ib as w,J as t,Jb as E,K as n,L as m,O as C,P as _,Q as c,X as o,Y as u,Yb as k,Z as P,Zb as R,ca as S,gc as V,m as h,ma as D,na as T,pa as y,r as g,s as p,wb as O,xb as I}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-2R6CW7ES.js";function F(i,d){if(i&1){let e=C();t(0,"div",24),_("click",function(){let a=g(e).$implicit,v=c(2);return p(v.selectDestination(a))}),m(1,"ion-icon",18),t(2,"span"),o(3),n()()}if(i&2){let e=d.$implicit,l=c(2);f("selected",(l.selectedDestination==null?null:l.selectedDestination.name)===e.name),r(3),u(e.name)}}function $(i,d){if(i&1&&(t(0,"div",8)(1,"h2"),o(2,"\u{1F4CD} Select Destination"),n(),t(3,"div",22),x(4,F,4,3,"div",23),n()()),i&2){let e=c();r(4),s("ngForOf",e.demoDestinations)}}function U(i,d){if(i&1){let e=C();t(0,"div",8)(1,"h2"),o(2,"\u{1F6B6}\u200D\u2642\uFE0F Select Travel Mode"),n(),t(3,"div",25)(4,"div",26),_("click",function(){g(e);let a=c();return p(a.selectTravelMode("foot-walking"))}),m(5,"ion-icon",27),t(6,"span"),o(7,"Walking"),n()(),t(8,"div",26),_("click",function(){g(e);let a=c();return p(a.selectTravelMode("cycling-regular"))}),m(9,"ion-icon",28),t(10,"span"),o(11,"Cycling"),n()(),t(12,"div",26),_("click",function(){g(e);let a=c();return p(a.selectTravelMode("driving-car"))}),m(13,"ion-icon",21),t(14,"span"),o(15,"Driving"),n()()()()}if(i&2){let e=c();r(4),f("selected",e.selectedTravelMode==="foot-walking"),r(4),f("selected",e.selectedTravelMode==="cycling-regular"),r(4),f("selected",e.selectedTravelMode==="driving-car")}}function B(i,d){if(i&1){let e=C();t(0,"app-real-time-navigation",29),_("navigationStarted",function(){g(e);let a=c();return p(a.onNavigationStarted())})("navigationStopped",function(){g(e);let a=c();return p(a.onNavigationStopped())})("routeUpdated",function(a){g(e);let v=c();return p(v.onRouteUpdated(a))})("instructionChanged",function(a){g(e);let v=c();return p(v.onInstructionChanged(a))}),n()}if(i&2){let e=c();s("destination",e.selectedDestination)("travelMode",e.selectedTravelMode)}}function A(i,d){if(i&1&&(t(0,"div",31)(1,"span",32),o(2,"Distance"),n(),t(3,"span",33),o(4),n()()),i&2){let e=c(2);r(4),u(e.routingService.formatDistance(e.currentRoute.distance))}}function H(i,d){if(i&1&&(t(0,"div",31)(1,"span",32),o(2,"Duration"),n(),t(3,"span",33),o(4),n()()),i&2){let e=c(2);r(4),u(e.routingService.formatDuration(e.currentRoute.duration))}}function W(i,d){if(i&1&&(t(0,"div",8)(1,"h2"),o(2,"\u{1F4CA} Navigation Status"),n(),t(3,"div",30)(4,"div",31)(5,"span",32),o(6,"Destination"),n(),t(7,"span",33),o(8),n()(),t(9,"div",31)(10,"span",32),o(11,"Travel Mode"),n(),t(12,"span",33),o(13),n()(),x(14,A,5,1,"div",34)(15,H,5,1,"div",34),n()()),i&2){let e=c();r(8),u(e.selectedDestination==null?null:e.selectedDestination.name),r(5),u(e.getTravelModeLabel(e.selectedTravelMode)),r(),s("ngIf",e.currentRoute),r(),s("ngIf",e.currentRoute)}}function j(i,d){if(i&1&&(t(0,"div",39),o(1),n()),i&2){let e=c(2);r(),P(" in ",e.routingService.formatDistance(e.currentInstruction.distance)," ")}}function G(i,d){if(i&1&&(t(0,"div",8)(1,"h2"),o(2,"\u{1F4CD} Current Instruction"),n(),t(3,"div",35)(4,"div",36)(5,"div",37),o(6),n(),x(7,j,2,1,"div",38),n()()()),i&2){let e=c();r(6),u(e.currentInstruction.instruction),r(),s("ngIf",e.currentInstruction.distance)}}function q(i,d){if(i&1&&(t(0,"div",40),o(1),n()),i&2){let e=d.$implicit;r(),u(e)}}function J(i,d){i&1&&(t(0,"div",41),o(1," No logs yet. Start navigation to see real-time updates. "),n())}var ne=(()=>{class i{constructor(e){this.routingService=e,this.demoDestinations=[{name:"Cebu City Hall",lat:10.2935,lng:123.9015},{name:"Ayala Center Cebu",lat:10.3181,lng:123.9058},{name:"SM City Cebu",lat:10.3369,lng:123.9233},{name:"University of San Carlos",lat:10.3017,lng:123.8947}],this.selectedDestination=null,this.selectedTravelMode="foot-walking",this.isNavigating=!1,this.currentRoute=null,this.currentInstruction=null,this.logs=[]}ngOnInit(){this.addLog("\u{1F680} Real-time Navigation Demo initialized")}selectDestination(e){this.selectedDestination=e,this.addLog(`\u{1F4CD} Selected destination: ${e.name}`)}selectTravelMode(e){this.selectedTravelMode=e,this.addLog(`\u{1F6B6}\u200D\u2642\uFE0F Selected travel mode: ${e}`)}onNavigationStarted(){this.isNavigating=!0,this.addLog("\u{1F9ED} Real-time navigation started")}onNavigationStopped(){this.isNavigating=!1,this.currentRoute=null,this.currentInstruction=null,this.addLog("\u23F9\uFE0F Real-time navigation stopped")}onRouteUpdated(e){this.currentRoute=e;let l=this.routingService.getRouteSummary(e);this.addLog(`\u{1F504} Route updated: ${l.distanceText}, ${l.durationText}`)}onInstructionChanged(e){this.currentInstruction=e,this.addLog(`\u{1F4CD} Navigation instruction: ${e.instruction}`)}clearLogs(){this.logs=[]}addLog(e){let l=new Date().toLocaleTimeString();this.logs.unshift(`[${l}] ${e}`),this.logs.length>20&&(this.logs=this.logs.slice(0,20)),console.log(e)}getTravelModeIcon(e){return{"foot-walking":"walk","cycling-regular":"bicycle","driving-car":"car"}[e]||"walk"}getTravelModeLabel(e){return{"foot-walking":"Walking","cycling-regular":"Cycling","driving-car":"Driving"}[e]||"Walking"}static{this.\u0275fac=function(l){return new(l||i)(M(L))}}static{this.\u0275cmp=h({type:i,selectors:[["app-real-time-demo"]],standalone:!0,features:[S],decls:52,vars:9,consts:[[3,"translucent"],["slot","start"],["defaultHref","/"],[3,"fullscreen"],[1,"demo-container"],[1,"header-section"],["class","section",4,"ngIf"],[3,"destination","travelMode","navigationStarted","navigationStopped","routeUpdated","instructionChanged",4,"ngIf"],[1,"section"],[1,"logs-header"],["fill","clear","size","small",3,"click"],["name","trash","slot","start"],[1,"logs-container"],["class","log-entry",4,"ngFor","ngForOf"],["class","no-logs",4,"ngIf"],[1,"features-list"],[1,"feature-item"],["name","refresh","color","primary"],["name","location","color","primary"],["name","navigate","color","primary"],["name","time","color","primary"],["name","car","color","primary"],[1,"destination-grid"],["class","destination-card",3,"selected","click",4,"ngFor","ngForOf"],[1,"destination-card",3,"click"],[1,"travel-mode-grid"],[1,"mode-card",3,"click"],["name","walk","color","primary"],["name","bicycle","color","primary"],[3,"navigationStarted","navigationStopped","routeUpdated","instructionChanged","destination","travelMode"],[1,"status-grid"],[1,"status-item"],[1,"label"],[1,"value"],["class","status-item",4,"ngIf"],[1,"instruction-card"],[1,"instruction-content"],[1,"instruction-text"],["class","instruction-distance",4,"ngIf"],[1,"instruction-distance"],[1,"log-entry"],[1,"no-logs"]],template:function(l,a){l&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),o(3,"Real-time Navigation Demo"),n(),t(4,"ion-buttons",1),m(5,"ion-back-button",2),n()()(),t(6,"ion-content",3)(7,"div",4)(8,"div",5)(9,"h1"),o(10,"\u{1F9ED} Real-time Navigation Demo"),n(),t(11,"p"),o(12,"Test the real-time navigation features with live route updates"),n()(),x(13,$,5,1,"div",6)(14,U,16,6,"div",6)(15,B,1,2,"app-real-time-navigation",7)(16,W,16,4,"div",6)(17,G,8,2,"div",6),t(18,"div",8)(19,"div",9)(20,"h2"),o(21,"\u{1F4DD} Navigation Logs"),n(),t(22,"ion-button",10),_("click",function(){return a.clearLogs()}),m(23,"ion-icon",11),o(24," Clear "),n()(),t(25,"div",12),x(26,q,2,1,"div",13)(27,J,2,0,"div",14),n()(),t(28,"div",8)(29,"h2"),o(30,"\u2728 Real-time Features"),n(),t(31,"div",15)(32,"div",16),m(33,"ion-icon",17),t(34,"span"),o(35,"Automatic route updates every 15-30 seconds"),n()(),t(36,"div",16),m(37,"ion-icon",18),t(38,"span"),o(39,"Position tracking with GPS"),n()(),t(40,"div",16),m(41,"ion-icon",19),t(42,"span"),o(43,"Turn-by-turn navigation instructions"),n()(),t(44,"div",16),m(45,"ion-icon",20),t(46,"span"),o(47,"Live ETA and distance updates"),n()(),t(48,"div",16),m(49,"ion-icon",21),t(50,"span"),o(51,"Multiple travel modes (walking, cycling, driving)"),n()()()()()()),l&2&&(s("translucent",!0),r(6),s("fullscreen",!0),r(7),s("ngIf",!a.isNavigating),r(),s("ngIf",!a.isNavigating&&a.selectedDestination),r(),s("ngIf",a.selectedDestination),r(),s("ngIf",a.isNavigating),r(),s("ngIf",a.currentInstruction),r(9),s("ngForOf",a.logs),r(),s("ngIf",a.logs.length===0))},dependencies:[y,D,T,V,O,I,b,w,E,k,R,N,z],styles:[".demo-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.header-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:30px}.header-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--ion-color-primary);margin-bottom:10px}.header-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px}.section[_ngcontent-%COMP%]{margin-bottom:30px}.section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-primary);margin-bottom:15px;font-size:18px}.destination-grid[_ngcontent-%COMP%], .travel-mode-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px}.destination-card[_ngcontent-%COMP%], .mode-card[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:20px;border-radius:12px;background:var(--ion-color-light);border:2px solid transparent;cursor:pointer;transition:all .3s ease}.destination-card[_ngcontent-%COMP%]:hover, .mode-card[_ngcontent-%COMP%]:hover{background:var(--ion-color-light-shade)}.destination-card.selected[_ngcontent-%COMP%], .mode-card.selected[_ngcontent-%COMP%]{background:var(--ion-color-primary-tint);border-color:var(--ion-color-primary);color:var(--ion-color-primary-shade)}.destination-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], .mode-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:32px;margin-bottom:10px}.destination-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .mode-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;text-align:center}.status-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:15px}.status-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:15px;background:var(--ion-color-light);border-radius:8px;text-align:center}.status-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);font-weight:500;margin-bottom:5px}.status-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:16px;font-weight:700;color:var(--ion-color-dark)}.instruction-card[_ngcontent-%COMP%]{background:var(--ion-color-primary);color:#fff;border-radius:12px;padding:20px}.instruction-content[_ngcontent-%COMP%]   .instruction-text[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:8px}.instruction-content[_ngcontent-%COMP%]   .instruction-distance[_ngcontent-%COMP%]{font-size:14px;opacity:.9}.logs-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}.logs-container[_ngcontent-%COMP%]{background:var(--ion-color-dark);color:var(--ion-color-light);border-radius:8px;padding:15px;max-height:300px;overflow-y:auto;font-family:Courier New,monospace;font-size:12px}.log-entry[_ngcontent-%COMP%]{margin-bottom:5px;word-wrap:break-word}.log-entry[_ngcontent-%COMP%]:last-child{margin-bottom:0}.no-logs[_ngcontent-%COMP%]{text-align:center;color:var(--ion-color-medium);font-style:italic}.features-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.feature-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;background:var(--ion-color-light);border-radius:8px}.feature-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:15px;flex-shrink:0}.feature-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;line-height:1.4}@media (max-width: 768px){.demo-container[_ngcontent-%COMP%]{padding:15px}.destination-grid[_ngcontent-%COMP%], .travel-mode-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.status-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.logs-container[_ngcontent-%COMP%]{max-height:200px;font-size:11px}}.destination-card.selected[_ngcontent-%COMP%], .mode-card.selected[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse .3s ease-out}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}"]})}}return i})();export{ne as RealTimeDemoPage};
