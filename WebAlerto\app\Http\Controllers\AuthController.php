<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class AuthController extends Controller
{
    /**
     * Register a new user for mobile app
     */
    public function signup(Request $request)
    {
        \Log::info('Registration attempt', [
            'email' => $request->email,
            'has_password' => !empty($request->password),
            'has_password_confirmation' => !empty($request->password_confirmation),
            'request_data' => $request->except(['password', 'password_confirmation'])
        ]);

        // Check if email belongs to a deactivated user
        $existingUser = User::where('email', $request->email)->first();
        if ($existingUser) {
            if ($existingUser->status !== 'Active') {
                \Log::warning('Registration attempt with deactivated user email', [
                    'email' => $request->email,
                    'existing_user_status' => $existingUser->status,
                    'existing_user_id' => $existingUser->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'This email address is associated with a deactivated account. Please contact the administrator.',
                    'errors' => ['email' => ['This email address is associated with a deactivated account.']]
                ], 422);
            } else {
                // Email exists and user is active
                return response()->json([
                    'success' => false,
                    'message' => 'This email address is already registered.',
                    'errors' => ['email' => ['The email has already been taken.']]
                ], 422);
            }
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'password_confirmation' => 'required|string|same:password',
        ]);

        if ($validator->fails()) {
            \Log::warning('Registration validation failed', [
                'email' => $request->email,
                'errors' => $validator->errors()->toArray()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Extract username from email for default names
            $emailUsername = explode('@', $request->email)[0];

            \Log::info('Creating user', [
                'email' => $request->email,
                'first_name' => $emailUsername
            ]);

            // Create the user
            $user = User::create([
                'first_name' => $emailUsername, // Use email username as first name
                'last_name' => 'User', // Default last name
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'position' => 'Mobile User', // Default position for mobile users
                'barangay' => 'Mobile', // Default barangay for mobile users
                'role' => 'mobile_user', // Default role for mobile users
                'status' => 'Active', // Default status for mobile users
            ]);

            \Log::info('User created successfully', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User registered successfully',
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                ]
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Registration failed', [
                'email' => $request->email,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user for mobile app
     */
    public function login(Request $request)
    {
        // Log the login attempt
        \Log::info('Login attempt received', [
            'email' => $request->input('email'),
            'has_password' => !empty($request->input('password')),
            'request_data' => $request->except(['password'])
        ]);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Log the authentication attempt
            \Log::info('Attempting authentication', [
                'email' => $request->input('email'),
                'password_length' => strlen($request->input('password'))
            ]);

            // Check if user exists and is an admin/web user, then check status
            $user = User::where('email', $request->input('email'))->first();

            if ($user && in_array($user->role, ['admin', 'super_admin', 'chairman', 'officer', 'assistant']) && $user->status !== 'Active') {
                \Log::warning('Login attempt by inactive admin user', [
                    'email' => $request->input('email'),
                    'status' => $user->status,
                    'role' => $user->role,
                    'user_id' => $user->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Your admin account has been deactivated. Please contact the system administrator.'
                ], 403);
            }

            // Attempt to authenticate the user
            if (!Auth::attempt($request->only('email', 'password'))) {
                \Log::warning('Authentication failed', [
                    'email' => $request->input('email'),
                    'user_exists' => User::where('email', $request->input('email'))->exists()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'The email and password didn\'t match'
                ], 401);
            }

            $user = Auth::user();

            // Create a token for the user (using Sanctum)
            $token = $user->createToken('mobile-app-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                    'position' => $user->position,
                    'barangay' => $user->barangay,
                ],
                'token' => $token
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user (revoke token)
     */
    public function logout(Request $request)
    {
        try {
            // Revoke the current user's token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logged out successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Logout failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
