<?php

namespace App\Http\Controllers;

use App\Models\Invitation;
use App\Models\User;

use App\Models\Barangay;

use App\Services\EmailNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Auth;


class AdminInvitationController extends Controller
{
    protected $emailService;

    public function __construct(EmailNotificationService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Display a listing of invitations
     */
    public function index(Request $request)
    {
        $query = Invitation::with('invitedBy');

        // Filter by status
        if ($request->has('status')) {
            switch ($request->status) {
                case 'pending':
                    $query->pending();
                    break;
                case 'accepted':
                    $query->accepted();
                    break;
                case 'expired':
                    $query->expired();
                    break;
            }
        }

        // Filter by barangay (for non-super admins)
        if (!auth()->user()->hasRole('super_admin')) {
            $query->where('barangay', auth()->user()->barangay);
        }

        $invitations = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $invitations
        ]);
    }

    /**
     * Send a new admin invitation
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email|unique:invitations,email',
            'title' => 'nullable|string|max:50',
            'first_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'last_name' => 'required|string|max:100',
            'suffix' => 'nullable|string|max:20',
            'position' => 'required|string|max:100',
            'barangay' => 'required|string|max:100',
            'role' => 'required|in:admin,chairman,officer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check permissions
        $currentUser = auth()->user();
        if (!$currentUser->hasRole('super_admin') && $request->barangay !== $currentUser->barangay) {
            return response()->json([
                'success' => false,
                'message' => 'You can only invite users to your own barangay'
            ], 403);
        }

        try {
            $invitation = $this->emailService->sendAdminInvitation($request->all(), $currentUser);

            if ($invitation) {
                return response()->json([
                    'success' => true,
                    'message' => 'Invitation sent successfully',
                    'data' => $invitation
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send invitation'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send admin invitation', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the invitation'
            ], 500);
        }
    }

    /**
     * Display the specified invitation
     */
    public function show(Invitation $invitation)
    {
        // Check permissions
        $currentUser = auth()->user();
        if (!$currentUser->hasRole('super_admin') && $invitation->barangay !== $currentUser->barangay) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $invitation->load('invitedBy')
        ]);
    }

    /**
     * Resend an invitation
     */
    public function resend(Invitation $invitation)
    {
        // Check permissions
        $currentUser = auth()->user();
        if (!$currentUser->hasRole('super_admin') && $invitation->barangay !== $currentUser->barangay) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($invitation->isAccepted()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot resend an accepted invitation'
            ], 400);
        }

        try {
            $result = $this->emailService->resendAdminInvitation($invitation);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Invitation resent successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to resend invitation'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to resend admin invitation', [
                'error' => $e->getMessage(),
                'invitation_id' => $invitation->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while resending the invitation'
            ], 500);
        }
    }

    /**
     * Cancel an invitation
     */
    public function cancel(Invitation $invitation)
    {
        // Check permissions
        $currentUser = auth()->user();
        if (!$currentUser->hasRole('super_admin') && $invitation->barangay !== $currentUser->barangay) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($invitation->isAccepted()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel an accepted invitation'
            ], 400);
        }

        try {
            $invitation->markAsExpired();

            return response()->json([
                'success' => true,
                'message' => 'Invitation cancelled successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel invitation', [
                'error' => $e->getMessage(),
                'invitation_id' => $invitation->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cancelling the invitation'
            ], 500);
        }
    }

    /**
     * Accept invitation and update user account
     */
    public function acceptInvitation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'token' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'title' => 'nullable|string|max:50',
            'middle_name' => 'nullable|string|max:100',
            'suffix' => 'nullable|string|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $invitation = Invitation::where('email', $request->email)
                                   ->where('token', $request->token)
                                   ->where('status', 'pending')
                                   ->where('expires_at', '>', now())
                                   ->first();

            if (!$invitation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired invitation'
                ], 400);
            }

            // Find the existing user account
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User account not found'
                ], 400);
            }

            // Update user account with personal information and new password
            $user->update([
                'title' => $request->title,
                'first_name' => $request->first_name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'suffix' => $request->suffix,
                'password' => Hash::make($request->password),
                'status' => 'Active' // Activate the account
            ]);

            // Mark invitation as accepted
            $invitation->markAsAccepted();

            // Send welcome notification
            $this->emailService->notifyUser(
                $user,
                'Welcome to ALERTO',
                'Your admin account has been successfully activated. You can now access the ALERTO dashboard.',
                'account_activated'
            );

            Log::info('Admin invitation accepted and user account updated', [
                'user_id' => $user->id,
                'email' => $user->email,
                'invitation_id' => $invitation->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Account activated successfully. You can now log in.',
                'data' => [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'redirect_url' => '/login'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to accept invitation', [
                'error' => $e->getMessage(),
                'email' => $request->email
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while activating your account'
            ], 500);
        }
    }

    /**
     * Clean up expired invitations
     */
    public function cleanupExpired()
    {
        try {
            $expiredCount = $this->emailService->cleanupExpiredInvitations();

            return response()->json([
                'success' => true,
                'message' => "Cleaned up {$expiredCount} expired invitations"
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cleanup expired invitations', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while cleaning up expired invitations'
            ], 500);
        }
    }

    /**
     * Show invitation acceptance form
     */
    public function showAcceptForm(Request $request)
    {
        $email = $request->get('email');
        $token = $request->get('token');

        if (!$email || !$token) {
            return view('components.invitation.accept-invitation')->withErrors(['Invalid invitation link.']);
        }

        $invitation = Invitation::where('email', $email)
                               ->where('token', $token)
                               ->where('status', 'pending')
                               ->where('expires_at', '>', now())
                               ->first();

        if (!$invitation) {
            return view('components.invitation.accept-invitation')->withErrors(['Invalid or expired invitation.']);
        }

        // Pass invitation data to the view for pre-filling if needed
        return view('components.invitation.accept-invitation', [
            'invitation' => $invitation,
            'email' => $email,
            'token' => $token
        ]);
    }

    /**
     * Register a new admin account (System Administrator only)
     */
    public function registerAdmin(Request $request)
    {
        // Check if user is System Administrator
        if (!auth()->user()->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Only System Administrators can register admin accounts.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email|unique:invitations,email',
            'barangay' => 'required|string|max:100',
            'role' => 'required|in:admin,chairman,officer,assistant'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate City Hall admin role
        if ($request->barangay === 'City Hall' && $request->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'City Hall users must have admin role for city-wide monitoring access.'
            ], 422);
        }

        // Validate barangay-specific roles
        if ($request->barangay !== 'City Hall' && $request->role === 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Admin role is only available for City Hall users. Barangay users should use chairman, officer, or assistant roles.'
            ], 422);
        }

        try {
            // Create user account in database with null personal information
            $user = User::create([
                'email' => $request->email,
                'title' => null,
                'first_name' => null, // Will be filled by user later
                'middle_name' => null,
                'last_name' => null, // Will be filled by user later
                'suffix' => null,
                'position' => null,
                'barangay' => $request->barangay,
                'role' => $request->role,
                'password' => Hash::make('temp_password_' . time()), // Temporary password
                'status' => 'Pending' // User needs to set password
            ]);

            // Create invitation data
            $invitationData = [
                'email' => $request->email,
                'title' => null,
                'first_name' => null,
                'middle_name' => null,
                'last_name' => null,
                'suffix' => null,
                'position' => null,
                'barangay' => $request->barangay,
                'role' => $request->role,
                'user_id' => $user->id // Link to the created user
            ];

            $invitation = $this->emailService->sendAdminInvitation($invitationData, auth()->user());

            if ($invitation) {
                Log::info('Admin account registered by System Administrator', [
                    'user_id' => $user->id,
                    'email' => $request->email,
                    'barangay' => $request->barangay,
                    'role' => $request->role,
                    'registered_by' => auth()->user()->id
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Admin account registered successfully. Invitation email has been sent.',
                    'data' => [
                        'user' => $user,
                        'invitation' => $invitation
                    ]
                ]);
            } else {
                // If invitation failed, delete the created user
                $user->delete();
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send invitation email. Account registration cancelled.'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to register admin account', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while registering the admin account'
            ], 500);
        }
    }

    /**
     * Show the admin registration form (System Administrator only)
     */
    public function showRegistrationForm()
    {
        // Check if user is System Administrator
        if (!auth()->user()->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Only System Administrators can register admin accounts.'
            ], 403);
        }

        // Get available barangays for dropdown

        $barangays = Barangay::pluck('name')->toArray();


        return response()->json([
            'success' => true,
            'data' => [
                'barangays' => $barangays,
                'roles' => [
                    'admin' => 'City DRRMO Director (City-wide monitoring, access to all barangays)',
                    'chairman' => 'BDRRMO Chairman (Barangay-specific access)',
                    'officer' => 'BDRRMO Officer (Barangay-specific access)',
                    'assistant' => 'BDRRMO Assistant (Barangay-specific access)'
                ],
                'access_info' => [
                    'super_admin' => 'System Administrator - Full system access',
                    'admin' => 'City Hall Admin - City-wide monitoring, access to all barangays',
                    'chairman' => 'BDRRMO Chairman - Barangay-specific access only',
                    'officer' => 'BDRRMO Officer - Barangay-specific access only',
                    'assistant' => 'BDRRMO Assistant - Barangay-specific access only'
                ]
            ]
        ]);
    }

    /**
     * Show the separate admin invitation form (System Administrator only)
     */
    public function showInviteForm()
    {
        if (!auth()->user()->hasRole('super_admin')) {
            abort(403, 'Access denied. Only System Administrators can send invitations.');
        }
        return view('components.invitation.invite-admin');
    }

    /**
     * Send an admin invitation (separate from registration, does not create user)
     */
    public function sendInvite(Request $request)
    {
        if (!auth()->user()->hasRole('super_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Only System Administrators can send invitations.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email|unique:invitations,email',
            'position' => 'required|string|max:100',
            'barangay' => 'required|string|max:100',
            'role' => 'required|in:admin,chairman,officer,assistant'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate City Hall admin role
        if ($request->barangay === 'City Hall' && $request->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'City Hall users must have admin role for city-wide monitoring access.'
            ], 422);
        }
        if ($request->barangay !== 'City Hall' && $request->role === 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Admin role is only available for City Hall users. Barangay users should use chairman, officer, or assistant roles.'
            ], 422);
        }

        try {
            $invitationData = [
                'email' => $request->email,
                'title' => null,
                'first_name' => null,
                'middle_name' => null,
                'last_name' => null,
                'suffix' => null,
                'position' => $request->position,
                'barangay' => $request->barangay,
                'role' => $request->role
            ];
            $invitation = $this->emailService->sendAdminInvitation($invitationData, auth()->user());
            if ($invitation) {
                return response()->json([
                    'success' => true,
                    'message' => 'Invitation sent successfully. The user will receive an email with setup instructions.',
                    'data' => $invitation
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send invitation.'
                ], 500);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send separate admin invitation', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the invitation.'
            ], 500);
        }
    }
}
