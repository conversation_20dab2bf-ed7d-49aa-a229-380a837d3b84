// import 'leaflet/dist/leaflet.css';
import { MapManager } from './push_notification/map/MapManager.js';
import { MapSearch } from './push_notification/map/MapSearch.js';
import { MapZones, SeverityManager } from './push_notification/map/MapZone.js';

class NotificationApp {
    constructor() {
        this.mapManager = new MapManager('map');
        this.mapSearch = new MapSearch(this.mapManager);
        this.severityManager = new SeverityManager();
        this.mapZones = new MapZones(this.mapManager, this.severityManager);
        this.affectedAreas = [];
    }

    initialize() {
        this.mapManager.initialize();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Severity changes
        document.querySelectorAll('input[name="severity"]').forEach(radio => {
            radio.addEventListener('change', this.handleSeverityChange.bind(this));
        });

        // Affected areas
        const addAreaBtn = document.getElementById('add-area-btn');
        if (addAreaBtn) {
            addAreaBtn.addEventListener('click', this.handleAddAffectedArea.bind(this));
        }
    }

    async handleSearch(event) {
        const query = event.target.value.trim();
        if (query.length < 2) return;

        try {
            // Use server-side search endpoint instead of direct API calls
            const response = await fetch(`/api/geocode?address=${encodeURIComponent(query)}`);
            const data = await response.json();
            this.displaySearchResults(data.features || []);
        } catch (error) {
            this.handleError(error);
        }
    }

    displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        resultsContainer.innerHTML = '';
        results.forEach((result, idx) => {
            const btn = document.createElement('button');
            btn.textContent = result.display_name;
            btn.className = 'block w-full text-left px-2 py-1 hover:bg-gray-200';
            btn.onclick = () => {
                document.getElementById('affected-area-input').value = result.display_name;
                resultsContainer.innerHTML = '';
            };
            resultsContainer.appendChild(btn);
        });
    }

    handleSeverityChange(event) {
        const severity = event.target.value;
        this.mapZones.updateSeverity(severity);
    }

    async handleAddAffectedArea() {
        const input = document.getElementById('affected-area-input');
        const area = input.value.trim();
        if (!area) return;

        if (this.affectedAreas.some(a => a.name === area)) {
            alert('Area already added.');
            return;
        }

        try {
            const results = await this.mapSearch.searchLocation(area + ' Philippines');
            if (!results.length || !results[0].geojson) {
                throw new Error('Area not found or no boundary data available.');
            }

            const result = results[0];
            const uniqueKey = result.osm_id || area + '-' + Date.now();
            const severity = document.querySelector('input[name="severity"]:checked')?.value || 'high';
            
            this.mapZones.addAffectedArea(result.geojson, area, severity, uniqueKey);
            this.affectedAreas.push({ name: area, geojson: result.geojson });
            this.updateAffectedAreasList();
            
            input.value = '';
        } catch (error) {
            this.handleError(error);
        }
    }

    handleError(error) {
        console.error(error);
        // Implement your error handling UI here
    }

    updateAffectedAreasList() {
        const list = document.getElementById('affected-areas-list');
        list.innerHTML = '';
        
        this.affectedAreas.forEach((area, idx) => {
            const tag = document.createElement('span');
            tag.className = 'bg-gray-100 text-gray-800 px-3 py-1 rounded-full flex items-center gap-1 text-sm';
            tag.innerHTML = `
                ${area.name}
                <button type="button" class="ml-1 text-red-500 hover:text-red-700" onclick="app.removeAffectedArea(${idx})">&times;</button>
            `;
            list.appendChild(tag);
        });

        document.getElementById('affected-areas-hidden').value = JSON.stringify(
            this.affectedAreas.map(a => a.geojson)
        );
    }
}

// Initialize the application
const app = new NotificationApp();
app.initialize();


