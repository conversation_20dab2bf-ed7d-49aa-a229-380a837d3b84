@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bell"></i>
                        Send Push Notification
                    </h3>
                </div>
                <div class="card-body">
                    <form id="notificationForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">Notification Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" required maxlength="255">
                                    <small class="form-text text-muted">Maximum 255 characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">Category</label>
                                    <select class="form-control" id="category" name="category">
                                        <option value="General">General</option>
                                        <option value="Emergency">Emergency</option>
                                        <option value="Weather">Weather Alert</option>
                                        <option value="Evacuation">Evacuation Notice</option>
                                        <option value="Safety">Safety Update</option>
                                        <option value="Information">Information</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="severity">Severity Level</label>
                                    <select class="form-control" id="severity" name="severity">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="device_type">Target Devices</label>
                                    <select class="form-control" id="device_type" name="device_type">
                                        <option value="all" selected>All Devices</option>
                                        <option value="android">Android Only</option>
                                        <option value="ios">iOS Only</option>
                                        <option value="web">Web Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="body">Message Content *</label>
                            <textarea class="form-control" id="body" name="body" rows="4" required maxlength="1000"></textarea>
                            <small class="form-text text-muted">Maximum 1000 characters</small>
                        </div>

                        <div class="form-group">
                            <label for="user_id">Send to Specific User (Optional)</label>
                            <input type="number" class="form-control" id="user_id" name="user_id" placeholder="Leave empty to send to all users">
                            <small class="form-text text-muted">Enter user ID to send to specific user only</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-paper-plane"></i>
                                    Send Notification
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-secondary btn-block" id="testBtn">
                                    <i class="fas fa-vial"></i>
                                    Send Test Notification
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Card -->
            <div class="card mt-3" id="resultsCard" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        Notification Results
                    </h3>
                </div>
                <div class="card-body" id="resultsContent">
                    <!-- Results will be displayed here -->
                </div>
            </div>

            <!-- Active Device Tokens -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-mobile-alt"></i>
                        Active Device Tokens
                    </h3>
                    <button class="btn btn-sm btn-info float-right" id="refreshTokens">
                        <i class="fas fa-sync"></i>
                        Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div id="tokensList">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading device tokens...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('notificationForm');
    const testBtn = document.getElementById('testBtn');
    const refreshBtn = document.getElementById('refreshTokens');
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');

    // Load device tokens on page load
    loadDeviceTokens();

    // Form submission - now handled by server-side
    form.addEventListener('submit', function(e) {
        // Allow normal form submission to server
        showLoading();
    });

    // Test notification - convert to server-side
    testBtn.addEventListener('click', function() {
        // Create a form for test notification
        const testForm = document.createElement('form');
        testForm.method = 'POST';
        testForm.action = '/api/test-notification';
        testForm.style.display = 'none';

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        testForm.appendChild(csrfInput);

        // Add form data
        const formData = new FormData(form);
        for (let [key, value] of formData.entries()) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            testForm.appendChild(input);
        }

        document.body.appendChild(testForm);
        testForm.submit();
    });

    // Refresh tokens
    refreshBtn.addEventListener('click', function() {
        window.location.reload();
    });

    // Server-side form submission - no AJAX needed
    function showLoading() {
        // Show loading state for form submission
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
        }
    }

    function sendTestNotification() {
        const title = document.getElementById('title').value || 'Test Notification';
        const body = document.getElementById('body').value || 'This is a test notification';
        const category = document.getElementById('category').value;
        const severity = document.getElementById('severity').value;

        fetch('/api/notifications/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                title: title,
                body: body,
                category: category,
                severity: severity
            })
        })
        .then(response => response.json())
        .then(data => {
            showResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
            showResults({
                success: false,
                message: 'Network error occurred'
            });
        });
    }

    function loadDeviceTokens() {
        fetch('/api/device-tokens')
        .then(response => response.json())
        .then(data => {
            displayTokens(data);
        })
        .catch(error => {
            console.error('Error loading tokens:', error);
            document.getElementById('tokensList').innerHTML = 
                '<div class="alert alert-danger">Error loading device tokens</div>';
        });
    }

    function displayTokens(data) {
        const tokensList = document.getElementById('tokensList');
        
        if (data.success && data.tokens && data.tokens.length > 0) {
            let html = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-info"><i class="fas fa-mobile-alt"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Total Active Tokens</span>
                                <span class="info-box-number">${data.tokens.length}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fab fa-android"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Android Devices</span>
                                <span class="info-box-number">${data.tokens.filter(t => t.device_type === 'android').length}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-box">
                            <span class="info-box-icon bg-warning"><i class="fab fa-apple"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">iOS Devices</span>
                                <span class="info-box-number">${data.tokens.filter(t => t.device_type === 'ios').length}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            tokensList.innerHTML = html;
        } else {
            tokensList.innerHTML = '<div class="alert alert-warning">No active device tokens found</div>';
        }
    }

    function showResults(data) {
        resultsCard.style.display = 'block';
        
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> Success!</h5>
                    <p>${data.message}</p>
                    ${data.tokens_count ? `<p><strong>Tokens targeted:</strong> ${data.tokens_count}</p>` : ''}
                    ${data.success_count ? `<p><strong>Successfully sent:</strong> ${data.success_count}</p>` : ''}
                    ${data.failure_count ? `<p><strong>Failed:</strong> ${data.failure_count}</p>` : ''}
                    ${data.notification_id ? `<p><strong>Notification ID:</strong> ${data.notification_id}</p>` : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> Error!</h5>
                    <p>${data.message}</p>
                    ${data.error ? `<p><strong>Details:</strong> ${data.error}</p>` : ''}
                </div>
            `;
        }

        // Scroll to results
        resultsCard.scrollIntoView({ behavior: 'smooth' });
    }
});
</script>
@endsection
