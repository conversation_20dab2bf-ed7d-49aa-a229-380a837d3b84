@extends('layout.app')

@section('title', 'Edit Evacuation Center')

@section('content')

<!-- Add error message container -->
<div id="mapError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4 z-[1000]" role="alert">
    <strong class="font-bold">Error!</strong>
    <span class="block sm:inline">Failed to load the map. Please check your internet connection and try again.</span>
    <button onclick="retryMapLoad()" class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <title>Retry</title>
            <path d="M14.66 15.66A8 8 0 1 1 17 10h-2a6 6 0 1 0-1.76 4.24l1.42 1.42zM12 10h8l-4 4-4-4z"/>
        </svg>
    </button>
</div>

<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button and Title -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}" 
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl md:text-4xl font-extrabold text-gray-900">Edit Evacuation Center</h1>
                    <p class="text-gray-600 mt-1">Update evacuation center details and location</p>
                </div>
            </div>
        </div>

        @if ($errors->any())
            <div class="mb-6 bg-red-50/80 backdrop-blur-sm border-l-4 border-red-500 p-4 rounded-xl shadow-lg">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <h3 class="text-red-800 font-medium">Please correct the following errors:</h3>
                </div>
                <ul class="list-disc pl-5 space-y-1">
                    @foreach ($errors->all() as $error)
                        <li class="text-red-700">{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @php
            $disasterTypes = old('disaster_type', $evacuationCenter->disaster_type);
            $isOthersChecked = is_array($disasterTypes) && in_array('Others', $disasterTypes);
            
            $otherDisasterText = '';
            if (is_array($disasterTypes)) {
                foreach ($disasterTypes as $type) {
                    if (strpos($type, 'Others:') === 0) {
                        $otherDisasterText = trim(str_replace('Others:', '', $type));
                        $isOthersChecked = true;
                        break;
                    }
                }
            }
        @endphp

        <!-- Main Form -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8">
            <div class="max-w-3xl mx-auto">
                <div class="flex items-center gap-6 mb-8">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white text-xl font-semibold w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Center Information</h2>
                        <p class="text-gray-600 mt-1">Update the details of the evacuation center</p>
                    </div>
                </div>

                <form id="evacuationCenterForm" action="{{ route('components.evacuation_management.update', $evacuationCenter->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="building_name" name="building_name" value="{{ old('building_name', $evacuationCenter->building_name ?? '') }}">
                    
                    <div class="grid grid-cols-1 gap-8">
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="name" class="block text-xl font-semibold text-sky-600 mb-3">Center Name</label>
                            <input type="text" id="name" name="name" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('name', $evacuationCenter->name) }}"
                                   placeholder="Enter center name">
                        </div>
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="capacity" class="block text-xl font-semibold text-sky-600 mb-3">Capacity</label>
                            <input type="number" id="capacity" name="capacity" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('capacity', $evacuationCenter->capacity) }}"
                                   placeholder="Enter capacity">
                        </div>
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="contact" class="block text-xl font-semibold text-sky-600 mb-3">Contact Number</label>
                            <input type="text" id="contact" name="contact" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('contact', $evacuationCenter->contact) }}"
                                   placeholder="Enter contact number">
                        </div>
                        
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100 disaster-type-container">
                            <label class="block text-xl font-semibold text-sky-600 mb-3">Disaster Type</label>
                            <div class="space-y-2">
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="typhoon" name="disaster_type[]" value="Typhoon" class="h-5 w-5 text-green-600 border-gray-300 rounded focus:ring-green-500" {{ is_array($disasterTypes) && in_array('Typhoon', $disasterTypes) ? 'checked' : '' }}>
                                    <label for="typhoon" class="text-lg text-gray-800">Typhoon <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #22c55e;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="flood" name="disaster_type[]" value="Flood" class="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500" {{ is_array($disasterTypes) && in_array('Flood', $disasterTypes) ? 'checked' : '' }}>
                                    <label for="flood" class="text-lg text-gray-800">Flood <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #3b82f6;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="fire" name="disaster_type[]" value="Fire" class="h-5 w-5 text-red-600 border-gray-300 rounded focus:ring-red-500" {{ is_array($disasterTypes) && in_array('Fire', $disasterTypes) ? 'checked' : '' }}>
                                    <label for="fire" class="text-lg text-gray-800">Fire <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #ef4444;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="earthquake" name="disaster_type[]" value="Earthquake" class="h-5 w-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500" {{ is_array($disasterTypes) && in_array('Earthquake', $disasterTypes) ? 'checked' : '' }}>
                                    <label for="earthquake" class="text-lg text-gray-800">Earthquake <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #f59e42;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="landslide" name="disaster_type[]" value="Landslide" class="h-5 w-5 text-yellow-700 border-gray-300 rounded focus:ring-yellow-700" {{ is_array($disasterTypes) && in_array('Landslide', $disasterTypes) ? 'checked' : '' }}>
                                    <label for="landslide" class="text-lg text-gray-800">Landslide <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #a16207;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="others" name="disaster_type[]" value="Others" class="h-5 w-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500" {{ $isOthersChecked ? 'checked' : '' }}>
                                    <label for="others" class="text-lg text-gray-800">Others <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #9333ea;"></span></label>
                                </div>
                                <!-- Custom disaster type input field -->
                                <div id="customDisasterInput" class="mt-3 {{ $isOthersChecked ? '' : 'hidden' }}">
                                    <label for="custom_disaster_type" class="block text-sm font-medium text-gray-700 mb-2">Specify Disaster Type:</label>
                                    <input type="text" id="custom_disaster_type" name="other_disaster_type" 
                                           class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 text-base py-2 px-3 bg-white" 
                                           placeholder="Enter custom disaster type (e.g., Volcanic Eruption, Tsunami)"
                                           value="{{ old('other_disaster_type', $otherDisasterText) }}">
                                </div>
                            </div>
                            <div id="selectedDisasters" class="flex flex-wrap gap-2 mt-2"></div>
                            <p class="text-sm text-gray-500 mt-2">If more than one disaster is selected, this center will use a <span class="inline-block w-4 h-4 rounded-full align-middle" style="background-color: #6b7280;"></span> gray marker on the map.</p>
                            <p class="text-sm text-gray-500 mt-2">If "Others" is selected alone, this center will use a <span class="inline-block w-4 h-4 rounded-full align-middle" style="background-color: #9333ea;"></span> purple marker on the map.</p>
                        </div>
                    </div>

                    <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                        <label for="status" class="block text-xl font-semibold text-sky-600 mb-3">Status</label>
                        <select id="status" name="status"
                                class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" required>
                            <option value="Active"
                                {{ old('status', $evacuationCenter->status) == 'Active' ? 'selected' : '' }}>
                                Active
                            </option>
                            <option value="Inactive"
                                {{ old('status', $evacuationCenter->status) == 'Inactive' ? 'selected' : '' }}>
                                Inactive
                            </option>
                            <option value="Under Maintenance"
                                {{ old('status', $evacuationCenter->status) == 'Under Maintenance' ? 'selected' : '' }}>
                                Under Maintenance
                            </option>
                        </select>
                    </div>

                    <!-- Location Section -->
                    <div class="space-y-8 mt-8">
                        <!-- Search Box -->
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="search" class="block text-xl font-semibold text-sky-600 mb-3">Search Location</label>
                            <div class="flex gap-3">
                                <div class="relative flex-1">
                                    <input type="text" id="search"
                                           class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 pl-12 pr-4 bg-white"
                                           placeholder="Type any location and press Enter or click Search...">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                        </svg>
                                    </div>
                                    <div id="searchResults" class="absolute z-[1000] w-full bg-white border border-gray-300 rounded-lg mt-2 shadow-lg max-h-60 overflow-y-auto hidden"></div>
                                </div>
                                <button type="button" id="searchButton" class="px-6 py-4 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors font-semibold text-lg">
                                    <i class="fas fa-search mr-2"></i>Search
                                </button>
                            </div>
                        </div>

                        <!-- Map Container -->
                        <div class="bg-sky-50 p-6 border border-sky-100 relative">
                            <label class="block text-xl font-semibold text-sky-600 mb-3">Map View</label>
                            <div class="overflow-hidden border-2 border-gray-300 relative">
                                <div id="map" class="h-[400px] w-full relative z-[10]">
                                    <div id="mapLoadingIndicator" class="absolute inset-0 bg-white flex items-center justify-center z-[15]">
                                        <div class="text-center">
                                            <div class="animate-spin h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
                                            <p class="text-gray-600 mt-2 text-lg">Loading map...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 space-y-2 text-gray-600">
                                <p class="flex items-center gap-2">
                                    <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Type any location and press Enter/click Search, or click on the map to set position
                                </p>
                                <p class="flex items-center gap-2">
                                    <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                                    </svg>
                                    Drag the marker to adjust the location if needed
                                </p>
                                <p class="flex items-center gap-2">
                                    <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Confirm the location is correct before saving
                                </p>
                            </div>
                        </div>

                        <!-- Location Details Card -->
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label class="block text-xl font-semibold text-gray-900 mb-3">Selected Location Details</label>
                            <div class="space-y-4">
                                <p id="selectedAddress" class="text-lg text-gray-700 bg-white p-4 rounded-lg border-2 border-gray-300">
                                    @if(old('street_name', $evacuationCenter->street_name) || old('barangay', $evacuationCenter->barangay) || old('city', $evacuationCenter->city) || old('province', $evacuationCenter->province))
                                        {{ old('street_name', $evacuationCenter->street_name) }}, {{ old('barangay', $evacuationCenter->barangay) }}, {{ old('city', $evacuationCenter->city) }}, {{ old('province', $evacuationCenter->province) }}
                                    @else
                                        No location selected yet
                                    @endif
                                </p>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="bg-white p-4 rounded-lg border-2 border-gray-300">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Latitude</label>
                                        <p id="selectedLatitude" class="text-lg text-gray-700">
                                            @if(old('latitude', $evacuationCenter->latitude))
                                                {{ old('latitude', $evacuationCenter->latitude) }}
                                            @else
                                                --
                                            @endif
                                        </p>
                                    </div>
                                    <div class="bg-white p-4 rounded-lg border-2 border-gray-300">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Longitude</label>
                                        <p id="selectedLongitude" class="text-lg text-gray-700">
                                            @if(old('longitude', $evacuationCenter->longitude))
                                                {{ old('longitude', $evacuationCenter->longitude) }}
                                            @else
                                                --
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden form fields for location data -->
                        <input type="hidden" id="latitude" name="latitude" required value="{{ old('latitude', $evacuationCenter->latitude) }}">
                        <input type="hidden" id="longitude" name="longitude" required value="{{ old('longitude', $evacuationCenter->longitude) }}">
                        <input type="hidden" id="street_name" name="street_name" required value="{{ old('street_name', $evacuationCenter->street_name) }}">
                        <input type="hidden" id="barangay" name="barangay" required value="{{ old('barangay', $evacuationCenter->barangay) }}">
                        <input type="hidden" id="city" name="city" required value="{{ old('city', $evacuationCenter->city) }}">
                        <input type="hidden" id="province" name="province" required value="{{ old('province', $evacuationCenter->province) }}">
                        <input type="hidden" id="postal_code" name="postal_code" required value="{{ old('postal_code', $evacuationCenter->postal_code) }}">
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end gap-4 pt-6">
                        <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                           class="px-6 py-3 border-2 border-sky-200 text-sky-700 font-medium text-base rounded-xl hover:bg-sky-50 transition-all duration-200">
                            Cancel
                        </a>
                        <button type="submit" id="saveButton"
                                class="px-6 py-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-medium text-base rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Update Center
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<style>
    /* Ensure map container has proper dimensions */
    #map {
        height: 400px !important;
        width: 100% !important;
        position: relative !important;
        z-index: 10 !important;
    }

    /* Fix any potential CSS conflicts */
    .leaflet-container {
        height: 400px !important;
        width: 100% !important;
    }

    /* Ensure map tiles load properly */
    .leaflet-tile {
        max-width: none !important;
    }
</style>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script>
    // Pass data from PHP to our external JavaScript file
    window.EvacuationCenterData = {
        isAdmin: {{ auth()->user()->hasRole('admin') || auth()->user()->hasRole('super_admin') ? 'true' : 'false' }},
        userBarangay: '{{ auth()->user()->barangay }}',
        hasOldLocation: true,
        oldLatitude: '{{ old('latitude', $evacuationCenter->latitude) }}',
        oldLongitude: '{{ old('longitude', $evacuationCenter->longitude) }}'
    };
</script>
<script src="{{ asset('js/evac_management/evacuationCenter.js') }}" defer></script>

@endsection