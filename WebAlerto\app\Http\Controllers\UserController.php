<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\MobileUser;
use App\Models\Barangay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedBarangay = $request->input('barangay');
        $searchQuery = $request->input('search');

        // Get list of all barangays for super_admin filter
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangays = Barangay::pluck('name')->unique()->toArray();
        }

        // Base query for mobile users
        $query = MobileUser::query();

        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        } else if ($selectedBarangay) {
            // If super_admin and barangay is selected, filter by that barangay
            $query->where('barangay', $selectedBarangay);
        }

        // Apply search filter
        if ($searchQuery) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('full_name', 'like', "%{$searchQuery}%")
                  ->orWhere('mobile_number', 'like', "%{$searchQuery}%")
                  ->orWhere('address', 'like', "%{$searchQuery}%");
            });
        }

        $mobileUsers = $query->paginate(10);
        
        return view('components.user_management.user-management', compact('mobileUsers', 'barangays', 'selectedBarangay', 'searchQuery'));
    }

    
public function deactivate($id)
{
    $user = Auth::user();

    // Super_admin can deactivate any mobile user
    if ($user->hasRole('super_admin')) {
        $targetUser = MobileUser::findOrFail($id);
    } else {
        // Chairman can only deactivate mobile users in their barangay
        $targetUser = MobileUser::where('barangay', $user->barangay)->findOrFail($id);
    }

    // Check if user has authority to deactivate
    if (!$user->canManageUser($targetUser)) {
        return redirect()->back()->with('error', 'You are not authorized to deactivate users.');
    }

    $targetUser->status = 'Inactive';
    $targetUser->save();

    // Redirect back to user management page
    return redirect()->route('components.user-management')->with('success', 'Mobile user deactivated successfully.');
}

    public function search(Request $request)
    {
        $user = Auth::user();
        $query = strtoupper($request->input('query'));

        // Query mobile users whose name fields contain the query and belongs to the same barangay
        $mobileUsers = MobileUser::where('barangay', $user->barangay)
                    ->where(function($q) use ($query) {
                        $q->where('full_name', 'like', '%' . $query . '%')
                          ->orWhere('mobile_number', 'like', '%' . $query . '%');
                    })
                    ->get();

        return response()->json(['mobileUsers' => $mobileUsers]);
    }

    
public function destroy($id)
{
    if (!auth()->check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    // Super_admin can delete any mobile user
    if ($user->hasRole('super_admin')) {
        $targetUser = MobileUser::findOrFail($id);
    } else {
        // Chairman can only delete mobile users in their barangay
        $targetUser = MobileUser::where('barangay', $user->barangay)->findOrFail($id);
    }

    // Check if user has authority to delete
    if (!$user->canManageUser($targetUser)) {
        return redirect()->route('components.user-management')
            ->with('error', 'You are not authorized to delete users.');
    }

    $targetUser->delete();
    return redirect()->route('components.user-management')
        ->with('success', 'Mobile user deleted successfully!');
}
    public function edit($id)
{
    $userToEdit = MobileUser::findOrFail($id); // Mobile user na i-edit
    $currentUser = auth()->user(); // Currently logged-in user

    

    // Chairman can only edit mobile users within their barangay
    if ($currentUser->hasRole('chairman') && $currentUser->barangay != $userToEdit->barangay) {
        abort(403, 'Unauthorized access.');
    }

    // For the form barangay list, you can limit or keep as is
    $barangays = MobileUser::distinct()->pluck('barangay');

    
return view('components.user_management.edit-user', ['mobileUser' => $userToEdit, 'barangays' => $barangays]);
}


public function update(Request $request, $id)
{
    $mobileUser = MobileUser::findOrFail($id);
    $currentUser = auth()->user();

    // Chairman can only edit mobile users within their barangay
    if ($currentUser->hasRole('chairman') && $currentUser->barangay != $mobileUser->barangay) {
        abort(403, 'Unauthorized access.');
    }

    // Validation rules for mobile users
    $rules = [
        'full_name' => 'required|string|max:255',
        'mobile_number' => 'required|string|max:20',
        'age' => 'required|integer|min:1|max:120',
        'gender' => 'required|in:Male,Female,Other',
        'address' => 'required|string|max:500',
    ];

    // Only super_admin or chairman (same barangay) can update barangay
    if (
        $currentUser->hasRole('super_admin') ||
        ($currentUser->hasRole('chairman') && $currentUser->barangay == $mobileUser->barangay)
    ) {
        $rules['barangay'] = 'required|string|max:255';
    }

    $request->validate($rules);


    // Update mobile user data
    $mobileUser->update($request->only([
        'full_name', 'mobile_number', 'age', 'gender', 'address'
    ]));

    // Update barangay if authorized

    if (
        $currentUser->hasRole('super_admin') ||
        ($currentUser->hasRole('chairman') && $currentUser->barangay == $mobileUser->barangay)
    ) {

        $mobileUser->update([

            'barangay' => $request->barangay
        ]);
    }


    return redirect()->route('components.user-management')
        ->with('success', 'Mobile user updated successfully!');
}

    public function view($id)
    {
        $mobileUser = MobileUser::findOrFail($id);
        $currentUser = auth()->user();

        // Chairman can only view mobile users within their barangay
        if ($currentUser->hasRole('chairman') && $currentUser->barangay != $mobileUser->barangay) {
            abort(403, 'Unauthorized access.');
        }

        return view('components.user_management.view-user', compact('mobileUser'));

    }
}