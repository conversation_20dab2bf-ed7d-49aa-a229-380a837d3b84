import{a as A}from"./chunk-WPPT3EJF.js";import{i as M,k as P,ra as S}from"./chunk-SFXIJNIZ.js";import{a as I,g as m}from"./chunk-2R6CW7ES.js";var k=(()=>{class f{constructor(t){this.http=t,this.ORS_API_KEY=A.ors<PERSON>pi<PERSON>ey,this.ORS_BASE_URL="https://api.openrouteservice.org/v2/directions",this.currentRoute=null,this.isRealTimeActive=!1,this.realTimeInterval=null,this.lastUserPosition=null,this.routeUpdateCallback=null}getDirections(u,l,d,R){return m(this,arguments,function*(t,e,o,i,s="foot-walking",n={}){console.log(`\u{1F5FA}\uFE0F Attempting route calculation from [${e}, ${t}] to [${i}, ${o}] using ${s}`);try{let y=I({coordinates:[[t,e],[o,i]],format:"geojson"},n),v=`${this.ORS_BASE_URL}/${s}/geojson`;console.log(`\u{1F310} Making request to: ${v}`),console.log(`\u{1F511} Using API key: ${this.ORS_API_KEY.substring(0,10)}...`);let p=yield this.http.post(v,y,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json",Accept:"application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8"}}).toPromise();if(console.log("\u{1F4E1} OpenRouteService response:",p),p&&p.features&&p.features.length>0){let a=p.features[0],r=0,c=0;a.properties&&(a.properties.summary?(r=a.properties.summary.distance||0,c=a.properties.summary.duration||0):a.properties.segments&&a.properties.segments.length>0&&(r=a.properties.segments[0].distance||0,c=a.properties.segments[0].duration||0)),(!r||!c)&&(console.log("\u26A0\uFE0F Missing distance/duration in response, calculating fallback"),r=this.calculateDistance(e,t,i,o),c=this.estimateDuration(r,s));let h={routes:[{geometry:a.geometry,distance:r,duration:c,legs:[{distance:r,duration:c}]}],code:"Ok"};return console.log("\u2705 Successfully parsed route from OpenRouteService"),console.log(`\u{1F4CF} Distance: ${(r/1e3).toFixed(2)} km, Duration: ${Math.round(c/60)} min`),h}throw new Error("No routes found in response")}catch(g){console.error("\u274C OpenRouteService error:",g),g.status&&console.error(`HTTP Status: ${g.status}`),g.error&&console.error("Error details:",g.error);try{console.log("\u{1F504} Retrying with simplified parameters...");let v={coordinates:[[t,e],[o,i]]},p=`${this.ORS_BASE_URL}/${s}`,a=yield this.http.post(p,v,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json"}}).toPromise();if(console.log("\u{1F4E1} Retry response:",a),a&&a.features&&a.features.length>0){let r=a.features[0],c=0,h=0;r.properties&&(r.properties.summary?(c=r.properties.summary.distance||0,h=r.properties.summary.duration||0):r.properties.segments&&r.properties.segments.length>0&&(c=r.properties.segments[0].distance||0,h=r.properties.segments[0].duration||0)),(!c||!h)&&(c=this.calculateDistance(e,t,i,o),h=this.estimateDuration(c,s));let T={routes:[{geometry:r.geometry,distance:c,duration:h,legs:[{distance:c,duration:h}]}],code:"Ok"};return console.log("\u2705 Retry successful!"),T}}catch(y){console.error("\u274C Retry also failed:",y)}return console.log("\u{1F504} All attempts failed, falling back to straight-line route"),this.createStraightLineRoute(t,e,o,i,s)}})}convertTravelModeToProfile(t){switch(t.toLowerCase()){case"walking":case"foot":return"foot-walking";case"cycling":case"bicycle":case"bike":return"cycling-regular";case"driving":case"car":return"driving-car";default:return"foot-walking"}}convertToGeoJSON(t){return{type:"Feature",geometry:t.geometry,properties:{distance:t.distance,duration:t.duration}}}getRouteSummary(t){let e=(t.distance/1e3).toFixed(2),o=Math.round(t.duration/60);return{distance:`${e} km`,duration:`${o} min`,distanceText:`${e} km`,durationText:`${o} min`}}estimateDuration(t,e){let o={"foot-walking":1.4,"cycling-regular":4.2,"driving-car":13.9},i=o[e]||o["foot-walking"];return t/i}createStraightLineRoute(t,e,o,i,s="foot-walking"){let n=[[t,e],[o,i]],u=this.calculateDistance(e,t,i,o),l=this.estimateDuration(u,s);return console.log(`\u{1F4CF} Fallback route: ${(u/1e3).toFixed(2)} km, ${Math.round(l/60)} min`),{routes:[{geometry:{coordinates:n,type:"LineString"},distance:u,duration:l,legs:[{distance:u,duration:l}]}],code:"Ok"}}calculateDistance(t,e,o,i){let n=t*Math.PI/180,u=o*Math.PI/180,l=(o-t)*Math.PI/180,d=(i-e)*Math.PI/180,R=Math.sin(l/2)*Math.sin(l/2)+Math.cos(n)*Math.cos(u)*Math.sin(d/2)*Math.sin(d/2);return 6371e3*(2*Math.atan2(Math.sqrt(R),Math.sqrt(1-R)))}startRealTimeRouting(t,e="foot-walking",o,i=3e4){console.log("\u{1F504} Starting real-time routing to:",t),this.isRealTimeActive=!0,this.routeUpdateCallback=o,this.realTimeInterval&&clearInterval(this.realTimeInterval),this.realTimeInterval=setInterval(()=>m(this,null,function*(){if(this.lastUserPosition&&this.isRealTimeActive)try{let s=yield this.getDirections(this.lastUserPosition.lng,this.lastUserPosition.lat,t.lng,t.lat,e,{geometries:"geojson",overview:"full",steps:!0,continue_straight:!1,alternative_routes:{target_count:2,weight_factor:1.4}});s.routes&&s.routes.length>0&&(this.currentRoute=s.routes[0],this.routeUpdateCallback?.(this.currentRoute),console.log("\u{1F504} Real-time route updated"))}catch(s){console.error("\u274C Real-time route update failed:",s)}}),i)}updateUserPosition(t,e){this.lastUserPosition={lat:t,lng:e},this.isRealTimeActive&&this.shouldUpdateRoute(t,e)&&(console.log("\u{1F4CD} Significant position change detected, updating route..."),this.triggerImmediateRouteUpdate())}stopRealTimeRouting(){console.log("\u23F9\uFE0F Stopping real-time routing"),this.isRealTimeActive=!1,this.currentRoute=null,this.routeUpdateCallback=null,this.lastUserPosition=null,this.realTimeInterval&&(clearInterval(this.realTimeInterval),this.realTimeInterval=null)}shouldUpdateRoute(t,e){return this.lastUserPosition?this.calculateDistance(this.lastUserPosition.lat,this.lastUserPosition.lng,t,e)>50:!0}triggerImmediateRouteUpdate(){return m(this,null,function*(){console.log("\u26A1 Immediate route update triggered")})}getCurrentRoute(){return this.currentRoute}isRealTimeRoutingActive(){return this.isRealTimeActive}getRouteWithTraffic(t,e,o,i,s="driving-car"){return m(this,null,function*(){let n=s==="driving-car"?{geometries:"geojson",overview:"full",steps:!0,continue_straight:!1,avoid_features:["tollways"],alternative_routes:{target_count:2,weight_factor:1.4}}:{geometries:"geojson",overview:"full",steps:!0};return this.getDirections(t,e,o,i,s,n)})}testConnection(){return m(this,null,function*(){try{console.log("\u{1F9EA} Testing OpenRouteService API connection...");let e={coordinates:[[123.8854,10.3157],[123.8954,10.3257]],format:"geojson"},o=`${this.ORS_BASE_URL}/foot-walking/geojson`,i=yield this.http.post(o,e,{headers:{Authorization:this.ORS_API_KEY,"Content-Type":"application/json",Accept:"application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8"}}).toPromise();return i&&i.features&&i.features.length>0?{success:!0,message:"OpenRouteService API connection successful!",details:{features:i.features.length,apiKey:this.ORS_API_KEY.substring(0,10)+"..."}}:{success:!1,message:"OpenRouteService API returned empty response",details:i}}catch(t){return console.error("\u274C OpenRouteService connection test failed:",t),{success:!1,message:`OpenRouteService API connection failed: ${t.message||t}`,details:{status:t.status,error:t.error,apiKey:this.ORS_API_KEY.substring(0,10)+"..."}}}})}getNavigationInstructions(t){let e=[];return t.legs&&t.legs.length>0&&t.legs.forEach((o,i)=>{o.steps&&o.steps.forEach((s,n)=>{e.push({id:`${i}-${n}`,instruction:s.name||"Continue",distance:s.distance,duration:s.duration,coordinates:s.geometry.coordinates,maneuver:this.getManeuverType(s.name||""),bearing:this.calculateBearing(s.geometry.coordinates)})})}),e}getCurrentInstruction(t,e,o){if(!o.length)return null;let i=o[0],s=1/0;for(let n of o)if(n.coordinates&&n.coordinates.length>0){let[u,l]=n.coordinates[0],d=this.calculateDistance(t,e,l,u);d<s&&(s=d,i=n)}return s<100?i:null}calculateBearing(t){if(t.length<2)return 0;let[e,o]=t[0],[i,s]=t[1],n=(i-e)*Math.PI/180,u=o*Math.PI/180,l=s*Math.PI/180,d=Math.sin(n)*Math.cos(l),R=Math.cos(u)*Math.sin(l)-Math.sin(u)*Math.cos(l)*Math.cos(n);return(Math.atan2(d,R)*180/Math.PI+360)%360}getManeuverType(t){let e=t.toLowerCase();return e.includes("left")?"turn-left":e.includes("right")?"turn-right":e.includes("straight")||e.includes("continue")?"straight":e.includes("u-turn")?"u-turn":e.includes("roundabout")?"roundabout":e.includes("exit")?"exit":e.includes("merge")?"merge":"straight"}getETA(t){let e=new Date,o=t.duration*1e3;return new Date(e.getTime()+o)}formatDuration(t){let e=Math.floor(t/3600),o=Math.floor(t%3600/60);return e>0?`${e}h ${o}m`:`${o}m`}formatDistance(t){return t>=1e3?`${(t/1e3).toFixed(1)} km`:`${Math.round(t)} m`}static{this.\u0275fac=function(e){return new(e||f)(P(S))}}static{this.\u0275prov=M({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();export{k as a};
