<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Barangay;
use Illuminate\Support\Facades\DB;

class BarangaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Disable foreign key checks to avoid issues with truncation
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        // Truncate the table to start fresh
        Barangay::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $barangays = [
            ['name' => 'Adlaon', 'code' => 'ADL'],
            ['name' => 'Agsungot', 'code' => 'AGS'],
            ['name' => 'Apas', 'code' => 'APA'],
            ['name' => 'Bacayan', 'code' => 'BAC'],
            ['name' => 'Banilad', 'code' => 'BAN'],
            ['name' => 'Binaliw', 'code' => 'BIN'],
            ['name' => 'Bonbon', 'code' => 'BON'],
            ['name' => 'Budla-an', 'code' => 'BUD'],
            ['name' => 'Busay', 'code' => 'BUS'],
            ['name' => 'Cambinocot', 'code' => 'CAM'],
            ['name' => 'Capitol Site', 'code' => 'CAP'],
            ['name' => 'Carreta', 'code' => 'CAR'],
            ['name' => 'Cogon Ramos', 'code' => 'COG'],
            ['name' => 'Day-as', 'code' => 'DAY'],
            ['name' => 'Ermita', 'code' => 'ERM'],
            ['name' => 'Guba', 'code' => 'GUB'],
            ['name' => 'Hipodromo', 'code' => 'HIP'],
            ['name' => 'Kalubihan', 'code' => 'KAL'],
            ['name' => 'Kamagayan', 'code' => 'KAM'],
            ['name' => 'Kamputhaw', 'code' => 'KMP'],
            ['name' => 'Kasambagan', 'code' => 'KAS'],
            ['name' => 'Lahug', 'code' => 'LAH'],
            ['name' => 'Lorega San Miguel', 'code' => 'LOR'],
            ['name' => 'Lusaran', 'code' => 'LUS'],
            ['name' => 'Luz', 'code' => 'LUZ'],
            ['name' => 'Mabini', 'code' => 'MAB'],
            ['name' => 'Mabolo', 'code' => 'MBO'],
            ['name' => 'Malubog', 'code' => 'MAL'],
            ['name' => 'Pahina Central', 'code' => 'PAH'],
            ['name' => 'Pari-an', 'code' => 'PAR'],
            ['name' => 'Paril', 'code' => 'PRL'],
            ['name' => 'Pit-os', 'code' => 'PIT'],
            ['name' => 'Pulangbato', 'code' => 'PUL'],
            ['name' => 'Pung-ol Sibugay', 'code' => 'PUN'],
            ['name' => 'Punta Princesa', 'code' => 'PRI'],
            ['name' => 'San Antonio', 'code' => 'SAN'],
            ['name' => 'San Jose', 'code' => 'SJO'],
            ['name' => 'San Roque', 'code' => 'SRO'],
            ['name' => 'Santa Cruz', 'code' => 'SCR'],
            ['name' => 'Santo Niño', 'code' => 'SNI'],
            ['name' => 'Sapangdaku', 'code' => 'SAP'],
            ['name' => 'Sawang Calero', 'code' => 'SAW'],
            ['name' => 'Sinsin', 'code' => 'SIN'],
            ['name' => 'Sirao', 'code' => 'SIR'],
            ['name' => 'Sudlon I', 'code' => 'SU1'],
            ['name' => 'Sudlon II', 'code' => 'SU2'],
            ['name' => 'T. Padilla', 'code' => 'TPD'],
            ['name' => 'Talamban', 'code' => 'TAL'],
            ['name' => 'Taptap', 'code' => 'TAP'],
            ['name' => 'Tejero', 'code' => 'TEJ'],
            ['name' => 'Tinago', 'code' => 'TIN'],
            ['name' => 'Zapatera', 'code' => 'ZAP'],
        ];

        // Insert the data into the database
        foreach ($barangays as $barangay) {
            Barangay::create(array_merge($barangay, [
                'status' => true,
                'description' => "Barangay {$barangay['name']} - Cebu City",
                'address' => "{$barangay['name']}, Cebu City, Cebu, Philippines",
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
} 