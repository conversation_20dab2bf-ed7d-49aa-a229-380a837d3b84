import{a as I}from"./chunk-WPPT3EJF.js";import{Ab as Y,B as c,Bb as K,C as g,Cb as $,Ea as q,F as f,Fb as C,G as m,J as D,Jb as _,K as e,Kb as O,L as t,M as o,Mb as H,Nb as N,Ob as G,Q as d,R as k,Vb as J,Y as n,Z as T,Zb as P,_ as F,_b as y,cc as V,da as x,fc as v,gc as Z,hc as S,jb as R,m as h,na as U,oa as z,qa as b,sa as L,xb as M,yb as E,za as B,zb as W}from"./chunk-N4Y2QYSK.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{g as p}from"./chunk-2R6CW7ES.js";var Q=".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:.9375rem;margin-bottom:15px}";function X(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",21),t(),e(3,"ul",22)(4,"li"),n(5,"Drop, Cover, and Hold On during shaking"),t(),e(6,"li"),n(7,"Stay away from windows, mirrors, and heavy objects"),t(),e(8,"li"),n(9,"If outdoors, move away from buildings and power lines"),t(),e(10,"li"),n(11,"Keep emergency supplies: water, food, flashlight, radio"),t(),e(12,"li"),n(13,"Secure heavy furniture and appliances to walls"),t(),e(14,"li"),n(15,"Know your evacuation routes and meeting points"),t(),e(16,"li"),n(17,"Practice earthquake drills regularly"),t()()())}function ee(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",23),t(),e(3,"ul",22)(4,"li"),n(5,"Move to higher ground immediately"),t(),e(6,"li"),n(7,"Never walk or drive through flood water"),t(),e(8,"li"),n(9,"Turn off utilities (gas, electricity, water) if instructed"),t(),e(10,"li"),n(11,"Keep important documents in waterproof containers"),t(),e(12,"li"),n(13,"Have a battery-powered radio for emergency updates"),t(),e(14,"li"),n(15,"Stock non-perishable food and clean water"),t(),e(16,"li"),n(17,"Know your area's flood risk and evacuation routes"),t()()())}function te(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",24),t(),e(3,"ul",22)(4,"li"),n(5,"Monitor weather updates and warnings"),t(),e(6,"li"),n(7,"Secure or bring in outdoor furniture and objects"),t(),e(8,"li"),n(9,"Stock up on food, water, and medications"),t(),e(10,"li"),n(11,"Charge all electronic devices and have backup power"),t(),e(12,"li"),n(13,"Stay indoors and away from windows"),t(),e(14,"li"),n(15,"Prepare for power outages and flooding"),t(),e(16,"li"),n(17,"Have evacuation plan ready if in high-risk areas"),t()()())}function ne(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",25),t(),e(3,"ul",22)(4,"li"),n(5,"Install smoke detectors and check batteries regularly"),t(),e(6,"li"),n(7,"Create and practice a fire escape plan"),t(),e(8,"li"),n(9,"Keep fire extinguishers in key locations"),t(),e(10,"li"),n(11,"Stay low to avoid smoke when escaping"),t(),e(12,"li"),n(13,"Never use elevators during a fire"),t(),e(14,"li"),n(15,"Feel doors before opening - if hot, find another way"),t(),e(16,"li"),n(17,"Have a designated meeting point outside"),t()()())}function ie(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",26),t(),e(3,"ul",22)(4,"li"),n(5,"Watch for warning signs: tilting trees, cracks in ground"),t(),e(6,"li"),n(7,"Listen for unusual sounds like trees cracking or boulders knocking"),t(),e(8,"li"),n(9,"Move away from the path of a landslide quickly"),t(),e(10,"li"),n(11,"Avoid river valleys and low-lying areas"),t(),e(12,"li"),n(13,"Stay alert during heavy rainfall"),t(),e(14,"li"),n(15,"Have evacuation routes planned from high-risk areas"),t(),e(16,"li"),n(17,"Report landslide hazards to local authorities"),t()()())}function oe(a,u){a&1&&(e(0,"ion-card-content",19)(1,"div",20),o(2,"img",27),t(),e(3,"ul",22)(4,"li"),n(5,"Keep emergency contact numbers readily available"),t(),e(6,"li"),n(7,"Maintain a first aid kit and know basic first aid"),t(),e(8,"li"),n(9,"Store emergency supplies: water (1 gallon per person per day)"),t(),e(10,"li"),n(11,"Have non-perishable food for at least 3 days"),t(),e(12,"li"),n(13,"Keep flashlights, batteries, and portable radio"),t(),e(14,"li"),n(15,"Have copies of important documents in waterproof container"),t(),e(16,"li"),n(17,"Know your local emergency services and evacuation procedures"),t()()())}function ae(a,u){if(a&1&&(e(0,"div",25)(1,"span",26),n(2),t(),e(3,"span",27),n(4),t()()),a&2){let i=k().$implicit,r=k(2);c(),D("category-"+i.type),c(),F(" ",r.getCategoryLabel(i.type)," "),c(),D(i.read?"read":"unread"),c(),F(" ",i.read?"Read":"Unread"," ")}}function re(a,u){if(a&1&&(e(0,"div",19)(1,"div",20)(2,"h5",21),n(3),t(),e(4,"span",22),n(5),t()(),e(6,"p",23),n(7),t(),f(8,ae,5,6,"div",24),t()),a&2){let i=u.$implicit,r=k(2);c(3),T(i.title),c(2),T(r.formatTime(i.created_at)),c(2),T(i.message),c(),m("ngIf",i.data)}}function le(a,u){if(a&1&&(e(0,"div",5)(1,"h4",10),n(2,"Recent Notifications"),t(),e(3,"div",17),f(4,re,9,4,"div",18),t()()),a&2){let i=k();c(4),m("ngForOf",i.notifications)}}function se(a,u){a&1&&(e(0,"div",5)(1,"div",28),o(2,"ion-icon",29),e(3,"h4"),n(4,"No Notifications Yet"),t(),e(5,"p"),n(6,"You haven't received any push notifications yet. When emergency alerts or evacuation center updates are sent, they will appear here."),t()()())}function ce(a,u){a&1&&(e(0,"div",5)(1,"div",30),o(2,"ion-spinner"),e(3,"p"),n(4,"Loading notification history..."),t()()())}var Oe=(()=>{class a{constructor(i,r,l,s,w){this.modalCtrl=i,this.alertCtrl=r,this.toastCtrl=l,this.http=s,this.router=w,this.userData={},this.loadUserData()}loadUserData(){let i=localStorage.getItem("userData");i&&(this.userData=JSON.parse(i))}logout(){return p(this,null,function*(){yield(yield this.alertCtrl.create({header:"Confirm Logout",message:"Are you sure you want to log out?",buttons:[{text:"Cancel",role:"cancel"},{text:"Log Out",handler:()=>{this.performLogout()}}]})).present()})}performLogout(){return p(this,null,function*(){try{yield(yield this.toastCtrl.create({message:"Logging out...",duration:1e3})).present(),localStorage.removeItem("token"),localStorage.removeItem("userData"),localStorage.removeItem("user"),localStorage.removeItem("fcm_token"),localStorage.removeItem("offline_credentials");let r=localStorage.getItem("token");if(r)try{yield this.http.post(`${I.apiUrl}/auth/logout`,{},{headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}}).toPromise()}catch(s){console.log("Server logout failed, but continuing with local logout:",s)}this.router.navigate(["/intro"],{replaceUrl:!0}),yield(yield this.toastCtrl.create({message:"Successfully logged out",duration:2e3,color:"success"})).present()}catch(i){console.error("Logout error:",i),yield(yield this.alertCtrl.create({header:"Logout Error",message:"There was an error logging out. Please try again.",buttons:["OK"]})).present()}})}openTermsModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:de,cssClass:"terms-modal"})).present()})}openPrivacyModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:me,cssClass:"terms-modal"})).present()})}openEmergencyContactsModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:ge,cssClass:"terms-modal"})).present()})}openSafetyTipsModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:ue,cssClass:"terms-modal"})).present()})}openGuideModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:pe,cssClass:"terms-modal"})).present()})}openUserGuideModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:fe,cssClass:"user-guide-modal"})).present()})}openNotificationHistoryModal(){return p(this,null,function*(){yield(yield this.modalCtrl.create({component:he,cssClass:"notification-history-modal"})).present()})}testFCM(){return p(this,null,function*(){if(localStorage.getItem("google_play_services_missing")==="true"){yield(yield this.alertCtrl.create({header:"Google Play Services Required",message:"Push notifications require Google Play Services. Would you like to install or update Google Play Services?",buttons:[{text:"Install/Update",handler:()=>{window.open("market://details?id=com.google.android.gms","_system")}},{text:"Continue Anyway",handler:()=>{this.checkFCMToken()}}]})).present();return}yield this.checkFCMToken()})}checkFCMToken(){return p(this,null,function*(){let i=localStorage.getItem("fcm_token");if(!i){yield(yield this.alertCtrl.create({header:"No FCM Token",message:"No FCM token found. Please restart the app to generate a token.",buttons:["OK"]})).present();return}yield(yield this.alertCtrl.create({header:"FCM Token",message:`Current token: ${i.substring(0,20)}...`,buttons:[{text:"Test Local Notification",handler:()=>{this.showTestNotification()}},{text:"Send from Backend",handler:()=>{this.sendTestNotificationFromBackend(i)}},{text:"Check Google Play",handler:()=>{this.checkGooglePlayServices()}},{text:"Cancel",role:"cancel"}]})).present()})}checkGooglePlayServices(){return p(this,null,function*(){try{window.open("market://details?id=com.google.android.gms","_system")}catch(i){console.error("Error opening Google Play Store:",i),yield(yield this.alertCtrl.create({header:"Error",message:"Could not open Google Play Store. Please check if Google Play Store is installed on your device.",buttons:["OK"]})).present()}})}showTestNotification(){return p(this,null,function*(){let i={title:"Test Notification",body:"This is a local test notification",category:"General",severity:"medium",wasTapped:!1,time:new Date().toISOString()};"vibrate"in navigator&&navigator.vibrate([500,100,500]),yield(yield this.alertCtrl.create({header:i.title,subHeader:i.category?`${i.category.toUpperCase()}`:"",message:i.body,buttons:["OK"]})).present()})}sendTestNotificationFromBackend(i){return p(this,null,function*(){yield(yield this.toastCtrl.create({message:"Sending test notification from backend...",duration:2e3})).present(),this.http.post(`${I.apiUrl}/test-notification`,{token:i,title:"Test from App",message:"This is a test notification sent from the app",category:"General",severity:"medium"}).subscribe({next:()=>{this.toastCtrl.create({message:"Test notification sent successfully!",duration:3e3,color:"success"}).then(l=>l.present())},error:l=>{this.alertCtrl.create({header:"Error",message:`Failed to send test notification: ${l.message||JSON.stringify(l)}`,buttons:["OK"]}).then(s=>s.present())}})})}static{this.\u0275fac=function(r){return new(r||a)(g(v),g(V),g(Z),g(L),g(B))}}static{this.\u0275cmp=h({type:a,selectors:[["app-profile"]],standalone:!0,features:[x],decls:40,vars:1,consts:[[3,"translucent"],[1,"profile-header"],[1,"profile-background"],[2,"display","flex","align-items","center","justify-content","center","height","120px","width","100%"],["src","assets/ALERTO.png",2,"width","200px","height","200px","object-fit","contain"],["lines","full",1,"menu-list"],["button","",2,"padding-top","10px",3,"click"],["src","assets/info.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],[2,"padding-left","15px","font-size","17px"],["src","assets/system-icon.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["name","notifications-outline","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/medical-call.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/first-aid-box.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/shield.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/terms-and-conditions.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["button","",2,"padding-top","10px","--color","#d32f2f",3,"click"],["name","log-out-outline","slot","start",2,"font-size","28px","color","#d32f2f"],[2,"padding-left","15px","font-size","17px","color","#d32f2f"]],template:function(r,l){r&1&&(e(0,"ion-header",0),o(1,"ion-toolbar"),t(),e(2,"ion-content")(3,"div",1)(4,"div",2)(5,"div",3),o(6,"img",4),t()()(),e(7,"ion-list",5)(8,"ion-item",6),d("click",function(){return l.openGuideModal()}),o(9,"img",7),e(10,"ion-label",8),n(11,"Reference Guide for Map Symbols"),t()(),e(12,"ion-item",6),d("click",function(){return l.openUserGuideModal()}),o(13,"img",9),e(14,"ion-label",8),n(15,"User Guide"),t()(),e(16,"ion-item",6),d("click",function(){return l.openNotificationHistoryModal()}),o(17,"ion-icon",10),e(18,"ion-label",8),n(19,"Push Notification History"),t()(),e(20,"ion-item",6),d("click",function(){return l.openEmergencyContactsModal()}),o(21,"img",11),e(22,"ion-label",8),n(23,"Emergency Contacts"),t()(),e(24,"ion-item",6),d("click",function(){return l.openSafetyTipsModal()}),o(25,"img",12),e(26,"ion-label",8),n(27,"Safety Tips"),t()(),e(28,"ion-item",6),d("click",function(){return l.openPrivacyModal()}),o(29,"img",13),e(30,"ion-label",8),n(31,"Privacy Policy"),t()(),e(32,"ion-item",6),d("click",function(){return l.openTermsModal()}),o(33,"img",14),e(34,"ion-label",8),n(35,"Terms and Condition"),t()(),e(36,"ion-item",15),d("click",function(){return l.logout()}),o(37,"ion-icon",16),e(38,"ion-label",17),n(39,"Log Out"),t()()()()),r&2&&m("translucent",!0)},dependencies:[S,C,_,O,H,N,G,y,b,R,q],styles:['@charset "UTF-8";ion-content[_ngcontent-%COMP%]{--overflow: hidden}ion-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6,#2563eb);box-shadow:0 4px 12px #3b82f633}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent;--color: white}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#fff}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{--color: white}.profile-header[_ngcontent-%COMP%]{position:relative;margin-bottom:5px}.profile-background[_ngcontent-%COMP%]{background:#fff;height:150px;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;overflow:hidden}.profile-avatar[_ngcontent-%COMP%]{width:80px;height:80px;background:#fff3;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:16px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:3px solid rgba(255,255,255,.3);box-shadow:0 8px 32px #0000001a}.profile-avatar[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%]{font-size:40px;color:#fff}.profile-info[_ngcontent-%COMP%]{text-align:center;color:#fff}.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 8px;font-size:24px;font-weight:600;text-shadow:0 2px 4px rgba(0,0,0,.1)}.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px;opacity:.9;font-weight:400}.menu-list[_ngcontent-%COMP%]{background:transparent;margin-top:5px;padding:0 16px}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px;--min-height: 50px;margin-bottom:4px;border-radius:12px;--background: white;box-shadow:0 2px 8px #0000001a;transition:all .3s ease}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:16px;color:var(--ion-color-primary)}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark)}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%)}.terms-modal[_ngcontent-%COMP%]{--height: 90%;--border-radius: 16px}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-light)}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:24px;font-weight:700;margin-bottom:8px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .effective-date[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:14px;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:18px;font-weight:600;margin:24px 0 12px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:16px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:8px;padding-left:24px;position:relative}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\2022";position:absolute;left:8px;color:var(--ion-color-primary)}.terms-modal[_ngcontent-%COMP%]   .legend-title[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:20px;font-weight:600;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]{margin-top:30px;display:flex;flex-direction:column;align-items:center}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;background:var(--ion-color-light);border-radius:8px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:32px;height:32px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .legend-icon[_ngcontent-%COMP%]{font-size:20px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:16px}']})}}return a})(),de=(()=>{class a{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:61,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"terms-content"],[1,"modal-section-title"],[1,"effective-date"],[1,"welcome"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),n(4,"Terms and Conditions"),t()(),e(5,"ion-buttons",1)(6,"ion-button",2),d("click",function(){return l.dismiss()}),n(7,"Close"),t()()()(),e(8,"ion-content",3)(9,"div",4)(10,"h1",5)(11,"strong"),n(12,"Terms and Conditions"),t()(),e(13,"p",6),n(14,"Effective Date: April 26, 2025"),t(),e(15,"p",7),n(16,'Welcome to Evacuation Mapping System ("we", "our", or "us"). These '),e(17,"strong"),n(18,"Terms and Conditions"),t(),n(19,' ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.'),t(),e(20,"section")(21,"h2",5),n(22,"1. User Eligibility"),t(),e(23,"p"),n(24,"To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete."),t()(),e(25,"section")(26,"h2",5),n(27,"2. User Account"),t(),e(28,"p"),n(29,"To access certain features of the Service, you must create an account. You agree to provide:"),t(),e(30,"ul")(31,"li"),n(32,"Your full name"),t(),e(33,"li"),n(34,"A valid email address"),t(),e(35,"li"),n(36,"A password"),t(),e(37,"li"),n(38,"Your location data (for accurate evacuation mapping)"),t()(),e(39,"p"),n(40,"You are responsible for maintaining the confidentiality of your account and for all activities that occur under your account."),t()(),e(41,"section")(42,"h2",5),n(43,"3. Use of Service"),t(),e(44,"p"),n(45,"You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account."),t()(),e(46,"section")(47,"h2",5),n(48,"4. Modifications"),t(),e(49,"p"),n(50,"We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes."),t()(),e(51,"section")(52,"h2",5),n(53,"5. Limitation of Liability"),t(),e(54,"p"),n(55,"We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service."),t()(),e(56,"section")(57,"h2",5),n(58,"6. Termination"),t(),e(59,"p"),n(60,"We may suspend or terminate your access to the Service if you violate these Terms."),t()()()())},dependencies:[S,M,E,C,_,P,y],styles:[Q]})}}return a})(),me=(()=>{class a{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:66,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"effective-date"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),n(4,"Privacy Policy"),t()(),e(5,"ion-buttons",1)(6,"ion-button",2),d("click",function(){return l.dismiss()}),n(7,"Close"),t()()()(),e(8,"ion-content",3)(9,"h2",4)(10,"strong"),n(11,"Privacy Policy"),t()(),e(12,"p",5),n(13,"Effective Date: April 26, 2025"),t(),e(14,"p"),n(15,"DisasterGuard is committed to protecting your privacy. This "),e(16,"strong"),n(17,"Privacy Policy"),t(),n(18," outlines how we collect, use, and protect your information when you use our evacuation mapping system."),t(),e(19,"h3",4),n(20,"1. Information We Collect"),t(),e(21,"p"),n(22,"We collect the following personal information upon registration:"),t(),e(23,"ul")(24,"li"),n(25,"Name"),t(),e(26,"li"),n(27,"Email address"),t(),e(28,"li"),n(29,"Password (stored securely)"),t(),e(30,"li"),n(31,"Location data (for evacuation mapping purposes)"),t()(),e(32,"h3",4),n(33,"2. How We Use Your Information"),t(),e(34,"p"),n(35,"Your data is used solely to:"),t(),e(36,"ul")(37,"li"),n(38,"Provide personalized evacuation routes and mapping"),t(),e(39,"li"),n(40,"Contact you regarding urgent updates or emergencies"),t(),e(41,"li"),n(42,"Improve system functionality"),t()(),e(43,"p"),n(44,"We do not sell, rent, or share your personal information with third parties, except as required by law or to ensure user safety during emergencies."),t(),e(45,"h3",4),n(46,"3. Data Security"),t(),e(47,"p"),n(48,"We implement appropriate security measures to protect your data. Your password is encrypted, and location data is only used to provide real-time evacuation support."),t(),e(49,"h3",4),n(50,"4. Your Rights"),t(),e(51,"p"),n(52,"You may:"),t(),e(53,"ul")(54,"li"),n(55,"Access or update your personal data"),t(),e(56,"li"),n(57,"Request deletion of your account"),t(),e(58,"li"),n(59,"Opt-out of communications at any time"),t()(),e(60,"p"),n(61,"To do so, contact us at: <EMAIL>"),t(),e(62,"h3",4),n(63,"5. Changes to This Policy"),t(),e(64,"p"),n(65,"We may update this Privacy Policy occasionally. You will be notified of any significant changes."),t()())},dependencies:[S,M,E,C,_,P,y],styles:[Q]})}}return a})(),pe=(()=>{class a{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:103,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"legend-section"],[1,"section-header"],[1,"legend-items"],[1,"legend-item"],["src","assets/Location.png",1,"legend-icon-img"],[1,"legend-label"],["src","assets/forEarthquake.png",1,"legend-icon-img"],["src","assets/forTyphoon.png",1,"legend-icon-img"],["src","assets/forFlood.png",1,"legend-icon-img"],["src","assets/forFire.png",1,"legend-icon-img"],["src","assets/forLandslide.png",1,"legend-icon-img"],["src","assets/forOthers.png",1,"legend-icon-img"],["src","assets/forMultiple.png",1,"legend-icon-img"],["src","assets/home-insuranceForEarthquake.png",1,"legend-icon-img"],["src","assets/downloadForEarthquake.png",1,"legend-icon-img"],["src","assets/compassForEarthquake.png",1,"legend-icon-img"],["src","assets/walking.png",1,"legend-icon-img"],["src","assets/bike.png",1,"legend-icon-img"],["src","assets/car.png",1,"legend-icon-img"],["src","assets/home1.png",1,"legend-icon-img"],["src","assets/search1.png",1,"legend-icon-img"],["src","assets/map1.png",1,"legend-icon-img"],["src","assets/lamp1.png",1,"legend-icon-img"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Map Symbols Guide"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),d("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"h3",4)(9,"strong"),n(10,"Reference Guide for Map Symbols"),t()(),e(11,"div",5)(12,"h4",6),n(13,"\u{1F4CD} Location Markers"),t(),e(14,"div",7)(15,"div",8),o(16,"img",9),e(17,"span",10),n(18,"Your Current Location"),t()()()(),e(19,"div",5)(20,"h4",6),n(21,"\u{1F3E0} Evacuation Centers by Disaster Type"),t(),e(22,"div",7)(23,"div",8),o(24,"img",11),e(25,"span",10),n(26,"Earthquake Evacuation Centers"),t()(),e(27,"div",8),o(28,"img",12),e(29,"span",10),n(30,"Typhoon Evacuation Centers"),t()(),e(31,"div",8),o(32,"img",13),e(33,"span",10),n(34,"Flood Evacuation Centers"),t()(),e(35,"div",8),o(36,"img",14),e(37,"span",10),n(38,"Fire Evacuation Centers"),t()(),e(39,"div",8),o(40,"img",15),e(41,"span",10),n(42,"Landslide Evacuation Centers"),t()(),e(43,"div",8),o(44,"img",16),e(45,"span",10),n(46,"Other Disaster Centers"),t()(),e(47,"div",8),o(48,"img",17),e(49,"span",10),n(50,"Multiple Disaster Centers"),t()()()(),e(51,"div",5)(52,"h4",6),n(53,"\u{1F39B}\uFE0F Map Control Icons"),t(),e(54,"div",7)(55,"div",8),o(56,"img",18),e(57,"span",10),n(58,"Show All Evacuation Centers List"),t()(),e(59,"div",8),o(60,"img",19),e(61,"span",10),n(62,"Download Map with Routes"),t()(),e(63,"div",8),o(64,"img",20),e(65,"span",10),n(66,"Route to 2 Nearest Centers"),t()()()(),e(67,"div",5)(68,"h4",6),n(69,"\u{1F6B6} Navigation Options"),t(),e(70,"div",7)(71,"div",8),o(72,"img",21),e(73,"span",10),n(74,"Walking Route"),t()(),e(75,"div",8),o(76,"img",22),e(77,"span",10),n(78,"Cycling Route"),t()(),e(79,"div",8),o(80,"img",23),e(81,"span",10),n(82,"Driving Route"),t()()()(),e(83,"div",5)(84,"h4",6),n(85,"\u{1F4F1} App Features"),t(),e(86,"div",7)(87,"div",8),o(88,"img",24),e(89,"span",10),n(90,"Home - Disaster Selection"),t()(),e(91,"div",8),o(92,"img",25),e(93,"span",10),n(94,"Search - Find Locations"),t()(),e(95,"div",8),o(96,"img",26),e(97,"span",10),n(98,"Map - View All Centers"),t()(),e(99,"div",8),o(100,"img",27),e(101,"span",10),n(102,"Tips - Safety Information"),t()()()()())},dependencies:[S,M,E,C,_,P,y,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:20px;text-align:center}.legend-section[_ngcontent-%COMP%]{margin-bottom:25px}.section-header[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;margin-bottom:12px;color:var(--ion-color-primary);border-bottom:1px solid var(--ion-color-light);padding-bottom:5px}.legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.legend-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px;padding:8px;background:var(--ion-color-light);border-radius:8px}.legend-label[_ngcontent-%COMP%]{flex-grow:1;font-size:.9rem}.legend-icon-img[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:contain;flex-shrink:0}"]})}}return a})(),ge=(()=>{class a{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:44,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],["name","call-outline","slot","start"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Emergency Contacts"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),d("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"ion-list")(9,"ion-item"),o(10,"ion-icon",4),e(11,"ion-label")(12,"h2"),n(13,"National Emergency Hotline"),t(),e(14,"p"),n(15,"911"),t()()(),e(16,"ion-item"),o(17,"ion-icon",4),e(18,"ion-label")(19,"h2"),n(20,"Bureau of Fire Protection"),t(),e(21,"p"),n(22,"256-0541/42"),t()()(),e(23,"ion-item"),o(24,"ion-icon",4),e(25,"ion-label")(26,"h2"),n(27,"Cebu City Police Hotline"),t(),e(28,"p"),n(29,"166"),t()()(),e(30,"ion-item"),o(31,"ion-icon",4),e(32,"ion-label")(33,"h2"),n(34,"Red Cross Cebu Chapter"),t(),e(35,"p"),n(36,"(032) 253-4611"),t()()(),e(37,"ion-item"),o(38,"ion-icon",4),e(39,"ion-label")(40,"h2"),n(41,"Local Disaster Office"),t(),e(42,"p"),n(43,"Contact your LGU"),t()()()()())},dependencies:[S,M,E,C,_,O,H,N,G,P,y,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}h2[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:4px}p[_ngcontent-%COMP%]{font-size:.95rem;color:var(--ion-color-medium)}"]})}}return a})(),ue=(()=>{class a{constructor(i){this.modalCtrl=i,this.expandedCards={earthquake:!1,flood:!1,typhoon:!1,fire:!1,landslide:!1,general:!1}}toggleCard(i){this.expandedCards[i]=!this.expandedCards[i]}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:55,vars:12,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"safety-tips-container"],[1,"disaster-card","earthquake-card"],[1,"card-header-clickable",3,"click"],["name","pulse-outline",1,"disaster-icon"],[1,"disaster-text"],[1,"expand-icon",3,"name"],["class","disaster-content",4,"ngIf"],[1,"disaster-card","flood-card"],[1,"disaster-card","typhoon-card"],["name","cloudy-outline",1,"disaster-icon"],[1,"disaster-card","fire-card"],["name","flame-outline",1,"disaster-icon"],[1,"disaster-card","landslide-card"],["name","triangle-outline",1,"disaster-icon"],[1,"disaster-card","general-card"],[1,"disaster-content"],[1,"disaster-image-header"],["src","assets/linogs.jpg","alt","Earthquake Safety",1,"disaster-header-image"],[1,"safety-tips-list"],["src","assets/floods.jpg","alt","Flood Safety",1,"disaster-header-image"],["src","assets/typhoons.jpg","alt","Typhoon Safety",1,"disaster-header-image"],["src","assets/fires.jpg","alt","Fire Safety",1,"disaster-header-image"],["src","assets/landslides.jpg","alt","Landslide Safety",1,"disaster-header-image"],["src","assets/icon/generalSettings.png","alt","General Emergency Safety",1,"disaster-header-image"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Helpful Tips to Prepare for Disasters"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),d("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"div",4)(9,"ion-card",5)(10,"ion-card-header",6),d("click",function(){return l.toggleCard("earthquake")}),e(11,"ion-card-title"),o(12,"ion-icon",7),e(13,"span",8),n(14,"Earthquake"),t(),o(15,"ion-icon",9),t()(),f(16,X,18,0,"ion-card-content",10),t(),e(17,"ion-card",11)(18,"ion-card-header",6),d("click",function(){return l.toggleCard("flood")}),e(19,"ion-card-title")(20,"span",8),n(21,"Flood"),t(),o(22,"ion-icon",9),t()(),f(23,ee,18,0,"ion-card-content",10),t(),e(24,"ion-card",12)(25,"ion-card-header",6),d("click",function(){return l.toggleCard("typhoon")}),e(26,"ion-card-title"),o(27,"ion-icon",13),e(28,"span",8),n(29,"Typhoon"),t(),o(30,"ion-icon",9),t()(),f(31,te,18,0,"ion-card-content",10),t(),e(32,"ion-card",14)(33,"ion-card-header",6),d("click",function(){return l.toggleCard("fire")}),e(34,"ion-card-title"),o(35,"ion-icon",15),e(36,"span",8),n(37,"Fire"),t(),o(38,"ion-icon",9),t()(),f(39,ne,18,0,"ion-card-content",10),t(),e(40,"ion-card",16)(41,"ion-card-header",6),d("click",function(){return l.toggleCard("landslide")}),e(42,"ion-card-title"),o(43,"ion-icon",17),e(44,"span",8),n(45,"Landslide"),t(),o(46,"ion-icon",9),t()(),f(47,ie,18,0,"ion-card-content",10),t(),e(48,"ion-card",18)(49,"ion-card-header",6),d("click",function(){return l.toggleCard("general")}),e(50,"ion-card-title")(51,"span",8),n(52,"General Emergency"),t(),o(53,"ion-icon",9),t()(),f(54,oe,18,0,"ion-card-content",10),t()()()),r&2&&(c(15),m("name",l.expandedCards.earthquake?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.earthquake),c(6),m("name",l.expandedCards.flood?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.flood),c(7),m("name",l.expandedCards.typhoon?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.typhoon),c(7),m("name",l.expandedCards.fire?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.fire),c(7),m("name",l.expandedCards.landslide?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.landslide),c(6),m("name",l.expandedCards.general?"chevron-down-outline":"chevron-up-outline"),c(),m("ngIf",l.expandedCards.general))},dependencies:[S,M,E,W,Y,K,$,C,_,O,P,y,b,z],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:700}.safety-tips-container[_ngcontent-%COMP%]{padding:0;max-width:400px;margin:0 auto}.disaster-card[_ngcontent-%COMP%]{margin:8px 0;border-radius:12px;box-shadow:0 2px 8px #0000001a}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]{cursor:pointer;padding:12px 16px;transition:background-color .2s ease}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:1.1rem;font-weight:600;color:#000!important;padding-left:20px}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{width:24px;margin-right:12px;font-size:1.3rem;text-align:center}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   .disaster-text[_ngcontent-%COMP%]{flex:1;margin-left:36px}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{margin-left:auto;font-size:1.2rem;transition:transform .3s ease}.disaster-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]:hover{background-color:var(--ion-color-light)}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]{padding:0 16px 16px}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]   .disaster-image-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:16px}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]   .disaster-image-header[_ngcontent-%COMP%]   .disaster-header-image[_ngcontent-%COMP%]{width:100%;max-width:300px;height:180px;object-fit:cover;border-radius:8px;box-shadow:0 2px 8px #0000001a}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]   .safety-tips-list[_ngcontent-%COMP%]{margin:0;padding-left:20px}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]   .safety-tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;line-height:1.4;color:var(--ion-color-dark);font-size:.95rem}.disaster-card[_ngcontent-%COMP%]   .disaster-content[_ngcontent-%COMP%]   .safety-tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child{margin-bottom:0}.flood-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%], .general-card[_ngcontent-%COMP%]   .card-header-clickable[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{padding-left:56px}"]})}}return a})(),fe=(()=>{class a{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:138,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"guide-section"],[1,"section-title"],[1,"section-content"],[1,"feature-list"],[1,"feature-item"],["src","assets/home1.png",1,"feature-icon"],[1,"feature-text"],["src","assets/map1.png",1,"feature-icon"],["src","assets/search1.png",1,"feature-icon"],["src","assets/lamp1.png",1,"feature-icon"],[1,"steps-list"],[1,"step-item"],[1,"step-number"],[1,"step-content"],[1,"disaster-grid"],[1,"disaster-type"],["src","assets/earthquake-icon.svg",1,"disaster-icon"],["src","assets/icon/bagyo.png",1,"disaster-icon"],["src","assets/flood.png",1,"disaster-icon"],["src","assets/fireIcon.png",1,"disaster-icon"],["src","assets/landslideIcon.png",1,"disaster-icon"],["src","assets/otherdisasterIcon.png",1,"disaster-icon"],[1,"tips-list"],[1,"tip-item"],["name","call-outline",1,"tip-icon"],["name","location-outline",1,"tip-icon"],["name","download-outline",1,"tip-icon"],["name","notifications-outline",1,"tip-icon"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"ALERTO User Guide"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),d("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"div",4)(9,"h3",5),n(10,"\u{1F6E1}\uFE0F Welcome to ALERTO - The Safe Zone"),t(),e(11,"p",6),n(12," ALERTO is your comprehensive disaster preparedness and evacuation assistance app. Our mission is to keep you safe by providing real-time access to evacuation centers, emergency contacts, and safety information during natural disasters. "),t()(),e(13,"div",4)(14,"h3",5),n(15,"\u2728 Main Features"),t(),e(16,"div",7)(17,"div",8),o(18,"img",9),e(19,"div",10)(20,"h4"),n(21,"Disaster Selection"),t(),e(22,"p"),n(23,"Choose from 6 disaster types to find specific evacuation centers"),t()()(),e(24,"div",8),o(25,"img",11),e(26,"div",10)(27,"h4"),n(28,"Interactive Maps"),t(),e(29,"p"),n(30,"View evacuation centers with real-time navigation and routing"),t()()(),e(31,"div",8),o(32,"img",12),e(33,"div",10)(34,"h4"),n(35,"Location Search"),t(),e(36,"p"),n(37,"Find specific locations and nearby evacuation centers"),t()()(),e(38,"div",8),o(39,"img",13),e(40,"div",10)(41,"h4"),n(42,"Safety Tips & Contacts"),t(),e(43,"p"),n(44,"Access emergency contacts and disaster-specific safety information"),t()()()()(),e(45,"div",4)(46,"h3",5),n(47,"\u{1F4F1} How to Use ALERTO"),t(),e(48,"div",14)(49,"div",15)(50,"div",16),n(51,"1"),t(),e(52,"div",17)(53,"h4"),n(54,"Select Disaster Type"),t(),e(55,"p"),n(56,"From the home screen, tap on the disaster type you need help with (Earthquake, Typhoon, Flood, Fire, Landslide, or General)"),t()()(),e(57,"div",15)(58,"div",16),n(59,"2"),t(),e(60,"div",17)(61,"h4"),n(62,"View Evacuation Centers"),t(),e(63,"p"),n(64,"The map will show evacuation centers specific to your selected disaster type with your current location"),t()()(),e(65,"div",15)(66,"div",16),n(67,"3"),t(),e(68,"div",17)(69,"h4"),n(70,"Use Map Controls"),t(),e(71,"p"),n(72,"\u2022 Tap the house icon to see all centers list"),o(73,"br"),n(74,"\u2022 Tap download to save map offline"),o(75,"br"),n(76,"\u2022 Tap compass to route to 2 nearest centers"),t()()(),e(77,"div",15)(78,"div",16),n(79,"4"),t(),e(80,"div",17)(81,"h4"),n(82,"Navigate to Safety"),t(),e(83,"p"),n(84,"Choose walking, cycling, or driving routes to reach your selected evacuation center safely"),t()()()()(),e(85,"div",4)(86,"h3",5),n(87,"\u{1F32A}\uFE0F Supported Disaster Types"),t(),e(88,"div",18)(89,"div",19),o(90,"img",20),e(91,"span"),n(92,"Earthquake"),t()(),e(93,"div",19),o(94,"img",21),e(95,"span"),n(96,"Typhoon"),t()(),e(97,"div",19),o(98,"img",22),e(99,"span"),n(100,"Flood"),t()(),e(101,"div",19),o(102,"img",23),e(103,"span"),n(104,"Fire"),t()(),e(105,"div",19),o(106,"img",24),e(107,"span"),n(108,"Landslide"),t()(),e(109,"div",19),o(110,"img",25),e(111,"span"),n(112,"General"),t()()()(),e(113,"div",4)(114,"h3",5),n(115,"\u{1F6A8} Emergency Tips"),t(),e(116,"div",26)(117,"div",27),o(118,"ion-icon",28),e(119,"p"),n(120,"Always call 911 for immediate emergency assistance"),t()(),e(121,"div",27),o(122,"ion-icon",29),e(123,"p"),n(124,"Enable location services for accurate evacuation center directions"),t()(),e(125,"div",27),o(126,"ion-icon",30),e(127,"p"),n(128,"Download maps when you have internet for offline access during emergencies"),t()(),e(129,"div",27),o(130,"ion-icon",31),e(131,"p"),n(132,"Keep notifications enabled to receive emergency alerts"),t()()()(),e(133,"div",4)(134,"h3",5),n(135,"\u{1F4DE} Need Help?"),t(),e(136,"p",6),n(137," For technical support or questions about ALERTO, visit the Tips tab for emergency contacts and safety information. Stay safe and prepared! "),t()()())},dependencies:[S,M,E,C,_,O,P,y,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.3rem;font-weight:700;color:var(--ion-color-primary)}.guide-section[_ngcontent-%COMP%]{margin-bottom:25px;padding-bottom:20px;border-bottom:1px solid var(--ion-color-light)}.guide-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.section-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:15px;color:var(--ion-color-primary)}.section-content[_ngcontent-%COMP%]{font-size:.95rem;line-height:1.5;color:var(--ion-color-medium-shade)}.feature-list[_ngcontent-%COMP%], .steps-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px}.feature-item[_ngcontent-%COMP%], .step-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:12px;background:var(--ion-color-light);border-radius:10px}.feature-icon[_ngcontent-%COMP%]{width:32px;height:32px;object-fit:contain;flex-shrink:0}.feature-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;margin-bottom:5px;color:var(--ion-color-dark)}.feature-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;color:var(--ion-color-medium-shade);margin:0;line-height:1.4}.step-number[_ngcontent-%COMP%]{width:30px;height:30px;background:var(--ion-color-primary);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:.9rem;flex-shrink:0}.disaster-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:15px}.disaster-type[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;padding:15px;background:var(--ion-color-light);border-radius:10px;text-align:center}.disaster-icon[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:contain}.disaster-type[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.85rem;font-weight:500;color:var(--ion-color-dark)}.tips-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.tip-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:10px;background:var(--ion-color-light);border-radius:8px}.tip-icon[_ngcontent-%COMP%]{color:var(--ion-color-primary);font-size:1.2rem;flex-shrink:0}.tip-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem;color:var(--ion-color-medium-shade)}"]})}}return a})(),he=(()=>{class a{constructor(i,r){this.modalCtrl=i,this.http=r,this.notifications=[],this.isLoading=!0}ngOnInit(){this.loadNotificationHistory()}loadNotificationHistory(){return p(this,null,function*(){try{let i=yield this.http.get(`${I.apiUrl}/notifications?limit=50`).toPromise();i&&(this.notifications=i.notifications||[])}catch(i){console.error("Error loading notification history:",i)}finally{this.isLoading=!1}})}formatTime(i){let r=new Date(i),s=new Date().getTime()-r.getTime(),w=Math.floor(s/6e4),j=Math.floor(s/36e5),A=Math.floor(s/864e5);return w<1?"Just now":w<60?`${w}m ago`:j<24?`${j}h ago`:A<7?`${A}d ago`:r.toLocaleDateString()}getCategoryLabel(i){switch(i){case"emergency_alert":return"Emergency";case"evacuation_center_added":return"Evacuation";case"system_update":return"System";default:return"General"}}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(r){return new(r||a)(g(v),g(L))}}static{this.\u0275cmp=h({type:a,selectors:[["ng-component"]],standalone:!0,features:[x],decls:41,vars:3,consts:[["slot","start"],[3,"click"],["name","arrow-back"],[1,"modal-title"],[1,"ion-padding"],[1,"history-section"],[1,"section-title"],[1,"section-content"],["class","history-section",4,"ngIf"],[1,"info-section"],[1,"section-header"],[1,"info-items"],[1,"info-item"],["name","warning-outline",1,"info-icon","emergency"],[1,"info-content"],["name","home-outline",1,"info-icon","evacuation"],["name","information-circle-outline",1,"info-icon","system"],[1,"notification-list"],["class","notification-item",4,"ngFor","ngForOf"],[1,"notification-item"],[1,"notification-header"],[1,"notification-title"],[1,"notification-time"],[1,"notification-message"],["class","notification-meta",4,"ngIf"],[1,"notification-meta"],[1,"notification-category"],[1,"notification-status"],[1,"empty-state"],["name","notifications-off-outline",1,"empty-icon"],[1,"loading-state"]],template:function(r,l){r&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-buttons",0)(3,"ion-button",1),d("click",function(){return l.dismiss()}),o(4,"ion-icon",2),t()(),e(5,"ion-title",3),n(6,"Push Notification History"),t()()(),e(7,"ion-content",4)(8,"div",5)(9,"h3",6),n(10,"\u{1F4F1} FCM Notifications"),t(),e(11,"p",7),n(12," This shows the history of push notifications sent via Firebase Cloud Messaging (FCM). These notifications include emergency alerts, evacuation center updates, and system notifications. "),t()(),f(13,le,5,1,"div",8)(14,se,7,0,"div",8)(15,ce,5,0,"div",8),e(16,"div",9)(17,"h4",10),n(18,"\u2139\uFE0F About Push Notifications"),t(),e(19,"div",11)(20,"div",12),o(21,"ion-icon",13),e(22,"div",14)(23,"h5"),n(24,"Emergency Alerts"),t(),e(25,"p"),n(26,"Critical notifications for fire and landslide disasters with sound and vibration"),t()()(),e(27,"div",12),o(28,"ion-icon",15),e(29,"div",14)(30,"h5"),n(31,"Evacuation Centers"),t(),e(32,"p"),n(33,"Updates when new evacuation centers are added to your area"),t()()(),e(34,"div",12),o(35,"ion-icon",16),e(36,"div",14)(37,"h5"),n(38,"System Updates"),t(),e(39,"p"),n(40,"App updates and important system announcements"),t()()()()()()),r&2&&(c(13),m("ngIf",l.notifications.length>0),c(),m("ngIf",l.notifications.length===0&&!l.isLoading),c(),m("ngIf",l.isLoading))},dependencies:[S,M,E,C,_,O,J,P,y,b,U,z],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:var(--ion-color-primary)}.history-section[_ngcontent-%COMP%]{margin-bottom:25px;padding-bottom:20px;border-bottom:1px solid var(--ion-color-light)}.history-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.section-title[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:15px;color:var(--ion-color-primary)}.section-header[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;margin-bottom:15px;color:var(--ion-color-dark)}.section-content[_ngcontent-%COMP%]{font-size:.9rem;line-height:1.5;color:var(--ion-color-medium-shade);margin:0}.notification-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.notification-item[_ngcontent-%COMP%]{background:var(--ion-color-light);border-radius:8px;padding:12px;border-left:4px solid var(--ion-color-primary)}.notification-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:8px}.notification-title[_ngcontent-%COMP%]{font-size:.95rem;font-weight:600;margin:0;color:var(--ion-color-dark);flex:1}.notification-time[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium);margin-left:8px}.notification-message[_ngcontent-%COMP%]{font-size:.85rem;color:var(--ion-color-medium-shade);margin:0 0 8px;line-height:1.4}.notification-meta[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.notification-category[_ngcontent-%COMP%]{font-size:.75rem;padding:2px 6px;border-radius:4px;font-weight:500}.category-emergency_alert[_ngcontent-%COMP%]{background:#ffebee;color:#c62828}.category-evacuation_center_added[_ngcontent-%COMP%]{background:#e8f5e8;color:#2e7d32}.category-system_update[_ngcontent-%COMP%]{background:#e3f2fd;color:#1565c0}.category-general[_ngcontent-%COMP%]{background:#f3e5f5;color:#7b1fa2}.notification-status[_ngcontent-%COMP%]{font-size:.75rem;padding:2px 6px;border-radius:4px;font-weight:500}.notification-status.read[_ngcontent-%COMP%]{background:#e8f5e8;color:#2e7d32}.notification-status.unread[_ngcontent-%COMP%]{background:#fff3e0;color:#ef6c00}.empty-state[_ngcontent-%COMP%], .loading-state[_ngcontent-%COMP%]{text-align:center;padding:32px 16px}.empty-icon[_ngcontent-%COMP%]{font-size:48px;color:var(--ion-color-medium);margin-bottom:16px}.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin:0 0 8px;color:var(--ion-color-dark)}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;color:var(--ion-color-medium-shade);margin:0;line-height:1.5}.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:var(--ion-color-medium)}.info-section[_ngcontent-%COMP%]{background:var(--ion-color-light);border-radius:8px;padding:16px;margin-top:16px}.info-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.info-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px}.info-icon[_ngcontent-%COMP%]{font-size:20px;margin-top:2px}.info-icon.emergency[_ngcontent-%COMP%]{color:#f44336}.info-icon.evacuation[_ngcontent-%COMP%]{color:#4caf50}.info-icon.system[_ngcontent-%COMP%]{color:#2196f3}.info-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;margin:0 0 4px;color:var(--ion-color-dark)}.info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium-shade);margin:0;line-height:1.4}"]})}}return a})();export{ge as EmergencyContactsModalComponent,pe as GuideModalComponent,he as NotificationHistoryModalComponent,me as PrivacyModalComponent,Oe as ProfilePage,ue as SafetyTipsModalComponent,de as TermsModalComponent,fe as UserGuideModalComponent};
