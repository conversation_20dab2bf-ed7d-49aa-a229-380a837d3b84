<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use App\Models\SystemLog;
use App\Models\ApiKey;
use App\Models\SystemBackup;
use App\Models\User;
use App\Models\Notification;
use App\Models\Evacuation;

use App\Models\Barangay;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Mail\AdminRegistrationMail;


class SuperAdminController extends Controller
{

    public function dashboard()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get barangay-specific stats
        $barangayStats = [];

        try {
            // Get barangays from database, fallback to default list if none exist
            $barangays = Barangay::pluck('name')->toArray();
        } catch (\Exception $e) {
            \Log::warning('Failed to fetch barangays from database', ['error' => $e->getMessage()]);
            $barangays = [];
        }

        // If no barangays in database, use default list
        if (empty($barangays)) {
            $barangays = ['Lahug', 'Mabolo', 'Luz', 'Banilad', 'Guadalupe', 'Talamban'];
        }

        foreach ($barangays as $barangay) {
            try {
                $barangayStats[$barangay] = [
                    'total_users' => User::where('barangay', $barangay)->count(),
                    'active_users' => User::where('barangay', $barangay)->where('status', 'Active')->count(),
                    'total_notifications' => Notification::where('barangay', $barangay)->count(),
                    'active_centers' => Evacuation::where('barangay', $barangay)->where('status', 'active')->count(),
                    'recent_notifications' => Notification::where('barangay', $barangay)
                        ->orderBy('created_at', 'desc')
                        ->take(5)
                        ->get()
                ];
            } catch (\Exception $e) {
                \Log::warning("Failed to get stats for barangay: $barangay", ['error' => $e->getMessage()]);
                $barangayStats[$barangay] = [
                    'total_users' => 0,
                    'active_users' => 0,
                    'total_notifications' => 0,
                    'active_centers' => 0,
                    'recent_notifications' => collect()
                ];
            }
        }

        // Get main dashboard stats with error handling
        try {
            $stats = [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'Active')->count(),
                'total_logs' => SystemLog::count(),
                'recent_logs' => SystemLog::with('user')->latest()->take(5)->get(),
                'system_settings' => SystemSetting::all(),
                'api_keys' => ApiKey::where('is_active', true)->count(),
                'backups' => SystemBackup::latest()->take(5)->get(),
                'barangay_stats' => $barangayStats
            ];
        } catch (\Exception $e) {
            \Log::error('Failed to get dashboard stats', ['error' => $e->getMessage()]);
            $stats = [
                'total_users' => 0,
                'active_users' => 0,
                'total_logs' => 0,
                'recent_logs' => collect(),
                'system_settings' => collect(),
                'api_keys' => 0,
                'backups' => collect(),
                'barangay_stats' => $barangayStats
            ];
        }

        return view('components.superadmin.dashboard', compact('stats'));
    }


    public function systemSettings()
    {
        $settings = SystemSetting::all()->groupBy('group');
        return view('components.superadmin.settings', compact('settings'));
    }

    public function updateSystemSettings(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        foreach ($request->settings as $key => $value) {
            SystemSetting::set($key, $value);
        }

        SystemLog::log('settings_updated', $user, null, 'System settings updated');

        return redirect()->back()->with('success', 'System settings updated successfully.');
    }

    public function systemLogs()
    {
        $logs = SystemLog::with('user')->latest()->paginate(20);
        return view('components.superadmin.logs', compact('logs'));
    }

    public function apiKeys()
    {
        $apiKeys = ApiKey::with('user')->latest()->get();
        return view('components.superadmin.api-keys', compact('apiKeys'));
    }

    public function generateApiKey(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $apiKey = ApiKey::create([
            'name' => $request->name,
            'key' => Str::random(32),
            'user_id' => $user->id
        ]);

        SystemLog::log('api_key_generated', $user, $apiKey);

        return redirect()->back()->with('success', 'API key generated successfully.');
    }

    public function revokeApiKey($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $apiKey = ApiKey::findOrFail($id);
        $apiKey->update(['is_active' => false]);

        SystemLog::log('api_key_revoked', $user, $apiKey);

        return redirect()->back()->with('success', 'API key revoked successfully.');
    }

    public function createBackup()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $filename = 'backup-' . date('Y-m-d-H-i-s') . '.sql';
        
        $backup = SystemBackup::create([
            'filename' => $filename,
            'type' => 'full',
            'status' => 'in_progress',
            'created_by' => $user->id
        ]);

        try {
            Artisan::call('backup:run');
            
            $backup->update([
                'status' => 'completed',
                'notes' => 'Backup completed successfully'
            ]);

            SystemLog::log('backup_created', $user, $backup);
            
            return redirect()->back()->with('success', 'Backup created successfully.');
        } catch (\Exception $e) {
            $backup->update([
                'status' => 'failed',
                'notes' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Backup failed: ' . $e->getMessage());
        }
    }

    public function restoreBackup($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $backup = SystemBackup::findOrFail($id);
        
        try {
            Artisan::call('backup:restore', ['--filename' => $backup->filename]);
            
            SystemLog::log('backup_restored', $user, $backup);
            
            return redirect()->back()->with('success', 'Backup restored successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Restore failed: ' . $e->getMessage());
        }
    }

    public function systemHealth()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $health = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connection' => \DB::connection()->getPdo() ? 'Connected' : 'Disconnected',
            'storage_writable' => is_writable(storage_path()) ? 'Yes' : 'No',
            'cache_writable' => is_writable(storage_path('framework/cache')) ? 'Yes' : 'No',
            'memory_usage' => memory_get_usage(true),
            'disk_free_space' => disk_free_space('/'),
            'disk_total_space' => disk_total_space('/'),
        ];

        return view('components.superadmin.health', compact('health'));
    }

    /**
     * Show admin user management page
     */
    public function adminUsers(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get selected barangay from request
        $selectedBarangay = $request->input('barangay');

        // Build query for admin users
        $query = User::whereIn('role', ['admin', 'super_admin', 'chairman', 'officer']);

        // Apply barangay filter if selected
        if ($selectedBarangay) {
            $query->where('barangay', $selectedBarangay);
        }

        $adminUsers = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get barangays for the filter dropdown
        try {
            $barangays = Barangay::pluck('name')->toArray();
        } catch (\Exception $e) {
            \Log::warning('Failed to fetch barangays in adminUsers', ['error' => $e->getMessage()]);
            $barangays = [];
        }

        // If no barangays in database, use default list
        if (empty($barangays)) {
            $barangays = ['Lahug', 'Mabolo', 'Luz', 'Banilad', 'Guadalupe', 'Talamban'];
        }

        return view('components.superadmin.admin-users', compact('adminUsers', 'barangays', 'selectedBarangay'));
    }

    /**
     * Show create admin user form
     */
    public function createAdminUser()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get barangays for the dropdown in the create form
        try {
            $barangays = Barangay::pluck('name')->toArray();
        } catch (\Exception $e) {
            \Log::warning('Failed to fetch barangays in createAdminUser', ['error' => $e->getMessage()]);
            $barangays = [];
        }

        // If no barangays in database, use default list
        if (empty($barangays)) {
            $barangays = ['Lahug', 'Mabolo', 'Luz', 'Banilad', 'Guadalupe', 'Talamban'];
        }

        return view('components.superadmin.create-admin-user', compact('barangays'));
    }

    /**
     * Store new admin user
     */

    public function storeAdminUser(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }


        \Log::info('Admin user creation attempt', [
            'request_data' => $request->except(['password']),
            'user_id' => $user->id
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|in:Atty.,Engr.,Arch.,Dr.,Dir.',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'suffix' => 'nullable|string|in:Jr.,Sr.,II,III,IV,V',
            'position' => 'required|string|max:255',
            'barangay' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'role' => 'required|string|in:admin,super_admin,chairman,officer',
        ]);

        if ($validator->fails()) {
            \Log::warning('Admin user creation validation failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->except(['password'])
            ]);

            // Check if email already exists and provide helpful message
            if ($validator->errors()->has('email')) {
                $existingUser = User::where('email', $request->email)->first();
                if ($existingUser) {
                    $errorMessage = "The email '{$request->email}' is already registered to: " .
                                  ($existingUser->first_name ?? '') . ' ' . ($existingUser->last_name ?? '') .
                                  " (Role: " . ($existingUser->role ?? 'N/A') . ", Status: " . ($existingUser->status ?? 'N/A') . ")";

                    return redirect()->back()
                        ->with('error', $errorMessage)
                        ->withInput();
                }
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Generate temporary password
            $temporaryPassword = Str::random(12);

            // Create the admin user
            $newUser = User::create([
                'title' => $request->title,
                'first_name' => $request->first_name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'suffix' => $request->suffix,
                'position' => $request->position,
                'barangay' => $request->barangay,
                'email' => $request->email,
                'password' => Hash::make($temporaryPassword),
                'role' => $request->role,
                'status' => 'Active'
            ]);

            // Send registration email
            try {
                Mail::to($newUser->email)->send(new AdminRegistrationMail($newUser, $temporaryPassword));
                $emailStatus = 'sent';
            } catch (\Exception $e) {
                \Log::error('Failed to send admin registration email', [
                    'user_id' => $newUser->id,
                    'email' => $newUser->email,
                    'error' => $e->getMessage()
                ]);
                $emailStatus = 'failed';
            }

            // Log the action
            SystemLog::log('admin_user_created', $user, $newUser,
                "Admin user created: {$newUser->email} (Email: {$emailStatus})");

            $message = 'Admin user created successfully!';
            if ($emailStatus === 'failed') {
                $message .= ' However, the registration email could not be sent. Please provide the credentials manually.';
                $message .= " Temporary password: {$temporaryPassword}";
            }

            \Log::info('Admin user created, redirecting to admin users list', [
                'new_user_id' => $newUser->id,
                'new_user_email' => $newUser->email,
                'redirect_route' => 'superadmin.admin-users'
            ]);

            return redirect()->route('superadmin.admin-users')
                ->with('success', $message)
                ->with('new_user_created', true);

        } catch (\Exception $e) {
            \Log::error('Failed to create admin user', [
                'error' => $e->getMessage(),
                'request_data' => $request->except(['password'])
            ]);

            return redirect()->back()
                ->with('error', 'Failed to create admin user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Check if email is available for registration
     */
    public function checkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json([
                'available' => false,
                'user_info' => ($existingUser->first_name ?? '') . ' ' . ($existingUser->last_name ?? '') .
                              ' (' . ($existingUser->role ?? 'N/A') . ')'
            ]);
        }

        return response()->json([
            'available' => true
        ]);
    }

    /**
     * Deactivate admin user
     */
    public function deactivateAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);

        // Prevent self-deactivation
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot modify your own account status.');
        }

        // Only allow deactivating admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'chairman', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be deactivated.');
        }

        $adminUser->update([
            'status' => 'Inactive'
        ]);

        // Revoke all active sessions/tokens for the deactivated user
        try {
            // Revoke all Sanctum tokens
            $adminUser->tokens()->delete();
        } catch (\Exception $e) {
            \Log::warning('Failed to revoke tokens for deactivated user', [
                'user_id' => $adminUser->id,
                'error' => $e->getMessage()
            ]);
        }

        SystemLog::log('admin_user_deactivated', $user, $adminUser,
            "Admin user deactivated: {$adminUser->email}");

        return redirect()->back()
            ->with('success', "Admin user '{$adminUser->first_name} {$adminUser->last_name}' has been deactivated successfully. They will no longer be able to access the system.");
    }

    /**
     * Reactivate admin user
     */
    public function reactivateAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);
        
        // Prevent self-deactivation
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot modify your own account status.');
        }

        // Only allow reactivating admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'chairman', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be reactivated.');
        }

        $adminUser->update([
            'status' => 'Active',
            'is_active' => true
        ]);

        SystemLog::log('admin_user_reactivated', $user, $adminUser, "Admin user {$adminUser->email} reactivated");

        return redirect()->back()->with('success', 'Admin user reactivated successfully.');
    }

    public function deleteAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);
        
        // Prevent self-deletion
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot delete your own account.');
        }

        // Only allow deleting admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'chairman', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be deleted.');
        }

        // Log the deletion before actually deleting
        SystemLog::log('admin_user_deleted', $user, $adminUser, "Admin user {$adminUser->email} permanently deleted");

        // Permanently delete the user
        $adminUser->delete();

        return redirect()->back()->with('success', 'Admin user deleted successfully.');
    }
}
