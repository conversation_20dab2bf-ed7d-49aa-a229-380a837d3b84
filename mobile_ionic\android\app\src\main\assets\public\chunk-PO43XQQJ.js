import{b as c}from"./chunk-ZJ5IMUT4.js";import{b as p,f as a,g as h,j as m,k as b}from"./chunk-SGSBBWFA.js";import{e as l}from"./chunk-BAKMWPBW.js";import{g as o}from"./chunk-2R6CW7ES.js";var u=":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}",v=u,y=":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}",g=y,f="split-pane-main",w="split-pane-side",x={xs:"(min-width: 0px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",never:""},S=class{constructor(e){p(this,e),this.ionSplitPaneVisible=b(this,"ionSplitPaneVisible",7),this.visible=!1,this.contentId=void 0,this.disabled=!1,this.when=x.lg}visibleChanged(e){this.ionSplitPaneVisible.emit({visible:e})}isVisible(){return o(this,null,function*(){return Promise.resolve(this.visible)})}connectedCallback(){return o(this,null,function*(){typeof customElements<"u"&&customElements!=null&&(yield customElements.whenDefined("ion-split-pane")),this.styleMainElement(),this.updateState()})}disconnectedCallback(){this.rmL&&(this.rmL(),this.rmL=void 0)}updateState(){if(this.rmL&&(this.rmL(),this.rmL=void 0),this.disabled){this.visible=!1;return}let e=this.when;if(typeof e=="boolean"){this.visible=e;return}let n=x[e]||e;if(n.length===0){this.visible=!1;return}let i=s=>{this.visible=s.matches},t=window.matchMedia(n);t.addListener(i),this.rmL=()=>t.removeListener(i),this.visible=t.matches}styleMainElement(){let e=this.contentId,n=this.el.children,i=this.el.childElementCount,t=!1;for(let s=0;s<i;s++){let r=n[s],d=e!==void 0&&r.id===e;if(d)if(t){l("[ion-split-pane] - Cannot have more than one main node.");return}else I(r,d),t=!0}t||l("[ion-split-pane] - Does not have a specified main node.")}render(){let e=c(this);return a(h,{key:"098801b5a318e2fc6913fb0d9079b1552927b99b",class:{[e]:!0,[`split-pane-${e}`]:!0,"split-pane-visible":this.visible}},a("slot",{key:"8cbc6a942ecba54fc3c62027d46917db067b65c8"}))}get el(){return m(this)}static get watchers(){return{visible:["visibleChanged"],disabled:["updateState"],when:["updateState"]}}},I=(e,n)=>{let i,t;n?(i=f,t=w):(i=w,t=f);let s=e.classList;s.add(i),s.remove(t)};S.style={ios:v,md:g};export{S as ion_split_pane};
