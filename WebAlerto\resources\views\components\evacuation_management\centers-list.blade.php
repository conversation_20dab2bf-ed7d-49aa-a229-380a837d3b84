@extends('layout.app')

@section('title', $title)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $title }}</h1>
                    <p class="text-gray-600 mt-1">Manage evacuation centers</p>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        @if(auth()->user()->hasRole('super_admin'))
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8 mb-10 flex flex-col items-center">
            <div class="flex items-center gap-4">
                <div class="flex-1">
                    <select name="barangay" id="barangay" class="w-48 border-sky-200 rounded-lg shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90">
                        <option value="">All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                {{ $barangay }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse ($centers as $center)
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden transform transition-all duration-200 hover:scale-105 h-full flex flex-col justify-between" data-center-id="{{ $center->id }}">
                    <div class="p-6 flex-1 flex flex-col">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold text-gray-900">{{ $center->name }}</h3>
                            <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium
                                @if($center->status === 'Active')
                                    bg-green-100 text-green-800
                                @elseif($center->status === 'Under Maintenance')
                                    bg-yellow-100 text-yellow-800
                                @else
                                    bg-red-100 text-red-800
                                @endif
                            ">
                                <i class="fas
                                    @if($center->status === 'Active')
                                        fa-check-circle
                                    @elseif($center->status === 'Under Maintenance')
                                        fa-wrench
                                    @else
                                        fa-times-circle
                                    @endif
                                mr-1"></i>
                                {{ $center->status }}
                            </span>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <p class="text-gray-600">{{ $center->street_name }}, {{ $center->barangay }}, {{ $center->city }}, {{ $center->province }}</p>
                            </div>
                            
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-users"></i>
                                </div>
                                <p class="text-gray-600">Capacity: {{ $center->capacity }}</p>
                            </div>
                            
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <p class="text-gray-600">{{ $center->contact }}</p>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    @if(is_array($center->disaster_type))
                                        @foreach($center->disaster_type as $type)
                                            @php
                                                if (strpos($type, 'Others:') === 0) {
                                                    $badgeClass = 'bg-purple-100 text-purple-800';
                                                    $displayText = trim(str_replace('Others:', '', $type));
                                                } else {
                                                    $badgeClass = match($type) {
                                                        'Typhoon' => 'bg-green-100 text-green-800',
                                                        'Flood' => 'bg-blue-100 text-blue-800',
                                                        'Fire' => 'bg-red-100 text-red-800',
                                                        'Earthquake' => 'bg-orange-100 text-orange-800',
                                                        'Landslide' => 'bg-amber-100 text-amber-800',
                                                        default => 'bg-gray-100 text-gray-800',
                                                    };
                                                    $displayText = $type;
                                                }
                                            @endphp
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium {{ $badgeClass }}">
                                                {{ $displayText }}
                                            </span>
                                        @endforeach
                                    @else
                                        @php
                                            if (strpos($center->disaster_type, 'Others:') === 0) {
                                                $badgeClass = 'bg-purple-100 text-purple-800';
                                                $displayText = trim(str_replace('Others:', '', $center->disaster_type));
                                            } else {
                                                $badgeClass = match($center->disaster_type) {
                                                    'Typhoon' => 'bg-green-100 text-green-800',
                                                    'Flood' => 'bg-blue-100 text-blue-800',
                                                    'Fire' => 'bg-red-100 text-red-800',
                                                    'Earthquake' => 'bg-orange-100 text-orange-800',
                                                    'Landslide' => 'bg-amber-100 text-amber-800',
                                                    default => 'bg-gray-100 text-gray-800',
                                                };
                                                $displayText = $center->disaster_type;
                                            }
                                        @endphp
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium {{ $badgeClass }}">
                                            {{ $displayText }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col md:flex-row justify-center items-center mt-6 gap-2 w-full md:w-auto mx-auto mt-auto mb-4">
                        <button onclick="openViewModal({{ $center->id }})" 
                            class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105 w-full md:w-auto">
                            <i class="fas fa-eye text-sm"></i>
                            View
                        </button>
                        <a href="{{ route('components.evacuation_management.edit-evacuation-center', $center->id) }}" 
                            class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105 w-full md:w-auto">
                            <i class="fas fa-edit text-sm"></i>
                            Edit
                        </a>
                        <button onclick="openDeleteModal({{ $center->id }}, '{{ $center->name }}')" 
                            class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105 w-full md:w-auto">
                            <i class="fas fa-trash text-sm"></i>
                            Delete
                        </button>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <div class="p-4 bg-sky-100 rounded-full text-sky-500 mb-4">
                                <i class="fas fa-building text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">No Centers Found</h3>
                            <p class="text-gray-500">No evacuation centers match your criteria</p>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($centers->hasPages())
        <div class="mt-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 px-6 py-4">
                {{ $centers->links() }}
            </div>
        </div>
        @endif
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4 hidden overflow-y-auto">
    <div class="relative mx-auto w-full max-w-3xl bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-semibold text-white">Evacuation Center Details</h3>
                <button onclick="closeViewModal()" class="text-white hover:text-sky-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Content -->
        <div id="viewModalContent" class="p-6 max-h-[calc(100vh-150px)] overflow-y-auto">
            <!-- Content will be dynamically inserted here -->
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-6">
    <div class="relative mx-auto p-8 border w-[480px] shadow-lg rounded-2xl bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Delete Confirmation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-gray-600">
                    Are you sure you want to delete <span id="centerNameToDelete" class="font-medium"></span>?
                </p>
                <p class="text-gray-500 text-sm mt-1">
                    This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <form id="deleteForm" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white text-sm font-medium rounded-lg shadow-lg transition-all duration-200">
                        Delete
                    </button>
                </form>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg shadow-sm hover:bg-gray-200 transition-all duration-200">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Multi-disaster': '#6b7280'
};

function getDisasterTypeBadges(disasterTypes) {
    if (!disasterTypes) return '';
    const types = Array.isArray(disasterTypes) ? disasterTypes : JSON.parse(disasterTypes);
    return types.map(type => {
        let colorClass = 'bg-gray-100 text-gray-800';
        let displayText = type;
        if (type.startsWith('Others:')) {
            colorClass = 'bg-purple-100 text-purple-800';
            displayText = type.replace('Others:', '').trim();
        } else if (type === 'Others') {
             colorClass = 'bg-purple-100 text-purple-800';
        }
        else {
            switch(type) {
                case 'Typhoon': colorClass = 'bg-green-100 text-green-800'; break;
                case 'Flood': colorClass = 'bg-blue-100 text-blue-800'; break;
                case 'Fire': colorClass = 'bg-red-100 text-red-800'; break;
                case 'Earthquake': colorClass = 'bg-orange-100 text-orange-800'; break;
                case 'Landslide': colorClass = 'bg-amber-100 text-amber-800'; break;
            }
        }
        return `<span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium ${colorClass}">${displayText}</span>`;
    }).join('');
}

function openViewModal(id) {
    const viewModal = document.getElementById('viewModal');
    const modalContent = document.getElementById('viewModalContent');
    
    viewModal.classList.remove('hidden');
    
    modalContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
        </div>
    `;
    
    fetch(`/api/evacuation-centers/${id}`)
        .then(response => response.json())
        .then(data => {
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <div class="border-b border-sky-100 pb-4">
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Center Name</h4>
                        <p class="text-lg font-bold text-gray-900">${data.name}</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Capacity</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-users text-sky-500"></i>
                                <p class="text-gray-900">${data.capacity}</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Contact Info</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone text-sky-500"></i>
                                <p class="text-gray-900">${data.contact}</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Status</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium ${
                                data.status === 'Active' ? 'bg-green-100 text-green-800' : 
                                (data.status === 'Under Maintenance' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')
                            }">
                                <i class="fas ${
                                    data.status === 'Active' ? 'fa-check-circle' : 
                                    (data.status === 'Under Maintenance' ? 'fa-wrench' : 'fa-times-circle')
                                } mr-1"></i>
                                ${data.status}
                            </span>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Disaster Type</h4>
                            <div class="flex flex-wrap gap-2">
                                ${getDisasterTypeBadges(data.disaster_type)}
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Address</h4>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-map-marker-alt text-sky-500 mt-1"></i>
                            <p class="text-gray-900">${data.building_name ? data.building_name + ', ' : ''}${data.street_name}, ${data.barangay}, ${data.city}, ${data.province}, Philippines</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Location</h4>
                        <div id="viewMap" class="h-80 w-full rounded-xl border border-sky-200 shadow-sm bg-gray-50"></div>
                    </div>
                </div>
            `;

            if (data.latitude && data.longitude) {
                setTimeout(() => {
                    const map = L.map('viewMap').setView([data.latitude, data.longitude], 15);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19
                    }).addTo(map);
                    
                    let markerColor;
                    const types = Array.isArray(data.disaster_type) ? data.disaster_type : JSON.parse(data.disaster_type);

                    if (types.length > 1) {
                        markerColor = disasterTypeColors['Multi-disaster'];
                    } else if (types.length === 1) {
                        const type = types[0];
                        markerColor = disasterTypeColors[type.startsWith('Others:') ? 'Others' : type] || disasterTypeColors['Multi-disaster'];
                    } else {
                        markerColor = disasterTypeColors['Multi-disaster'];
                    }

                    const markerIcon = L.divIcon({
                        className: 'custom-marker',
                        html: `<div style="background-color: ${markerColor};" class="w-8 h-8 rounded-full border-2 border-white shadow-lg"></div>`,
                        iconSize: [32, 32],
                        iconAnchor: [16, 32]
                    });
                    
                    L.marker([data.latitude, data.longitude], { icon: markerIcon }).addTo(map);
                }, 100);
            }
        })
        .catch(error => {
            console.error('Error fetching center details:', error);
            modalContent.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-500">Failed to load details.</p>
                </div>
            `;
        });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const viewModal = document.getElementById('viewModal');
    const deleteModal = document.getElementById('deleteModal');
    if (event.target === viewModal) {
        closeViewModal();
    }
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const barangaySelect = document.getElementById('barangay');
    if (barangaySelect) {
        barangaySelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

document.getElementById('deleteForm').addEventListener('submit', function(e) {
    // Allow normal form submission to server
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
});

function openDeleteModal(id, name) {
    document.getElementById('centerNameToDelete').textContent = name;
    const form = document.getElementById('deleteForm');
    form.action = '/evacuation/' + id;
    form.dataset.id = id;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}
</script>
@endpush

@endsection
