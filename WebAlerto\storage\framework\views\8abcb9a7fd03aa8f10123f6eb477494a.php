<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Header Section -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12 mb-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-shield text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Admin User Management</h1>
                        <p class="text-gray-600 mt-1">Manage administrator accounts and permissions</p>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <a href="<?php echo e(route('superadmin.create-admin-user')); ?>" 
                       class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        Create Admin User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search Section -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12 mb-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
            <form action="<?php echo e(route('superadmin.admin-users')); ?>" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-1">Search Admin Users</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" placeholder="Search by name or email..." value="<?php echo e($searchQuery ?? ''); ?>"
                               class="w-full pl-10 pr-4 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                
                <div>
                    <label for="barangay" class="block text-sm font-semibold text-gray-700 mb-1">Filter by Barangay</label>
                    <select name="barangay" id="barangay" onchange="this.form.submit()" class="w-full pl-3 pr-10 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <option value="">All Barangays</option>
                        <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($barangay); ?>" <?php echo e(($selectedBarangay ?? '') == $barangay ? 'selected' : ''); ?>>
                                <?php echo e($barangay); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="md:col-span-3 flex justify-end items-center gap-2">
                    <button type="submit" class="inline-flex items-center justify-center gap-2 bg-sky-500 text-white px-4 py-2 rounded-xl font-semibold shadow-lg transition-all hover:bg-sky-600">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <?php if(request('search') || request('barangay')): ?>
                        <a href="<?php echo e(route('superadmin.admin-users')); ?>" class="inline-flex items-center justify-center px-4 py-2 bg-gray-500 text-white rounded-xl font-semibold shadow-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12 mb-6">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo e(session('success')); ?>

                    <?php if(session('new_user_created')): ?>
                        <span class="ml-2 text-sm font-medium">(Redirected from user creation)</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12 mb-6">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <?php echo e(session('error')); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Admin Users Table -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-users-cog text-white text-xl"></i>
                        <h2 class="text-xl font-bold text-white">Administrator Accounts</h2>
                    </div>
                    <div class="text-white text-sm">
                        <span class="font-medium">Total:</span> <?php echo e($adminUsers->total()); ?> admin users
                    </div>
                </div>
            </div>

            <!-- Table Container -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50/80">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-user text-sky-600"></i>
                                    Admin Details
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-briefcase text-sky-600"></i>
                                    Position & Role
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-map-marker-alt text-sky-600"></i>
                                    Location
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-circle text-sky-600"></i>
                                    Status
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-calendar text-sky-600"></i>
                                    Created
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-cogs text-sky-600"></i>
                                    Actions
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/60 divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $adminUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $adminUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-sky-50/80 transition-all duration-200 group">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                                            <span class="text-white font-bold text-lg">
                                                <?php echo e(strtoupper(substr($adminUser->first_name, 0, 1) . substr($adminUser->last_name, 0, 1))); ?>

                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-gray-900">
                                            <?php echo e($adminUser->title); ?> <?php echo e($adminUser->first_name); ?> <?php echo e($adminUser->middle_name); ?> <?php echo e($adminUser->last_name); ?> <?php echo e($adminUser->suffix); ?>

                                        </div>
                                        <div class="text-sm text-gray-600"><?php echo e($adminUser->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($adminUser->position); ?></div>
                                <div class="text-sm text-gray-600">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php echo e($adminUser->role === 'super_admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'); ?>">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $adminUser->role))); ?>

                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($adminUser->barangay); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php echo e($adminUser->status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                    <i class="fas <?php echo e($adminUser->status === 'Active' ? 'fa-check-circle' : 'fa-times-circle'); ?> text-xs mr-1"></i>
                                    <?php echo e($adminUser->status); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-600">
                                <?php echo e($adminUser->created_at->format('M d, Y')); ?>

                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-2">
                                    <?php if($adminUser->id !== auth()->id()): ?>
                                        <?php if($adminUser->status === 'Active'): ?>
                                            <form action="<?php echo e(route('superadmin.deactivate-admin-user', $adminUser->id)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" 
                                                        class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200"
                                                        onclick="return confirm('Are you sure you want to deactivate this admin user?')">
                                                    <i class="fas fa-pause mr-1"></i>
                                                    Deactivate
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form action="<?php echo e(route('superadmin.reactivate-admin-user', $adminUser->id)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" 
                                                        class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200">
                                                    <i class="fas fa-play mr-1"></i>
                                                    Reactivate
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <!-- Delete Button -->
                                        <form action="<?php echo e(route('superadmin.delete-admin-user', $adminUser->id)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200"
                                                    onclick="return confirmDelete('<?php echo e($adminUser->email); ?>')">
                                                <i class="fas fa-trash mr-1"></i>
                                                Delete
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <span class="text-xs text-gray-500 italic">Current User</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="text-gray-400 text-4xl mb-3">👥</div>
                                    <p class="text-gray-500 font-medium">No admin users found</p>
                                    <p class="text-gray-400 text-sm mt-1">Create your first admin user to get started</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($adminUsers->hasPages()): ?>
            <div class="bg-gray-50/80 px-6 py-4 border-t border-gray-200">
                <?php echo e($adminUsers->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function confirmDelete(email) {
    return confirm(`⚠️ WARNING: This action will permanently delete the admin user account:\n\nEmail: ${email}\n\nThis action will:\n• Permanently remove the user account\n• Free up the email address for reuse\n• Delete all associated data\n• This action CANNOT be undone\n\nAre you absolutely sure you want to proceed?`);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/components/superadmin/admin-users.blade.php ENDPATH**/ ?>