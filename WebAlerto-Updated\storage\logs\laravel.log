[2025-06-22 12:33:14] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:33:14] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:33:14] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:33:14] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:33:26] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:33:26] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:33:26] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:33:26] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:34:04] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:34:04] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:34:04] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:34:04] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:34:20] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:34:20] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:34:20] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:34:20] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:37:30] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:37:30] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:37:30] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:37:30] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:38:04] local.INFO: Registration attempt {"email":"<EMAIL>","has_password":true,"has_password_confirmation":true,"request_data":{"email":"<EMAIL>"}} 
[2025-06-22 12:38:05] local.INFO: Creating user {"email":"<EMAIL>","first_name":"test"} 
[2025-06-22 12:38:05] local.INFO: User created successfully {"user_id":15,"email":"<EMAIL>"} 
[2025-06-22 12:38:17] local.INFO: Login attempt received {"email":"<EMAIL>","has_password":true,"request_data":{"email":"<EMAIL>"}} 
[2025-06-22 12:38:18] local.INFO: Attempting authentication {"email":"<EMAIL>","password_length":11} 
[2025-06-22 12:38:50] local.INFO: Device token registered successfully {"token_hash":"f3814511098d8ea97691072dbd6da9809a3f3748a0b136fcef955a15eb899926","device_type":"android","project_id":null} 
[2025-06-22 12:39:05] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:39:05] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:39:05] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:39:05] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:39:06] local.INFO: No active device tokens found  
[2025-06-22 12:39:49] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:39:49] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:39:49] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:39:49] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:39:50] local.ERROR: FCM notification failure via direct HTTP {"token_hash":"f3814511098d8ea97691072dbd6da9809a3f3748a0b136fcef955a15eb899926","error":"The registration token is not a valid FCM registration token","http_code":400} 
[2025-06-22 12:39:50] local.WARNING: No notifications sent successfully {"notification_id":6,"failure_count":1,"invalid_tokens":0} 
[2025-06-22 12:47:50] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:47:50] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:47:50] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:47:50] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:52:27] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:52:27] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:52:27] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:52:27] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:53:39] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:53:39] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:53:39] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:53:40] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:54:06] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:54:06] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:54:06] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:54:06] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:54:14] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:54:14] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 12:54:14] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 12:54:14] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 12:59:01] local.INFO: Checking user role {"user_id":1,"user_role":"super_admin","required_roles":["super_admin"]} 
[2025-06-22 12:59:04] local.INFO: Checking user role {"user_id":1,"user_role":"super_admin","required_roles":["super_admin"]} 
[2025-06-22 12:59:18] local.INFO: Checking user role {"user_id":1,"user_role":"super_admin","required_roles":["super_admin"]} 
[2025-06-22 12:59:52] local.INFO: Checking user role {"user_id":1,"user_role":"super_admin","required_roles":["super_admin"]} 
[2025-06-22 12:59:52] local.INFO: Admin user creation attempt {"request_data":{"_token":"pPGcLCkqk9y4BWsYWz8RJS1xqIBRDVGN6qy5opfC","title":"Engr.","first_name":"Jason","middle_name":"B.","last_name":"Ejurango","suffix":null,"email":"<EMAIL>","position":"Director","barangay":"Ermita","role":"chairman"},"user_id":1} 
[2025-06-22 12:59:52] local.DEBUG: From: <EMAIL>
To: <EMAIL>
Reply-To: <EMAIL>
Subject: WebAlerto Admin Account - Access Granted
MIME-Version: 1.0
Date: Sun, 22 Jun 2025 12:59:52 +0000
Message-ID: <<EMAIL>>
Content-Type: multipart/alternative; boundary=-dFL6zC1

---dFL6zC1
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: quoted-printable

WebAlerto Admin Account - Access Granted

Dear Jason Ejurango,

Your administrator account for WebAlerto Emergency Management System has been created by the system administrator. You have been granted access to manage emergency alerts and evacuation resources for Ermita Barangay.

This is an official system notification from your organization's emergency management team.

LOGIN CREDENTIALS:
==================
Email: <EMAIL>
Temporary Password: jATSyPt7Wvab
Role: Chairman
Position: Director

IMPORTANT SECURITY NOTICE:
=========================
- This is a temporary password. Please change it immediately after your first login.
- Do not share these credentials with anyone.
- Always log out when finished using the system.

ACCESS YOUR ACCOUNT:
===================
Login URL: http://localhost/login

NEED HELP?
==========
If you have any questions or need assistance, please contact the system administrator.

System: WebAlerto Emergency Management
Environment: Development

---
This email was sent automatically by the WebAlerto system.
© 2025 WebAlerto Emergency Alert & Management System

---dFL6zC1
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to WebAlerto</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #0ea5e9;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 16px;
        }
        .content {
            margin-bottom: 30px;
        }
        .credentials-box {
            background-color: #f1f5f9;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .credential-item {
            margin: 10px 0;
            font-size: 16px;
        }
        .credential-label {
            font-weight: bold;
            color: #475569;
        }
        .credential-value {
            color: #1e293b;
            font-family: 'Courier New', monospace;
            background-color: white;
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #cbd5e1;
            display: inline-block;
            margin-left: 10px;
        }
        .login-button {
            display: inline-block;
            background-color: #0ea5e9;
            color: white !important;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #0ea5e9;
        }
        .login-button:hover {
            background-color: #0284c7;
            border-color: #0284c7;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-icon {
            color: #f59e0b;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚨 Alerto</div>
            <div class="subtitle">Emergency Alert & Management System</div>
        </div>

        <div class="content">
            <h2 style="color: #1e293b; margin-bottom: 20px;">WebAlerto Admin Account Setup</h2>

            <p>Dear <strong>Jason Ejurango</strong>,</p>

            <p>Your administrator account for WebAlerto Emergency Management System has been created by the system administrator. You have been granted access to manage emergency alerts and evacuation resources for Ermita Barangay.</p>

            <p style="color: #059669; font-weight: 500;">This is an official system notification from your organization's emergency management team.</p>

            <div class="credentials-box">
                <h3 style="margin-top: 0; color: #374151;">Your Login Credentials</h3>
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value"><EMAIL></span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Temporary Password:</span>
                    <span class="credential-value">jATSyPt7Wvab</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Role:</span>
                    <span class="credential-value">Chairman</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Position:</span>
                    <span class="credential-value">Director</span>
                </div>
            </div>

            <div class="warning">
                <p><span class="warning-icon">⚠️ Important Security Notice:</span></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>This is a temporary password. Please change it immediately after your first login.</li>
                    <li>Do not share these credentials with anyone.</li>
                    <li>Always log out when finished using the system.</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="http://localhost/login" class="login-button">Access Admin Dashboard</a>
            </div>

            <div class="contact-info">
                <h4 style="margin-top: 0; color: #374151;">Need Help?</h4>
                <p style="margin: 5px 0;">If you have any questions or need assistance, please contact the system administrator.</p>
                <p style="margin: 5px 0;"><strong>System:</strong> WebAlerto Emergency Management</p>
                <p style="margin: 5px 0;"><strong>Environment:</strong> Development</p>
            </div>
        </div>

        <div class="footer">
            <p>This email was sent automatically by the WebAlerto system.</p>
            <p>© 2025 WebAlerto Emergency Alert & Management System</p>
        </div>
    </div>
</body>
</html>

---dFL6zC1--
  
[2025-06-22 12:59:52] local.INFO: Admin user created, redirecting to admin users list {"new_user_id":2,"new_user_email":"<EMAIL>","redirect_route":"superadmin.admin-users"} 
[2025-06-22 12:59:53] local.INFO: Checking user role {"user_id":1,"user_role":"super_admin","required_roles":["super_admin"]} 
[2025-06-22 13:00:50] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:00:50] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:00:50] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 13:00:50] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 13:00:53] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:00:53] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:00:53] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 13:00:53] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 13:02:28] local.INFO: Updated token with new user association {"token_hash":"7fa13b6994cc26ef55a2f60c2268cf5790eaa5ca8f24dec38a6546cc50db5290","old_user_id":14,"new_user_id":null} 
[2025-06-22 13:02:51] local.INFO: Registration attempt {"email":"<EMAIL>","has_password":true,"has_password_confirmation":true,"request_data":{"email":"<EMAIL>"}} 
[2025-06-22 13:02:51] local.INFO: Creating user {"email":"<EMAIL>","first_name":"3"} 
[2025-06-22 13:02:51] local.INFO: User created successfully {"user_id":3,"email":"<EMAIL>"} 
[2025-06-22 13:02:53] local.INFO: Updated token with new user association {"token_hash":"7fa13b6994cc26ef55a2f60c2268cf5790eaa5ca8f24dec38a6546cc50db5290","old_user_id":null,"new_user_id":3} 
[2025-06-22 13:03:11] local.INFO: Login attempt received {"email":"<EMAIL>","has_password":true,"request_data":{"email":"<EMAIL>"}} 
[2025-06-22 13:03:11] local.INFO: Attempting authentication {"email":"<EMAIL>","password_length":8} 
[2025-06-22 13:03:52] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:03:52] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:03:52] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 13:03:52] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 13:03:52] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'barangay' cannot be null (Connection: mysql, SQL: insert into `notifications` (`title`, `category`, `message`, `severity`, `sent`, `barangay`, `user_id`, `updated_at`, `created_at`) values (1, typhoon, 1, high, 0, ?, 1, 2025-06-22 13:03:52, 2025-06-22 13:03:52)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'barangay' cannot be null (Connection: mysql, SQL: insert into `notifications` (`title`, `category`, `message`, `severity`, `sent`, `barangay`, `user_id`, `updated_at`, `created_at`) values (1, typhoon, 1, high, 0, ?, 1, 2025-06-22 13:03:52, 2025-06-22 13:03:52)) at C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `no...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `no...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `no...', Array, 'id')
#3 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `no...', Array, 'id')
#4 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1188}(Object(App\\Models\\Notification))
#10 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Notification), Object(Closure))
#11 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\app\\Http\\Controllers\\NotificationController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\NotificationController->store(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\NotificationController), 'store')
#17 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'barangay' cannot be null at C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->{closure:Illuminate\\Database\\MySqlConnection::insert():42}('insert into `no...', Array)
#2 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `no...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `no...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `no...', Array, 'id')
#5 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `no...', Array, 'id')
#6 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1188}(Object(App\\Models\\Notification))
#12 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Notification), Object(Closure))
#13 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\app\\Http\\Controllers\\NotificationController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\NotificationController->store(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\NotificationController), 'store')
#19 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#68 {main}
"} 
[2025-06-22 13:11:21] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:11:21] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\WebAlerto\\storage\\certs/cacert.pem"} 
[2025-06-22 13:11:21] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 13:11:21] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 13:37:24] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-22 13:37:24] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-22 13:37:24] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-22 13:37:24] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-22 13:37:35] local.INFO: Updated token with new user association {"token_hash":"3b2e5be5bb5adce330b51f462c96edf1dd49363cf71ab3b63dcfe1f7d10516d6","old_user_id":3,"new_user_id":null} 
[2025-06-22 13:37:45] local.INFO: Login attempt received {"email":"<EMAIL>","has_password":true,"request_data":{"email":"<EMAIL>"}} 
[2025-06-22 13:37:45] local.INFO: Attempting authentication {"email":"<EMAIL>","password_length":8} 
[2025-06-22 13:37:47] local.INFO: Updated token with new user association {"token_hash":"3b2e5be5bb5adce330b51f462c96edf1dd49363cf71ab3b63dcfe1f7d10516d6","old_user_id":null,"new_user_id":3} 
[2025-06-25 14:00:59] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 14:00:59] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 14:00:59] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-25 14:00:59] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-25 14:23:48] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 14:23:48] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 14:23:48] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-25 14:23:48] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
[2025-06-25 15:01:42] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 15:01:42] local.INFO: Set CA certificate path for cURL and SSL context {"path":"C:\\Users\\<USER>\\junrelCAPSTONE\\Capstone\\WebAlerto-Updated\\storage\\certs/cacert.pem"} 
[2025-06-25 15:01:42] local.WARNING: Disabling SSL verification for FCM - this is not recommended for production  
[2025-06-25 15:01:42] local.INFO: Firebase Messaging initialized successfully {"project_id":"last-5acaf"} 
