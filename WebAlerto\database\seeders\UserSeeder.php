<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('users')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create System Administrator (only account for initial setup)
        User::create([
            'email' => env('SUPER_ADMIN_EMAIL', '<EMAIL>'),
            'password' => Hash::make(env('SUPER_ADMIN_PASSWORD', 'Admin@2025')),

            'title' => null,

            'first_name' => 'System',
            'middle_name' => null,
            'last_name' => 'Administrator',
            'suffix' => null,
            'position' => 'System Administrator',
            'barangay' => 'City Hall',
            'role' => 'super_admin',
            'status' => 'Active'
        ]);

        // Note: All other admin accounts must be created through the System Administrator registration form
        // This ensures no hardcoded credentials and proper invitation flow
        // 
        // Role-based access:
        // - super_admin: System Administrator (full access)
        // - admin: City Hall admin (city-wide monitoring, access to all barangays)
        // - chairman: BDRRMO Chairman (barangay-specific access)
        // - officer: BDRRMO Officer (barangay-specific access)
        // - assistant: BDRRMO Assistant (barangay-specific access)
    }
}
