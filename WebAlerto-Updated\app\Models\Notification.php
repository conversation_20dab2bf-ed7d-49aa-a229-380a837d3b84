<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notification extends Model
{
    use HasFactory;

    //
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'category',
        'barangay',
        'affected_areas',
        'severity',
        'sent',
        'created_at'
    ];
    protected $casts = [
        'created_at' => 'datetime',
        'sent' => 'boolean',
        'affected_areas' => 'array'
    ];

    protected $attributes = [
        'barangay' => 'All Areas', // Default value for barangay
    ];

    // Relationship with the user who created the notification
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scope to filter by barangay
    public function scopeFromBarangay($query, $barangay)
    {
        return $query->where('barangay', $barangay);
    }

    // Scope to get recent notifications
    public function scopeRecent($query, $limit = 3)
    {
        return $query->orderBy('created_at', 'desc')->limit($limit);
    }
}
