<?php $__env->startSection('content'); ?>

<?php
    $loggedInUser = auth()->user();
    $loggedInUserRole = $loggedInUser ? ($loggedInUser->role ?? 'No Role Attribute') : 'Not Logged In';
?>



<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class=" flex-cflexol lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Mobile User Management</h1>
                        <p class="text-gray-600 mt-1">Manage and monitor mobile app users</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <?php if(auth()->user()->hasRole('super_admin')): ?>
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <form action="<?php echo e(route('components.user-management')); ?>" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-1">Search Mobile Users</label>
                    <div class="relative">
                        <input type="text" name="search" id="search" placeholder="Search by name, mobile, or address..." value="<?php echo e($searchQuery ?? ''); ?>"
                               class="w-full pl-10 pr-4 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                
                <div>
                    <label for="barangay" class="block text-sm font-semibold text-gray-700 mb-1">Filter by Barangay</label>
                    <select name="barangay" id="barangay" onchange="this.form.submit()" class="w-full pl-10 pr-4 py-3 border-2 border-sky-200 rounded-xl focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm">
                        <option value="">All Barangays</option>
                        <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($barangay); ?>" <?php echo e($selectedBarangay == $barangay ? 'selected' : ''); ?>>
                                <?php echo e($barangay); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="md:col-span-3 flex justify-end items-center gap-2">
                    <button type="submit" class="inline-flex items-center justify-center gap-2 bg-sky-500 text-white px-4 py-2 rounded-xl font-semibold shadow-lg transition-all hover:bg-sky-600">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <?php if(request('search') || request('barangay')): ?>
                        <a href="<?php echo e(route('components.user-management')); ?>" class="inline-flex items-center justify-center px-4 py-2 bg-gray-500 text-white rounded-xl font-semibold shadow-lg transition-all hover:bg-gray-600">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
        <?php endif; ?>


        <!-- Mobile User Table -->

        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID no.</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Full Name</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Mobile Number</th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Age & Gender</th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Barangay</th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="userTableBody">
                        <?php $__empty_1 = true; $__currentLoopData = $mobileUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mobileUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo e($mobileUser->id); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
        <div class="flex-shrink-0 h-10 w-10 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
            <?php echo e($mobileUser->full_name ? strtoupper(substr($mobileUser->full_name, 0, 1)) : '?'); ?>

        </div>
        <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">
                <?php echo e($mobileUser->full_name); ?>

            </div>
            <div class="text-sm text-gray-500">
                <?php echo e(Str::limit($mobileUser->address, 30)); ?>

            </div>
        </div>
    </div>
</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600"><?php echo e($mobileUser->mobile_number); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                    <div class="flex flex-col">
                                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-sky-100 text-sky-800 mb-1">
                                            <?php echo e($mobileUser->age); ?> years old
                                        </span>
                                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                            <?php echo e($mobileUser->gender); ?>

                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-600"><?php echo e($mobileUser->barangay); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                        <?php echo e(strtolower($mobileUser->status) === 'active'
                                            ? 'bg-emerald-100 text-emerald-800'
                                            : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo e($mobileUser->status); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                    <div class="flex justify-center space-x-2">
                                        <button onclick="showMobileUserDetails(<?php echo e($mobileUser->id); ?>, '<?php echo e($mobileUser->full_name); ?>', '<?php echo e($mobileUser->mobile_number); ?>', '<?php echo e($mobileUser->age); ?>', '<?php echo e($mobileUser->gender); ?>', '<?php echo e($mobileUser->barangay); ?>', '<?php echo e($mobileUser->created_at); ?>', '<?php echo e($mobileUser->status); ?>', '<?php echo e($mobileUser->address); ?>')"
                                                class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg text-xs font-semibold shadow-sm transition-all duration-200">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </button>
                                        <a href="<?php echo e(route('components.user-management.edit', $mobileUser->id)); ?>"
                                           class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white rounded-lg text-xs font-semibold shadow-sm transition-all duration-200">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </a>
                                        <button onclick="openMobileUserDeleteModal(<?php echo e($mobileUser->id); ?>, '<?php echo e($mobileUser->full_name); ?>', '<?php echo e($mobileUser->barangay); ?>')"
                                                class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-lg text-xs font-semibold shadow-sm transition-all duration-200">
                                            <i class="fas fa-trash-alt mr-1"></i> Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">No mobile users found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($mobileUsers->hasPages()): ?>
        <div class="mt-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 px-6 py-4">
                <?php echo e($mobileUsers->links()); ?>

            </div>
        </div>
        <?php endif; ?>
        </div>
    </div>
</div>

<!-- Mobile User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-lg mx-4 border border-gray-200 max-h-[90vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-5 rounded-t-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-white/20 backdrop-blur-sm rounded-xl shadow-lg">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold text-white">Mobile User Profile</h3>
                        <p class="text-sky-100 text-sm mt-1">Complete mobile user information</p>
                    </div>
                </div>
                <button onclick="closeUserDetailsModal()" class="text-white/80 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-y-auto">
            <div id="userDetailsContent" class="space-y-4"></div>

            <!-- Only show Deactivate Form if Chairman -->
            <?php if(auth()->check() && (auth()->user()->role === 'super_admin' || auth()->user()->role === 'chairman')): ?>
    <div id="deactivateButtonContainer"></div>
<?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4 border border-gray-200">
        <div class="p-6">
            <div class="flex items-center gap-4 mb-4">
                <div class="p-3 bg-red-100 rounded-full">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete Mobile User</h3>
                    <p class="text-gray-600 text-sm">This action cannot be undone.</p>
                </div>
            </div>
            <p class="text-gray-700 mb-6">
                Are you sure you want to delete <span id="deleteUserName" class="font-semibold"></span> from <span id="deleteUserBarangay" class="font-semibold"></span>?
            </p>
            <form id="deleteForm" method="POST" class="flex justify-end gap-3">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="button" onclick="closeDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors">
                    <i class="fas fa-trash-alt mr-2"></i>Delete
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Register User Modal (SuperAdmin only) -->
<?php if(auth()->user()->hasRole('super_admin')): ?>
<div id="registerUserModal" class="fixed inset-0 bg-gradient-to-br from-sky-50 to-blue-50 flex items-center justify-center z-50 p-4">
    <div class="max-w-md w-full space-y-8 bg-white p-10 rounded-xl custom-shadow hover-shadow border border-sky-100 relative">
        <button id="closeRegisterUserModal" class="absolute top-4 right-4 text-sky-400 hover:text-sky-600 transition-colors p-2">
            <i class="fas fa-times text-xl"></i>
        </button>
        <div class="text-center">
            <img src="<?php echo e(asset('image/ALERTO Logo.png')); ?>" alt="WebAlerto Logo" class="w-24 h-24 mx-auto mb-4 object-contain">
            <h2 class="text-2xl md:text-3xl font-extrabold text-sky-600 leading-tight">Register New User</h2>
            <p class="mt-2 text-sm text-sky-600">Invite a new admin or barangay user</p>
        </div>
        <form id="registerUserForm" class="mt-8 space-y-6">
            <?php echo csrf_field(); ?>
            <div class="space-y-4">
                <div>
                    <label for="reg_email" class="block text-sm font-medium text-sky-700">Email Address</label>
                    <div class="mt-1">
                        <input type="email" name="email" id="reg_email" required autocomplete="off"
                            class="appearance-none block w-full px-3 py-2.5 border border-sky-200 rounded-lg shadow-sm placeholder-sky-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition duration-150 ease-in-out bg-sky-50 text-sky-900"
                            placeholder="Enter email address">
                    </div>
                </div>
                <div>
                    <label for="reg_barangay" class="block text-sm font-medium text-sky-700">Barangay Assignment</label>
                    <div class="mt-1">
                        <select name="barangay" id="reg_barangay" required
                            class="block w-full px-3 py-2.5 border border-sky-200 rounded-lg shadow-sm bg-sky-50 text-sky-900 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition duration-150 ease-in-out">
                            <option value="">Select Barangay</option>
                            <option value="City Hall">City Hall</option>
                            <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($barangay); ?>"><?php echo e($barangay); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>
                <div>
                    <label for="reg_role" class="block text-sm font-medium text-sky-700">Role</label>
                    <div class="mt-1">
                        <select name="role" id="reg_role" required
                            class="block w-full px-3 py-2.5 border border-sky-200 rounded-lg shadow-sm bg-sky-50 text-sky-900 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition duration-150 ease-in-out">
                            <option value="">Select Role</option>
                            <option value="admin">City DRRMO Director (City-wide monitoring)</option>
                            <option value="chairman">BDRRMO Chairman (Barangay-specific)</option>
                            <option value="officer">BDRRMO Officer (Barangay-specific)</option>
                            <option value="assistant">BDRRMO Assistant (Barangay-specific)</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="flex justify-end gap-3 pt-4">
                <button type="button" id="cancelRegisterUser" class="px-4 py-2 border border-sky-200 rounded-lg text-sky-700 bg-white hover:bg-sky-50 transition-colors">Cancel</button>
                <button type="submit" class="px-6 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg font-semibold shadow transition-all duration-200 w-full">
                    <i class="fas fa-user-plus mr-2"></i>
                    Register User
                </button>
            </div>
        </form>
    </div>
    <style>
        .custom-shadow {
            box-shadow: 0 20px 25px -5px rgba(14, 165, 233, 0.1), 0 10px 10px -5px rgba(14, 165, 233, 0.04);
        }
        .hover-shadow {
            transition: all 0.3s ease;
        }
        .hover-shadow:hover {
            box-shadow: 0 25px 30px -5px rgba(14, 165, 233, 0.2), 0 15px 15px -5px rgba(14, 165, 233, 0.1);
        }
    </style>
</div>
<?php endif; ?>

<!-- Success and Error Flash Messages -->
<?php if(session('success')): ?>
    <div id="successAlert" class="fixed top-5 right-5 bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-4 rounded-xl shadow-lg z-50 flex items-center gap-3">
        <i class="fas fa-check-circle text-xl"></i>
        <?php echo e(session('success')); ?>

    </div>
    <script>
        setTimeout(function() {
            const successAlert = document.getElementById('successAlert');
            if (successAlert) {
                successAlert.style.opacity = '0';
                successAlert.style.transition = 'opacity 0.5s ease-in-out';
                setTimeout(() => successAlert.remove(), 500);
            }
        }, 3000);
    </script>
<?php endif; ?>

<?php if(session('error')): ?>
    <div id="errorAlert" class="fixed top-5 right-5 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-4 rounded-xl shadow-lg z-50 flex items-center gap-3">
        <i class="fas fa-exclamation-circle text-xl"></i>
        <?php echo e(session('error')); ?>

    </div>
    <script>
        setTimeout(function() {
            const errorAlert = document.getElementById('errorAlert');
            if (errorAlert) {
                errorAlert.style.opacity = '0';
                errorAlert.style.transition = 'opacity 0.5s ease-in-out';
                setTimeout(() => errorAlert.remove(), 500);
            }
        }, 3000);
    </script>
<?php endif; ?>

<!-- Scripts -->
<script>
// Set JavaScript variable based on authenticated user's role
const userRole = <?php echo json_encode(auth()->check() ? auth()->user()->role : null, 15, 512) ?>;

// Live Search (local, no reload)
document.addEventListener('DOMContentLoaded', function () {
    const searchInput = document.getElementById('searchInput');
    const tableRows = document.querySelectorAll('#userTableBody tr');

    searchInput.addEventListener('input', function () {
        const query = this.value.toLowerCase().trim();
        tableRows.forEach(row => {
            const fullNameCell = row.querySelector('td:nth-child(2)');
            const mobileNumberCell = row.querySelector('td:nth-child(3)');
            const fullName = fullNameCell ? fullNameCell.textContent.toLowerCase() : '';
            const mobileNumber = mobileNumberCell ? mobileNumberCell.textContent.toLowerCase() : '';
            if (fullName.includes(query) || mobileNumber.includes(query)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
});

// Show Mobile User Details
function showMobileUserDetails(id, fullName, mobileNumber, age, gender, barangay, createdAt, status, address) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');
    const date = new Date(createdAt);
    const formattedDate = date.toLocaleString();

    content.innerHTML = `
        <div class="space-y-4">
            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-user text-sky-600 mr-2"></i>Full Name
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${fullName}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-mobile-alt text-sky-600 mr-2"></i>Mobile Number
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${mobileNumber}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-birthday-cake text-sky-600 mr-2"></i>Age & Gender
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">
                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-sky-100 text-sky-800 mr-2">
                        ${age} years old
                    </span>
                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                        ${gender}
                    </span>
                </div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-map-marker-alt text-sky-600 mr-2"></i>Address
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${address}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-map text-sky-600 mr-2"></i>Barangay
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${barangay}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-clock text-sky-600 mr-2"></i>Registered At
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3 text-gray-900">${formattedDate}</div>
            </div>

            <div class="flex flex-col">
                <label class="text-sm font-semibold text-gray-600 mb-1">
                    <i class="fas fa-toggle-on text-sky-600 mr-2"></i>Status
                </label>
                <div class="bg-gray-50 border-2 border-sky-200 rounded-xl p-3">
                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${status === 'Active' 
                            ? 'bg-emerald-100 text-emerald-800' 
                            : 'bg-red-100 text-red-800'}">
                        ${status}
                    </span>
                </div>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

// Close User Details Modal
function closeUserDetailsModal() {
    const modal = document.getElementById('userDetailsModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}

// Open Delete Modal
function openMobileUserDeleteModal(id, fullName, barangay) {
    const modal = document.getElementById('deleteModal');
    const form = document.getElementById('deleteForm');
    const nameSpan = document.getElementById('deleteUserName');
    const barangaySpan = document.getElementById('deleteUserBarangay');

    form.action = `/user-management/${id}`;
    nameSpan.textContent = fullName;
    barangaySpan.textContent = barangay;

    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

// Close Delete Modal
function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const userDetailsModal = document.getElementById('userDetailsModal');
    const deleteModal = document.getElementById('deleteModal');
    
    if (event.target === userDetailsModal) {
        closeUserDetailsModal();
    }
    
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
});

document.addEventListener('DOMContentLoaded', function () {
    // Modal open/close logic
    const openBtn = document.getElementById('openRegisterUserModal');
    const modal = document.getElementById('registerUserModal');
    const closeBtn = document.getElementById('closeRegisterUserModal');
    const cancelBtn = document.getElementById('cancelRegisterUser');
    if (openBtn && modal && closeBtn && cancelBtn) {
        openBtn.addEventListener('click', () => modal.classList.remove('hidden'));
        closeBtn.addEventListener('click', () => modal.classList.add('hidden'));
        cancelBtn.addEventListener('click', () => modal.classList.add('hidden'));
    }
    
    // Form submission
    const registerForm = document.getElementById('registerUserForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Registering...';
            submitBtn.disabled = true;

            fetch('<?php echo e(route("user-management.register")); ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'User Registered Successfully!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        // Reset form and hide modal
                        registerForm.reset();
                        modal.classList.add('hidden');
                        // Reload the page to show the new user
                        window.location.reload();
                    });
                } else {
                    // Show error message with validation details if available
                    let errorMsg = data.message || 'An error occurred during registration.';
                    if (data.errors) {
                        // If errors is an object, join all messages
                        errorMsg += '\n' + Object.values(data.errors).flat().join('\n');
                    }
                    Swal.fire({
                        icon: 'error',
                        title: 'Registration Failed',
                        text: errorMsg,
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Registration Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonColor: '#ef4444'
                });
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/components/user_management/user-management.blade.php ENDPATH**/ ?>