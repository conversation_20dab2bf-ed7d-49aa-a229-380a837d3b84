<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable\splash.png" qualifiers="" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-land-hdpi\splash.png" qualifiers="land-hdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-land-mdpi\splash.png" qualifiers="land-mdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-land-xhdpi\splash.png" qualifiers="land-xhdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-land-xxhdpi\splash.png" qualifiers="land-xxhdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-land-xxxhdpi\splash.png" qualifiers="land-xxxhdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-port-hdpi\splash.png" qualifiers="port-hdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-port-mdpi\splash.png" qualifiers="port-mdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-port-xhdpi\splash.png" qualifiers="port-xhdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-port-xxhdpi\splash.png" qualifiers="port-xxhdpi-v4" type="drawable"/><file name="splash" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-port-xxxhdpi\splash.png" qualifiers="port-xxxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Alerto</string><string name="title_activity_main">Alerto</string><string name="package_name">io.ionic.starter</string><string name="custom_url_scheme">io.ionic.starter</string></file><file path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
    </style><style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
    </style></file><file name="config" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\xml\config.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="alerto_launcher" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-hdpi\alerto_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="alerto_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-hdpi\alerto_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="alerto_launcher_round" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-hdpi\alerto_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="alerto_launcher" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-mdpi\alerto_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="alerto_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-mdpi\alerto_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="alerto_launcher_round" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-mdpi\alerto_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="alerto_launcher" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xhdpi\alerto_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="alerto_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xhdpi\alerto_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="alerto_launcher_round" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xhdpi\alerto_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="alerto_launcher" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxhdpi\alerto_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="alerto_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxhdpi\alerto_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="alerto_launcher_round" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxhdpi\alerto_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="alerto_launcher" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxxhdpi\alerto_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="alerto_launcher_foreground" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxxhdpi\alerto_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="alerto_launcher_round" path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\main\res\mipmap-xxxhdpi\alerto_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\junrelCAPSTONE\Capstone\mobile_ionic\android\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">660101685392</string><string name="google_api_key" translatable="false">AIzaSyA5H6_NGbhDlVZ4l67qEC_JNRmcXPQ-GAo</string><string name="google_app_id" translatable="false">1:660101685392:android:c7c81cb0ccca4f30cb7815</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyA5H6_NGbhDlVZ4l67qEC_JNRmcXPQ-GAo</string><string name="google_storage_bucket" translatable="false">last-5acaf.firebasestorage.app</string><string name="project_id" translatable="false">last-5acaf</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>