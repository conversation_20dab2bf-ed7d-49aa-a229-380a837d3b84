import{b as v}from"./chunk-I4SN7ED3.js";import{a as k}from"./chunk-FSE75POS.js";import{B as o,C as P,F as p,G as c,K as i,Kb as T,L as e,M as l,P as _,Q as C,R as g,Vb as w,Y as a,Z as d,_ as O,da as y,hc as N,m as M,oa as I,qa as S,r as f,s as x,v as m,xb as b}from"./chunk-N4Y2QYSK.js";import{g as h}from"./chunk-2R6CW7ES.js";function E(n,u){if(n&1&&(i(0,"div",11)(1,"span",12),a(2,"ETA"),e(),i(3,"span",13),a(4),e()()),n&2){let t=g(2);o(4),d(t.formatETA())}}function R(n,u){if(n&1&&(i(0,"div",26),a(1),e()),n&2){let t=g(3);o(),O(" in ",t.formatDistance(t.currentInstruction.distance)," ")}}function D(n,u){if(n&1&&(i(0,"div",19)(1,"div",20)(2,"div",21),l(3,"ion-icon",22),e(),i(4,"div",23)(5,"div",24),a(6),e(),p(7,R,2,1,"div",25),e()()()),n&2){let t=g(2);o(3),c("name",t.getManeuverIcon(t.currentInstruction.maneuver)),o(3),d(t.currentInstruction.instruction),o(),c("ngIf",t.currentInstruction.distance)}}function z(n,u){if(n&1){let t=_();i(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",6),l(4,"ion-icon",7),i(5,"span"),a(6),e()(),i(7,"div",8),l(8,"ion-icon",9),e()(),i(9,"div",10)(10,"div",11)(11,"span",12),a(12,"Distance"),e(),i(13,"span",13),a(14),e()(),i(15,"div",11)(16,"span",12),a(17,"Time"),e(),i(18,"span",13),a(19),e()(),p(20,E,5,1,"div",14),e()(),p(21,D,8,3,"div",15),i(22,"div",16)(23,"ion-button",17),C("click",function(){f(t);let s=g();return x(s.stopNavigation())}),l(24,"ion-icon",18),a(25," Stop Navigation "),e()()()}if(n&2){let t=g();o(6),d(t.destination.name||"Destination"),o(2),c("name",t.getTravelModeIcon()),o(6),d(t.formatDistance(t.remainingDistance)),o(5),d(t.formatDuration(t.remainingTime)),o(),c("ngIf",t.eta),o(),c("ngIf",t.currentInstruction)}}function U(n,u){if(n&1){let t=_();i(0,"div",27)(1,"ion-button",28),C("click",function(){f(t);let s=g();return x(s.startNavigation())}),l(2,"ion-icon",29),a(3," Start Navigation "),e()()}}function A(n,u){n&1&&(i(0,"div",30),l(1,"ion-spinner",31),i(2,"p"),a(3,"Calculating route..."),e()())}var K=(()=>{class n{constructor(t){this.routingService=t,this.travelMode="foot-walking",this.autoStart=!1,this.routeUpdated=new m,this.navigationStarted=new m,this.navigationStopped=new m,this.instructionChanged=new m,this.isNavigating=!1,this.currentRoute=null,this.currentInstruction=null,this.instructions=[],this.userPosition=null,this.eta=null,this.remainingDistance=0,this.remainingTime=0,this.positionWatcher=null}ngOnInit(){this.autoStart&&this.destination&&this.startNavigation()}ngOnDestroy(){this.stopNavigation()}startNavigation(){return h(this,null,function*(){if(!this.destination){console.error("No destination provided for navigation");return}console.log("\u{1F9ED} Starting real-time navigation to:",this.destination),this.isNavigating=!0,this.navigationStarted.emit();try{let t=yield v.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4});this.userPosition={lat:t.coords.latitude,lng:t.coords.longitude},this.startPositionTracking(),this.routingService.startRealTimeRouting(this.destination,this.travelMode,r=>this.onRouteUpdated(r),15e3),this.routingService.updateUserPosition(this.userPosition.lat,this.userPosition.lng)}catch(t){console.error("\u274C Failed to start navigation:",t),this.stopNavigation()}})}stopNavigation(){console.log("\u23F9\uFE0F Stopping real-time navigation"),this.isNavigating=!1,this.currentRoute=null,this.currentInstruction=null,this.instructions=[],this.eta=null,this.remainingDistance=0,this.remainingTime=0,this.positionWatcher&&(v.clearWatch({id:this.positionWatcher}),this.positionWatcher=null),this.routingService.stopRealTimeRouting(),this.navigationStopped.emit()}startPositionTracking(){return h(this,null,function*(){try{this.positionWatcher=yield v.watchPosition({enableHighAccuracy:!0,timeout:1e4,maximumAge:5e3},t=>{t&&(this.userPosition={lat:t.coords.latitude,lng:t.coords.longitude},this.routingService.updateUserPosition(this.userPosition.lat,this.userPosition.lng),this.updateCurrentInstruction())})}catch(t){console.error("\u274C Failed to start position tracking:",t)}})}onRouteUpdated(t){console.log("\u{1F504} Route updated in navigation component"),this.currentRoute=t,this.instructions=this.routingService.getNavigationInstructions(t),this.eta=this.routingService.getETA(t),this.remainingDistance=t.distance,this.remainingTime=t.duration,this.updateCurrentInstruction(),this.routeUpdated.emit(t)}updateCurrentInstruction(){if(this.userPosition&&this.instructions.length>0){let t=this.routingService.getCurrentInstruction(this.userPosition.lat,this.userPosition.lng,this.instructions);t&&t.id!==this.currentInstruction?.id&&(this.currentInstruction=t,this.instructionChanged.emit(t),console.log("\u{1F4CD} Navigation instruction updated:",t.instruction))}}formatDistance(t){return this.routingService.formatDistance(t)}formatDuration(t){return this.routingService.formatDuration(t)}formatETA(){return this.eta?this.eta.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):""}getManeuverIcon(t){return{straight:"arrow-up","turn-left":"arrow-back","turn-right":"arrow-forward","u-turn":"return-up-back",roundabout:"refresh-circle",exit:"exit",merge:"git-merge"}[t]||"arrow-up"}getTravelModeIcon(){return{"foot-walking":"walk","cycling-regular":"bicycle","driving-car":"car"}[this.travelMode]||"walk"}static{this.\u0275fac=function(r){return new(r||n)(P(k))}}static{this.\u0275cmp=M({type:n,selectors:[["app-real-time-navigation"]],inputs:{destination:"destination",travelMode:"travelMode",autoStart:"autoStart"},outputs:{routeUpdated:"routeUpdated",navigationStarted:"navigationStarted",navigationStopped:"navigationStopped",instructionChanged:"instructionChanged"},standalone:!0,features:[y],decls:3,vars:3,consts:[["class","navigation-container",4,"ngIf"],["class","start-navigation",4,"ngIf"],["class","loading-navigation",4,"ngIf"],[1,"navigation-container"],[1,"navigation-header"],[1,"route-info"],[1,"destination"],["name","location","color","primary"],[1,"travel-mode"],["color","medium",3,"name"],[1,"route-stats"],[1,"stat"],[1,"label"],[1,"value"],["class","stat",4,"ngIf"],["class","current-instruction",4,"ngIf"],[1,"navigation-controls"],["fill","clear","color","danger","size","small",3,"click"],["name","stop-circle","slot","start"],[1,"current-instruction"],[1,"instruction-content"],[1,"maneuver-icon"],["color","primary","size","large",3,"name"],[1,"instruction-text"],[1,"instruction"],["class","distance",4,"ngIf"],[1,"distance"],[1,"start-navigation"],["expand","block","color","primary",3,"click"],["name","navigate","slot","start"],[1,"loading-navigation"],["name","crescent","color","primary"]],template:function(r,s){r&1&&p(0,z,26,6,"div",0)(1,U,4,0,"div",1)(2,A,4,0,"div",2),r&2&&(c("ngIf",s.isNavigating),o(),c("ngIf",!s.isNavigating&&s.destination),o(),c("ngIf",s.isNavigating&&!s.currentRoute))},dependencies:[S,I,N,b,T,w],styles:['.navigation-container[_ngcontent-%COMP%]{background:var(--ion-color-light);border-radius:12px;padding:16px;margin:10px;box-shadow:0 4px 12px #0000001a;border:1px solid var(--ion-color-medium-tint)}.navigation-header[_ngcontent-%COMP%]{margin-bottom:16px}.route-info[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:12px}.destination[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:600;color:var(--ion-color-dark)}.destination[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:18px}.travel-mode[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.route-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;background:var(--ion-color-primary-tint);border-radius:8px;padding:12px}.stat[_ngcontent-%COMP%]{text-align:center;flex:1}.stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{display:block;font-size:12px;color:var(--ion-color-primary-shade);font-weight:500;margin-bottom:4px}.stat[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{display:block;font-size:16px;font-weight:700;color:var(--ion-color-primary)}.current-instruction[_ngcontent-%COMP%]{background:var(--ion-color-primary);color:#fff;border-radius:8px;padding:16px;margin-bottom:16px}.instruction-content[_ngcontent-%COMP%]{display:flex;align-items:center}.maneuver-icon[_ngcontent-%COMP%]{margin-right:16px}.maneuver-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:32px}.instruction-text[_ngcontent-%COMP%]{flex:1}.instruction-text[_ngcontent-%COMP%]   .instruction[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin-bottom:4px;line-height:1.3}.instruction-text[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:14px;opacity:.9;font-weight:400}.navigation-controls[_ngcontent-%COMP%]{display:flex;justify-content:center}.start-navigation[_ngcontent-%COMP%]{margin:10px}.start-navigation[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 12px;--padding-top: 12px;--padding-bottom: 12px;font-weight:600}.loading-navigation[_ngcontent-%COMP%]{text-align:center;padding:20px;margin:10px}.loading-navigation[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-bottom:12px}.loading-navigation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:14px;margin:0}@media (max-width: 480px){.route-stats[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.stat[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;text-align:left}.stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], .stat[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{display:inline}.instruction-content[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.maneuver-icon[_ngcontent-%COMP%]{margin-right:0;margin-bottom:12px}}.current-instruction[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translateY(-10px);opacity:0}to{transform:translateY(0);opacity:1}}.navigation-container[_ngcontent-%COMP%]{position:relative}.navigation-container[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:-2px;background:linear-gradient(45deg,var(--ion-color-primary),var(--ion-color-secondary));border-radius:14px;z-index:-1;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:.3}50%{opacity:.6}}']})}}return n})();export{K as a};
