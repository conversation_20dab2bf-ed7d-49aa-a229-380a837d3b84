import{s as I,t as L}from"./chunk-IFNCDCK6.js";import{a as d}from"./chunk-KW2BML7M.js";import{a as j}from"./chunk-HC6MZPB3.js";import{a as W,b as T,k as B,q as S,r as g}from"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import{a as v,b as G}from"./chunk-GIGBYVJT.js";import{a as A,b as p}from"./chunk-ZJ5IMUT4.js";import{b as w,f as s,g as x,j as O,k as u}from"./chunk-SGSBBWFA.js";import{e as C,m as M,n as m,p as E}from"./chunk-UYQ7EZNZ.js";import{b as P}from"./chunk-OBXDPQ3V.js";import{a as D}from"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import{a as f,f as k}from"./chunk-BAKMWPBW.js";import{g as r}from"./chunk-2R6CW7ES.js";var U=":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}",$=U,q=":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}",J=q,Q="cubic-bezier(0.32,0.72,0,1)",Y="cubic-bezier(0.0,0.0,0.2,1)",Z="cubic-bezier(1, 0, 0.68, 0.28)",ee="cubic-bezier(0.4, 0, 0.6, 1)",te=class{constructor(e){w(this,e),this.ionWillOpen=u(this,"ionWillOpen",7),this.ionWillClose=u(this,"ionWillClose",7),this.ionDidOpen=u(this,"ionDidOpen",7),this.ionDidClose=u(this,"ionDidClose",7),this.ionMenuChange=u(this,"ionMenuChange",7),this.lastOnEnd=0,this.blocker=P.createBlocker({disableScroll:!0}),this.didLoad=!1,this.operationCancelled=!1,this.isAnimating=!1,this._isOpen=!1,this.inheritedAttributes={},this.handleFocus=t=>{let i=B(document);i&&!i.contains(this.el)||this.trapKeyboardFocus(t,document)},this.isPaneVisible=!1,this.isEndSide=!1,this.contentId=void 0,this.menuId=void 0,this.type=void 0,this.disabled=!1,this.side="start",this.swipeGesture=!0,this.maxEdgeStart=50}typeChanged(e,t){let i=this.contentEl;i&&(t!==void 0&&i.classList.remove(`menu-content-${t}`),i.classList.add(`menu-content-${e}`),i.removeAttribute("style")),this.menuInnerEl&&this.menuInnerEl.removeAttribute("style"),this.animation=void 0}disabledChanged(){this.updateState(),this.ionMenuChange.emit({disabled:this.disabled,open:this._isOpen})}sideChanged(){this.isEndSide=E(this.side),this.animation=void 0}swipeGestureChanged(){this.updateState()}connectedCallback(){return r(this,null,function*(){typeof customElements<"u"&&customElements!=null&&(yield customElements.whenDefined("ion-menu")),this.type===void 0&&(this.type=f.get("menuType","overlay"));let e=this.contentId!==void 0?document.getElementById(this.contentId):null;if(e===null){k('[ion-menu] - Must have a "content" element to listen for drag events on.');return}this.el.contains(e)&&k(`[ion-menu] - The "contentId" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`),this.contentEl=e,e.classList.add("menu-content"),this.typeChanged(this.type,void 0),this.sideChanged(),d._register(this),this.menuChanged(),this.gesture=(yield import("./chunk-F5F7W64E.js")).createGesture({el:document,gestureName:"menu-swipe",gesturePriority:30,threshold:10,blurOnStart:!0,canStart:t=>this.canStart(t),onWillStart:()=>this.onWillStart(),onStart:()=>this.onStart(),onMove:t=>this.onMove(t),onEnd:t=>this.onEnd(t)}),this.updateState()})}componentWillLoad(){this.inheritedAttributes=C(this.el)}componentDidLoad(){return r(this,null,function*(){this.didLoad=!0;let e=this.el.closest("ion-split-pane");e!==null&&(this.isPaneVisible=yield e.isVisible()),this.menuChanged(),this.updateState()})}menuChanged(){this.didLoad&&this.ionMenuChange.emit({disabled:this.disabled,open:this._isOpen})}disconnectedCallback(){return r(this,null,function*(){yield this.close(!1),this.blocker.destroy(),d._unregister(this),this.animation&&this.animation.destroy(),this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.animation=void 0,this.contentEl=void 0})}onSplitPaneChanged(e){let t=this.el.closest("ion-split-pane");t!==null&&t===e.target&&(this.isPaneVisible=e.detail.visible,this.updateState())}onBackdropClick(e){this._isOpen&&this.lastOnEnd<e.timeStamp-100&&(e.composedPath&&!e.composedPath().includes(this.menuInnerEl))&&(e.preventDefault(),e.stopPropagation(),this.close(void 0,S))}onKeydown(e){e.key==="Escape"&&this.close(void 0,S)}isOpen(){return Promise.resolve(this._isOpen)}isActive(){return Promise.resolve(this._isActive())}open(e=!0){return this.setOpen(!0,e)}close(e=!0,t){return this.setOpen(!1,e,t)}toggle(e=!0){return this.setOpen(!this._isOpen,e)}setOpen(e,t=!0,i){return d._setOpen(this,e,t,i)}trapKeyboardFocus(e,t){let i=e.target;if(i)if(this.el.contains(i))this.lastFocus=i;else{let{el:n}=this;W(n),this.lastFocus===t.activeElement&&T(n)}}_setOpen(e,t=!0,i){return r(this,null,function*(){return!this._isActive()||this.isAnimating||e===this._isOpen?!1:(this.beforeAnimation(e,i),yield this.loadAnimation(),yield this.startAnimation(e,t),this.operationCancelled?(this.operationCancelled=!1,!1):(this.afterAnimation(e,i),!0))})}loadAnimation(){return r(this,null,function*(){let e=this.menuInnerEl.offsetWidth,t=E(this.side);if(e===this.width&&this.animation!==void 0&&t===this.isEndSide)return;this.width=e,this.isEndSide=t,this.animation&&(this.animation.destroy(),this.animation=void 0);let i=this.animation=yield d._createAnimation(this.type,this);f.getBoolean("animated",!0)||i.duration(0),i.fill("both")})}startAnimation(e,t){return r(this,null,function*(){let i=!e,n=p(this),a=n==="ios"?Q:Y,l=n==="ios"?Z:ee,o=this.animation.direction(i?"reverse":"normal").easing(i?l:a);t?yield o.play():o.play({sync:!0}),o.getDirection()==="reverse"&&o.direction("normal")})}_isActive(){return!this.disabled&&!this.isPaneVisible}canSwipe(){return this.swipeGesture&&!this.isAnimating&&this._isActive()}canStart(e){return!!document.querySelector("ion-modal.show-modal")||!this.canSwipe()?!1:this._isOpen?!0:d._getOpenSync()?!1:ie(window,e.currentX,this.isEndSide,this.maxEdgeStart)}onWillStart(){return this.beforeAnimation(!this._isOpen,g),this.loadAnimation()}onStart(){if(!this.isAnimating||!this.animation){m(!1,"isAnimating has to be true");return}this.animation.progressStart(!0,this._isOpen?1:0)}onMove(e){if(!this.isAnimating||!this.animation){m(!1,"isAnimating has to be true");return}let i=R(e.deltaX,this._isOpen,this.isEndSide)/this.width;this.animation.progressStep(this._isOpen?1-i:i)}onEnd(e){if(!this.isAnimating||!this.animation){m(!1,"isAnimating has to be true");return}let t=this._isOpen,i=this.isEndSide,n=R(e.deltaX,t,i),a=this.width,l=n/a,o=e.velocityX,h=a/2,b=o>=0&&(o>.2||e.deltaX>h),_=o<=0&&(o<-.2||e.deltaX<-h),c=t?i?b:_:i?_:b,z=!t&&c;t&&!c&&(z=!0),this.lastOnEnd=e.currentTime;let y=c?.001:-.001,K=l<0?.01:l;y+=j([0,0],[.4,0],[.6,1],[1,1],M(0,K,.9999))[0]||0;let N=this._isOpen?!c:c;this.animation.easing("cubic-bezier(0.4, 0.0, 0.6, 1)").onFinish(()=>this.afterAnimation(z,g),{oneTimeCallback:!0}).progressEnd(N?1:0,this._isOpen?1-y:y,300)}beforeAnimation(e,t){m(!this.isAnimating,"_before() should not be called while animating"),A("android")&&this.el.setAttribute("aria-hidden","true"),this.el.classList.add(V),this.el.setAttribute("tabindex","0"),this.backdropEl&&this.backdropEl.classList.add(F),this.contentEl&&(this.contentEl.classList.add(X),this.contentEl.setAttribute("aria-hidden","true")),this.blocker.block(),this.isAnimating=!0,e?this.ionWillOpen.emit():this.ionWillClose.emit({role:t})}afterAnimation(e,t){var i;this._isOpen=e,this.isAnimating=!1,this._isOpen||this.blocker.unblock(),e?(A("android")&&this.el.removeAttribute("aria-hidden"),this.ionDidOpen.emit(),((i=document.activeElement)===null||i===void 0?void 0:i.closest("ion-menu"))!==this.el&&this.el.focus(),document.addEventListener("focus",this.handleFocus,!0)):(this.el.removeAttribute("aria-hidden"),this.el.classList.remove(V),this.el.removeAttribute("tabindex"),this.contentEl&&(this.contentEl.classList.remove(X),this.contentEl.removeAttribute("aria-hidden")),this.backdropEl&&this.backdropEl.classList.remove(F),this.animation&&this.animation.stop(),this.ionDidClose.emit({role:t}),document.removeEventListener("focus",this.handleFocus,!0))}updateState(){let e=this._isActive();this.gesture&&this.gesture.enable(e&&this.swipeGesture),e||(this.isAnimating&&(this.operationCancelled=!0),this.afterAnimation(!1,g))}render(){let{type:e,disabled:t,el:i,isPaneVisible:n,inheritedAttributes:a,side:l}=this,o=p(this);return s(x,{key:"0a2ba4ff5600b80b54d1b5b45124779c6aa0d2f2",onKeyDown:D()?null:this.onKeydown,role:"navigation","aria-label":a["aria-label"]||"menu",class:{[o]:!0,[`menu-type-${e}`]:!0,"menu-enabled":!t,[`menu-side-${l}`]:!0,"menu-pane-visible":n,"split-pane-side":v("ion-split-pane",i)}},s("div",{key:"40a222bcde4b959abc9939c44e89ea0cf8967aba",class:"menu-inner",part:"container",ref:h=>this.menuInnerEl=h},s("slot",{key:"6a7ec5583294bb314990ff4ce6f25045652c07cb"})),s("ion-backdrop",{key:"95f1e87237f3cc24845d91b744f935bad6bb460d",ref:h=>this.backdropEl=h,class:"menu-backdrop",tappable:!1,stopPropagation:!1,part:"backdrop"}))}get el(){return O(this)}static get watchers(){return{type:["typeChanged"],disabled:["disabledChanged"],side:["sideChanged"],swipeGesture:["swipeGestureChanged"]}}},R=(e,t,i)=>Math.max(0,t!==i?-e:e),ie=(e,t,i,n)=>i?t>=e.innerWidth-n:t<=n,V="show-menu",F="show-backdrop",X="menu-content-open";te.style={ios:$,md:J};var H=e=>r(void 0,null,function*(){let t=yield d.get(e);return!!(t&&(yield t.isActive()))}),ne=':host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}',oe=ne,se=':host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}',ae=se,Ce=(()=>{let e=class{constructor(t){w(this,t),this.inheritedAttributes={},this.onClick=()=>r(this,null,function*(){return d.toggle(this.menu)}),this.visible=!1,this.color=void 0,this.disabled=!1,this.menu=void 0,this.autoHide=!0,this.type="button"}componentWillLoad(){this.inheritedAttributes=C(this.el)}componentDidLoad(){this.visibilityChanged()}visibilityChanged(){return r(this,null,function*(){this.visible=yield H(this.menu)})}render(){let{color:t,disabled:i,inheritedAttributes:n}=this,a=p(this),l=f.get("menuIcon",a==="ios"?I:L),o=this.autoHide&&!this.visible,h={type:this.type},b=n["aria-label"]||"menu";return s(x,{key:"3cde3704f28eb275f4a5ff2985bbb68c1024e79c",onClick:this.onClick,"aria-disabled":i?"true":null,"aria-hidden":o?"true":null,class:G(t,{[a]:!0,button:!0,"menu-button-hidden":o,"menu-button-disabled":i,"in-toolbar":v("ion-toolbar",this.el),"in-toolbar-color":v("ion-toolbar[color]",this.el),"ion-activatable":!0,"ion-focusable":!0})},s("button",Object.assign({key:"a02a3374288bd1759b6e352ada8eab0d45c6422f"},h,{disabled:i,class:"button-native",part:"native","aria-label":b}),s("span",{key:"ba699f2277a4e7b27ce5e42faeefc53d8805bb43",class:"button-inner"},s("slot",{key:"829fe6cbdeb173f50d1a670389d1565baa6273e4"},s("ion-icon",{key:"a9a9f7b8dcffc648a8429fe0adbe766869de72fd",part:"icon",icon:l,mode:a,lazy:!1,"aria-hidden":"true"}))),a==="md"&&s("ion-ripple-effect",{key:"48deca9a771a249f2fc76eaa8b9741c8d66d8355",type:"unbounded"})))}get el(){return O(this)}};return e.style={ios:oe,md:ae},e})(),re=":host(.menu-toggle-hidden){display:none}",de=re,Ee=(()=>{let e=class{constructor(t){w(this,t),this.onClick=()=>d.toggle(this.menu),this.visible=!1,this.menu=void 0,this.autoHide=!0}connectedCallback(){this.visibilityChanged()}visibilityChanged(){return r(this,null,function*(){this.visible=yield H(this.menu)})}render(){let t=p(this),i=this.autoHide&&!this.visible;return s(x,{key:"88e88fa13ac7f10ba3acfe378bd11cda0c7e2749",onClick:this.onClick,"aria-hidden":i?"true":null,class:{[t]:!0,"menu-toggle-hidden":i}},s("slot",{key:"0a14c7b63eda64702d2fd1b4bc7db4809892842d"}))}};return e.style=de,e})();export{te as ion_menu,Ce as ion_menu_button,Ee as ion_menu_toggle};
