<?php $__env->startSection('title', 'Evacuation Management Dashboard'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<style>
input#searchInput, #searchInput, input[type="text"]#searchInput {
    border: 2px solid #0ea5e9 !important;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: none !important;
}
input#searchInput:focus, #searchInput:focus, input[type="text"]#searchInput:focus,
input#searchInput:hover, #searchInput:hover, input[type="text"]#searchInput:hover {
    border-color: #0284c7 !important;
    box-shadow: 0 0 0 2px #38bdf8 !important;
    outline: none !important;
    background: #fff !important;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex-cflexol lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-building text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-black-600">
                           
                                Evacuation Center Management Dashboard
                            
                        </h1>
                        <p class="text-gray-600 mt-1">Manage and monitor evacuation centers</p>
                    </div>
                </div>
               
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
 
            <!-- Total Centers -->
            <a href="<?php echo e(route('components.evacuation_management.centers-list', ['type' => 'all'])); ?>" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Centers</p>
                        <h3 class="text-3xl font-bold text-gray-900 mt-2"><?php echo e($centers->total()); ?></h3>
                    </div>
                    <div class="p-4 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-building text-white text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Active Centers -->
            <a href="<?php echo e(route('components.evacuation_management.centers-list', ['type' => 'active'])); ?>" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-green-200 p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Active Centers</p>
                        <h3 class="text-3xl font-bold text-green-600 mt-2"><?php echo e($activeCenters); ?></h3>
                    </div>
                    <div class="p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-check-circle text-white text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Inactive Centers -->
            <a href="<?php echo e(route('components.evacuation_management.centers-list', ['type' => 'inactive'])); ?>" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-red-200 p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Inactive Centers</p>
                        <h3 class="text-3xl font-bold text-red-600 mt-2"><?php echo e($inactiveCenters); ?></h3>
                    </div>
                    <div class="p-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-times-circle text-white text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Under Maintenance -->
            <a href="<?php echo e(route('components.evacuation_management.centers-list', ['type' => 'maintenance'])); ?>" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-yellow-200 p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Under Maintenance</p>
                        <h3 class="text-3xl font-bold text-yellow-600 mt-2"><?php echo e($maintenanceCenters); ?></h3>
                    </div>
                    <div class="p-4 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-xl shadow-lg">
                        <i class="fas fa-wrench text-white text-2xl"></i>
                    </div>
                </div>
            </a>
         </div>

        <!-- Search and Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8 mb-8">
            <!-- Header with Add Button -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-search text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">Find Evacuation Centers</h2>
                        <p class="text-gray-600 mt-1">Begin your search below</p>
                    </div>
                </div>
                <a href="<?php echo e(route('components.evacuation_management.add-evacuation-center')); ?>"
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-plus"></i>
                    Add New Center
                </a>
            </div>

            <!-- Search Bar - Top, Full Width -->
            <div class="mb-6">
                
                <div class="relative">
                    <input type="text" id="searchInput" 
                           class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-gray-700 font-medium transition-all duration-200 hover:border-blue-400 pl-12"
                           placeholder="Search by center name, address, or barangay...">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Controls - Bottom, Side by Side -->
            <form action="<?php echo e(route('components.evacuation_management.evacuation-dashboard')); ?>" method="GET">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Barangay Filter -->
                    <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('super_admin')): ?>
                    <div>
                        <label for="barangay" class="block text-lg font-semibold text-sky-600 mb-3">Filter by Barangay</label>
                        <select name="barangay" id="barangay"
                                class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-gray-700 font-medium transition-all duration-200 hover:border-blue-400"
                                onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            <?php $__currentLoopData = $barangays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $barangay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($barangay); ?>" <?php echo e($selectedBarangay == $barangay ? 'selected' : ''); ?>>
                                    <?php echo e($barangay); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <!-- Status Filter -->
                    <div>
                        <label for="statusFilter" class="block text-lg font-semibold text-sky-600 mb-3">Filter by Status</label>
                        <select name="status" id="statusFilter" 
                                class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-gray-700 font-medium transition-all duration-200 hover:border-blue-400"
                                onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="Active" <?php echo e(request('status') == 'Active' ? 'selected' : ''); ?>>Active</option>
                            <option value="Inactive" <?php echo e(request('status') == 'Inactive' ? 'selected' : ''); ?>>Inactive</option>
                            <option value="Under Maintenance" <?php echo e(request('status') == 'Under Maintenance' ? 'selected' : ''); ?>>Under Maintenance</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <!-- Table Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="bg-blue-500 text-white text-base leading-normal">
                            <th class="py-5 px-8 text-left font-semibold">Center Name</th>
                            <th class="py-5 px-8 text-left font-semibold">Address</th>
                            <th class="py-5 px-8 text-center font-semibold">Capacity</th>
                            <th class="py-5 px-8 text-center font-semibold">Contact Number</th>
                            <th class="py-5 px-8 text-center font-semibold">Disaster Type</th>
                            <th class="py-5 px-8 text-center font-semibold">Status</th>
                            <th class="py-5 px-8 text-center font-semibold">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-600 text-base" id="evacuationTableBody">
                        <?php $__empty_1 = true; $__currentLoopData = $centers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $center): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="evacuation-row border-b border-sky-100 hover:bg-sky-50/50 transition-colors" 
                                data-name="<?php echo e(strtolower($center->name)); ?>"
                                data-address="<?php echo e(strtolower($center->street_name . ' ' . $center->barangay . ' ' . $center->city . ' ' . $center->province)); ?>">
                                <td class="py-5 px-8">
                                    <div class="flex flex-col">
                                        <span class="font-medium text-gray-900"><?php echo e($center->name); ?></span>
                                    </div>
                                </td>
                                <td class="py-5 px-8 text-left">
                                    <?php
                                        $addressParts = [
                                            $center->building_name, // Building name from map search
                                            $center->street_name,
                                            $center->barangay,
                                            $center->city,
                                            $center->province,
                                        ];

                                        // Clean up parts and remove duplicates/empties
                                        $addressParts = array_map('trim', $addressParts);
                                        $addressParts = array_filter($addressParts);
                                        $addressParts = array_unique($addressParts);
                                        
                                        $address = implode(', ', $addressParts);
                                        
                                        // Ensure "Philippines" is at the end, but only once.
                                        $address = trim(str_ireplace('Philippines', '', $address), ' ,');
                                        $address .= ', Philippines';
                                    ?>
                                    <?php echo e($address); ?>

                                </td>
                                <td class="py-5 px-8 text-center"><?php echo e($center->capacity); ?></td>
                                <td class="py-5 px-8 text-center"><?php echo e($center->contact); ?></td>
                                <td class="py-5 px-8 text-center">
                                    <div class="flex flex-wrap justify-center gap-1">
                                        <?php if(is_array($center->disaster_type)): ?>
                                            <?php $__currentLoopData = $center->disaster_type; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $badgeClass = 'bg-gray-100 text-gray-800';
                                                    $displayText = $type;
                                                    // Handle custom disaster type text
                                                    if (strpos($type, 'Others:') === 0) {
                                                        $badgeClass = 'bg-purple-100 text-purple-800';
                                                        $displayText = trim(str_replace('Others:', '', $type));
                                                    } 
                                                    // Handle 'Others' without custom text
                                                    elseif ($type == 'Others') {
                                                        $badgeClass = 'bg-purple-100 text-purple-800';
                                                    }
                                                    elseif ($type == 'Typhoon') {
                                                        $badgeClass = 'bg-green-100 text-green-800';
                                                    } elseif ($type == 'Flood') {
                                                        $badgeClass = 'bg-blue-100 text-blue-800';
                                                    } elseif ($type == 'Fire') {
                                                        $badgeClass = 'bg-red-100 text-red-800';
                                                    } elseif ($type == 'Earthquake') {
                                                        $badgeClass = 'bg-orange-100 text-orange-800';
                                                    } elseif ($type == 'Landslide') {
                                                        $badgeClass = 'bg-amber-100 text-amber-800';
                                                    }
                                                ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?php echo e($badgeClass); ?>">
                                                    <?php echo e($displayText); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                                <?php if($center->disaster_type == 'Typhoon'): ?> bg-green-100 text-green-800
                                                <?php elseif($center->disaster_type == 'Flood'): ?> bg-blue-100 text-blue-800
                                                <?php elseif($center->disaster_type == 'Fire'): ?> bg-red-100 text-red-800
                                                <?php elseif($center->disaster_type == 'Earthquake'): ?> bg-orange-100 text-orange-800
                                                <?php elseif($center->disaster_type == 'Landslide'): ?> bg-amber-100 text-amber-800
                                                <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                <?php echo e($center->disaster_type); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="py-5 px-8 text-center">
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                        <?php if($center->status === 'Active'): ?>
                                            bg-green-100 text-green-800
                                        <?php elseif($center->status === 'Under Maintenance'): ?>
                                            bg-yellow-100 text-yellow-800
                                        <?php else: ?>
                                            bg-red-100 text-red-800
                                        <?php endif; ?>
                                    ">
                                        <?php echo e($center->status); ?>

                                    </span>
                                </td>
                                <td class="py-5 px-8 text-center">
                                    <div class="flex justify-center gap-2">
                                        <button onclick="openViewModal(<?php echo e($center->id); ?>)" 
                                                class="inline-flex items-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                            <i class="fas fa-eye text-xs"></i>
                                            View
                                        </button>
                                        <a href="<?php echo e(route('components.evacuation_management.edit-evacuation-center', $center->id)); ?>" 
                                           class="inline-flex items-center gap-1 bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                            <i class="fas fa-edit text-xs"></i>
                                            Edit
                                        </a>
                                        <button onclick="openDeleteModal(<?php echo e($center->id); ?>, '<?php echo e($center->name); ?>')" 
                                                class="inline-flex items-center gap-1 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                            <i class="fas fa-trash text-xs"></i>
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="py-8 text-center text-gray-500">
                                    No evacuation centers found
                                </td>
                            </tr>
                        <?php endif; ?>
                        <!-- Add no results message -->
                        <tr id="noResultsMessage" class="hidden">
                            <td colspan="7" class="py-8 text-center text-gray-500">
                                No evacuation centers found matching "<span id="searchTermDisplay"></span>"
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($centers->hasPages()): ?>
                <div class="px-8 py-6 bg-gradient-to-r from-sky-50 to-blue-50 border-t border-sky-100">
                    <?php echo e($centers->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4 hidden overflow-y-auto">
    <div class="relative mx-auto w-full max-w-3xl bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-semibold text-white">Evacuation Center Details</h3>
                <button onclick="closeViewModal()" class="text-white hover:text-sky-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Content -->
        <div id="viewModalContent" class="p-6 max-h-[calc(100vh-150px)] overflow-y-auto">
            <!-- Content will be dynamically inserted here -->
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-6">
    <div class="relative mx-auto p-8 border w-[480px] shadow-lg rounded-2xl bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Delete Confirmation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-gray-600">
                    Are you sure you want to delete <span id="centerNameToDelete" class="font-medium"></span>?
                </p>
                <p class="text-gray-500 text-sm mt-1">
                    This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <form id="deleteForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" 
                            class="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white text-sm font-medium rounded-lg shadow-lg transition-all duration-200">
                        Delete
                    </button>
                </form>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg shadow-sm hover:bg-gray-200 transition-all duration-200">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Multi-disaster': '#6b7280'
};

function openDeleteModal(id, name) {
    document.getElementById('centerNameToDelete').textContent = name;
    const form = document.getElementById('deleteForm');
    form.dataset.id = id;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function openViewModal(id) {
    // Show modal immediately with loading spinner
    const viewModal = document.getElementById('viewModal');
    const modalContent = document.getElementById('viewModalContent');
    
    viewModal.classList.remove('hidden');
    
    // Add loading indicator
    modalContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
        </div>
    `;
    
    // Fetch center details
    fetch(`/api/evacuation-centers/${id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Update modal content
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <div class="border-b border-sky-100 pb-4">
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Center Name</h4>
                        <p class="text-lg font-bold text-gray-900">${data.name}</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Capacity</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-users text-sky-500"></i>
                                <p class="text-gray-900">${data.capacity}</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Contact Info</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-phone text-sky-500"></i>
                                <p class="text-gray-900">${data.contact}</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Status</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium ${
                                data.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }">
                                <i class="fas ${data.status === 'Active' ? 'fa-check-circle' : 'fa-times-circle'} mr-1"></i>
                                ${data.status}
                            </span>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Disaster Type</h4>
                            <div class="flex flex-wrap gap-2">
                                ${getDisasterTypeBadges(data.disaster_type)}
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Address</h4>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-map-marker-alt text-sky-500 mt-1"></i>
                            <p class="text-gray-900">${data.building_name ? data.building_name + ', ' : ''}${data.street_name}, ${data.barangay}, ${data.city}, ${data.province}, Philippines</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Location</h4>
                        <div id="viewMap" class="h-80 w-full rounded-xl border border-sky-200 shadow-sm bg-gray-50"></div>
                    </div>
                </div>
            `;

            // Initialize map if coordinates are available
            if (data.latitude && data.longitude) {
                setTimeout(() => {
                    const map = L.map('viewMap', {
                        zoomControl: true,
                        maxZoom: 19,
                        minZoom: 1,
                        scrollWheelZoom: true
                    }).setView([data.latitude, data.longitude], 15);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19
                    }).addTo(map);
                    
                    // Determine marker color
                    let markerColor;
                    const types = data.disaster_type;

                    if (Array.isArray(types) && types.length > 1) {
                        markerColor = disasterTypeColors['Multi-disaster'];
                    } else if (Array.isArray(types) && types.length === 1) {
                        const type = types[0];
                        if (type.startsWith('Others:')) {
                            markerColor = disasterTypeColors['Others'];
                        } else {
                            markerColor = disasterTypeColors[type] || disasterTypeColors['Multi-disaster'];
                        }
                    } else {
                        markerColor = disasterTypeColors['Multi-disaster']; // Default
                    }

                    // Add marker with custom icon
                    const markerIcon = L.divIcon({
                        className: 'custom-marker',
                        html: `<div style="background-color: ${markerColor};" class="w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                                <i class="fas fa-building text-white text-base"></i>
                               </div>`,
                        iconSize: [32, 32],
                        iconAnchor: [16, 32]
                    });
                    
                    L.marker([data.latitude, data.longitude], { icon: markerIcon }).addTo(map);
                }, 100); // Small delay to ensure the container is ready
            }
        })
        .catch(error => {
            console.error('Error fetching center details:', error);
            modalContent.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-500 text-5xl mb-4">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to Load Data</h3>
                    <p class="text-gray-500">There was an error loading the center details. Please try again.</p>
                    <button onclick="closeViewModal()" class="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        Close
                    </button>
                </div>
            `;
        });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

// Helper function to generate disaster type badges
function getDisasterTypeBadges(disasterTypes) {
    if (!disasterTypes) return '';
    
    const types = Array.isArray(disasterTypes) ? disasterTypes : [disasterTypes];
    return types.map(type => {
        let colorClass = 'bg-gray-100 text-gray-800';
        let displayText = type;

        if (type.startsWith('Others:')) {
            colorClass = 'bg-purple-100 text-purple-800';
            displayText = type.replace('Others:', '').trim();
        } else {
            switch(type) {
                case 'Typhoon': colorClass = 'bg-green-100 text-green-800'; break;
                case 'Flood': colorClass = 'bg-blue-100 text-blue-800'; break;
                case 'Fire': colorClass = 'bg-red-100 text-red-800'; break;
                case 'Earthquake': colorClass = 'bg-orange-100 text-orange-800'; break;
                case 'Landslide': colorClass = 'bg-amber-100 text-amber-800'; break;
            }
        }
        
        return `<span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium ${colorClass}">${displayText}</span>`;
    }).join('');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const deleteModal = document.getElementById('deleteModal');
    const viewModal = document.getElementById('viewModal');
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
    if (event.target === viewModal) {
        closeViewModal();
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase().trim();
    const rows = document.querySelectorAll('.evacuation-row');
    let hasResults = false;

    rows.forEach(row => {
        const name = row.dataset.name.toLowerCase();
        const address = row.dataset.address.toLowerCase();
        
        // Check if name or address includes the search term
        if (name.includes(searchTerm) || address.includes(searchTerm)) {
            row.style.display = '';
            hasResults = true;
        } else {
            row.style.display = 'none';
        }
    });

    // Show/hide no results message
    const noResultsMessage = document.getElementById('noResultsMessage');
    if (noResultsMessage) {
        if (!hasResults && searchTerm) {
            noResultsMessage.classList.remove('hidden');
            document.getElementById('searchTermDisplay').textContent = searchTerm;
        } else {
            noResultsMessage.classList.add('hidden');
        }
    }
});

// Auto-submit search functionality
let searchTimeout;
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const searchTerm = this.value.trim();
    
    // Submit form after 500ms delay to avoid too many requests
    searchTimeout = setTimeout(() => {
        if (searchTerm.length > 0 || searchTerm.length === 0) {
            this.form.submit();
        }
    }, 500);
});

// Handle Enter key for immediate search
document.getElementById('searchInput').addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        clearTimeout(searchTimeout);
        this.form.submit();
    }
});

// Close suggestions when clicking outside
document.addEventListener('click', function(e) {
    const searchSuggestions = document.getElementById('searchSuggestions');
    const searchInput = document.getElementById('searchInput');
    if (searchSuggestions && searchInput && !searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
        searchSuggestions.classList.add('hidden');
    }
});

// Add error handling for delete form submission
document.getElementById('deleteForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const id = form.dataset.id;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Disable button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
    
    fetch(`/evacuation/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            console.error('HTTP Status:', response.status);
            return response.json().then(errorData => {
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            throw new Error(data.message || 'Failed to delete evacuation center');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error deleting evacuation center: ' + error.message);
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\junrelCAPSTONE\Capstone\WebAlerto\resources\views/components/evacuation_management/evacuation-dashboard.blade.php ENDPATH**/ ?>