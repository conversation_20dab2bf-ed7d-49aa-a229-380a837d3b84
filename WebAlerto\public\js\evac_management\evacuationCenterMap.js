/**
 * Shared map and location handling logic for adding and editing evacuation centers.
 */
document.addEventListener('DOMContentLoaded', function () {
    const mapElement = document.getElementById('map');
    if (!mapElement) return;

    let map;
    let marker;
    const searchInput = document.getElementById('search');
    const searchButton = document.getElementById('searchButton');
    const searchResultsContainer = document.getElementById('searchResults');
    
    const initialLat = document.getElementById('latitude').value || 10.3157; // Default to Cebu City
    const initialLng = document.getElementById('longitude').value || 123.8854;

    function initializeMap() {
        try {
            map = L.map('map', {
                zoomControl: true,
                maxZoom: 19,
                minZoom: 5,
                scrollWheelZoom: true
            }).setView([initialLat, initialLng], 15);

            <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19
            }).addTo(map);

            marker = L.marker([initialLat, initialLng], {
                draggable: true,
                icon: L.divIcon({
                    className: 'custom-marker',
                    html: `<div class="w-10 h-10 bg-red-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                                <i class="fas fa-map-marker-alt text-white text-xl"></i>
                           </div>`,
                    iconSize: [40, 40],
                    iconAnchor: [20, 40]
                })
            }).addTo(map);

            map.on('click', function (e) {
                marker.setLatLng(e.latlng);
                updateLocationDetails(e.latlng.lat, e.latlng.lng);
            });

            marker.on('dragend', function (e) {
                const latlng = e.target.getLatLng();
                updateLocationDetails(latlng.lat, latlng.lng);
            });
            
            // Initial load if coords exist
            if (document.getElementById('latitude').value) {
                 updateLocationDetails(initialLat, initialLng);
            }

            document.getElementById('mapLoadingIndicator').classList.add('hidden');
        } catch (error) {
            console.error("Map initialization failed:", error);
            document.getElementById('mapLoadingIndicator').innerHTML = '<p class="text-red-500 font-semibold">Map failed to load. Please refresh.</p>';
        }
    }

    function updateLocationDetails(lat, lng) {
        document.getElementById('latitude').value = lat.toFixed(7);
        document.getElementById('longitude').value = lng.toFixed(7);
        document.getElementById('selectedLatitude').textContent = lat.toFixed(7);
        document.getElementById('selectedLongitude').textContent = lng.toFixed(7);
        document.getElementById('selectedAddress').textContent = 'Fetching address...';

        fetch(`/reverse-geocode?lat=${lat}&lng=${lng}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                populateLocationFields(data);
            })
            .catch(error => {
                console.error('Reverse geocoding failed:', error);
                document.getElementById('selectedAddress').textContent = 'Could not determine address.';
            });
    }

    function populateLocationFields(data) {
        // Normalize and clean up the data to prevent duplicates
        const cleanData = {
            building_name: (data.building_name || '').trim(),
            street: (data.street || '').trim(),
            barangay: (data.barangay || '').trim(),
            city: (data.city || '').trim(),
            province: (data.province || '').trim()
        };

        // Helper function to check if a value is a substring of another (case-insensitive)
        const isSubstringOf = (value1, value2) => {
            if (!value1 || !value2) return false;
            const v1 = value1.toLowerCase().trim();
            const v2 = value2.toLowerCase().trim();
            return v1.includes(v2) || v2.includes(v1);
        };

        // Helper function to check if a value is already represented in existing parts
        const isAlreadyRepresented = (value, existingParts) => {
            if (!value) return true;
            return existingParts.some(part => isSubstringOf(value, part));
        };

        // Build address parts intelligently to avoid duplication
        let addressParts = [];

        // Start with the most specific information
        if (cleanData.building_name) {
            addressParts.push(cleanData.building_name);
        }

        // Add street only if it's not already represented in building name
        if (cleanData.street && !isAlreadyRepresented(cleanData.street, addressParts)) {
            addressParts.push(cleanData.street);
        }

        // Add barangay only if it's not already represented
        if (cleanData.barangay && !isAlreadyRepresented(cleanData.barangay, addressParts)) {
            addressParts.push(cleanData.barangay);
        }

        // Add city only if it's not already represented
        if (cleanData.city && !isAlreadyRepresented(cleanData.city, addressParts)) {
            addressParts.push(cleanData.city);
        }

        // Add province only if it's not already represented
        if (cleanData.province && !isAlreadyRepresented(cleanData.province, addressParts)) {
            addressParts.push(cleanData.province);
        }

        // Create a clean, concise address display
        const displayAddress = addressParts.length > 0
            ? addressParts.join(', ')
            : (data.full_address || 'Address not found');

        document.getElementById('selectedAddress').textContent = displayAddress;

        // Set center name if it's empty and a building name was found
        const centerNameInput = document.getElementById('name');
        if (centerNameInput && !centerNameInput.value && data.building_name) {
            centerNameInput.value = data.building_name;
        }

        // Populate hidden fields for submission
        document.getElementById('building_name').value = data.building_name || '';
        document.getElementById('street_name').value = data.street || '';
        document.getElementById('barangay').value = data.barangay || '';
        document.getElementById('city').value = data.city || '';
        document.getElementById('province').value = data.province || '';
        document.getElementById('postal_code').value = data.postal_code || '';
    }

    function handleSearch() {
        const query = searchInput.value;
        if (query.length < 3) return;

        fetch(`/geocode?address=${encodeURIComponent(query)}&lat=${map.getCenter().lat}&lng=${map.getCenter().lng}`)
            .then(response => response.json())
            .then(data => {
                searchResultsContainer.innerHTML = '';
                searchResultsContainer.classList.remove('hidden');
                if (data.features && data.features.length > 0) {
                    data.features.forEach(feature => {
                        const div = document.createElement('div');
                        div.innerHTML = `<div class="p-3 hover:bg-sky-100 cursor-pointer">
                            <p class="font-medium text-gray-800">${feature.text}</p>
                            <p class="text-sm text-gray-500">${feature.place_name}</p>
                         </div>`;
                        div.onclick = () => {
                            const [lng, lat] = feature.center;
                            const selectedText = feature.text || '';
                            const placeName = feature.place_name || '';

                            map.setView([lat, lng], 17);
                            marker.setLatLng([lat, lng]);

                            // Use the search result data directly to avoid duplication
                            const addressComponents = placeName.split(', ');

                            // Create a structured data object from the search result
                            const searchData = {
                                building_name: selectedText,
                                street: '',
                                barangay: '',
                                city: '',
                                province: '',
                                full_address: placeName
                            };

                            // Try to parse address components from the place_name
                            if (addressComponents.length >= 2) {
                                if (addressComponents.length >= 4) {
                                    searchData.barangay = addressComponents[1]?.trim() || '';
                                    searchData.city = addressComponents[2]?.trim() || '';
                                    searchData.province = addressComponents[3]?.trim() || '';
                                } else if (addressComponents.length >= 3) {
                                    searchData.city = addressComponents[1]?.trim() || '';
                                    searchData.province = addressComponents[2]?.trim() || '';
                                } else {
                                    searchData.city = addressComponents[1]?.trim() || '';
                                }
                            }

                            // Update coordinates
                            document.getElementById('latitude').value = lat.toFixed(7);
                            document.getElementById('longitude').value = lng.toFixed(7);
                            document.getElementById('selectedLatitude').textContent = lat.toFixed(7);
                            document.getElementById('selectedLongitude').textContent = lng.toFixed(7);

                            // Populate fields with the parsed search data
                            populateLocationFields(searchData);

                            searchResultsContainer.classList.add('hidden');
                        };
                        searchResultsContainer.appendChild(div);
                    });
                } else {
                    searchResultsContainer.innerHTML = '<p class="p-3 text-gray-500">No results found.</p>';
                }
            })
            .catch(error => console.error('Geocoding search failed:', error));
    }

    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch();
        }
    });
    searchButton.addEventListener('click', handleSearch);
    
    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchResultsContainer.contains(e.target) && !searchInput.contains(e.target)) {
            searchResultsContainer.classList.add('hidden');
        }
    });

    initializeMap();
}); 