import{$b as u,Ea as l,K as e,L as t,M as n,Nb as b,Wb as c,Xb as p,Y as o,da as r,hc as d,m,qa as s}from"./chunk-N4Y2QYSK.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-2R6CW7ES.js";var I=(()=>{class a{constructor(){}static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275cmp=m({type:a,selectors:[["app-tabs"]],standalone:!0,features:[r],decls:18,vars:0,consts:[["slot","bottom"],["tab","home"],["src","assets/home1.png",1,"tab-icon"],["tab","search"],["src","assets/search1.png",1,"tab-icon"],["tab","map"],["src","assets/map1.png",1,"tab-icon"],["tab","profile"],["src","assets/lamp1.png",1,"tab-icon"]],template:function(i,g){i&1&&(e(0,"ion-tabs")(1,"ion-tab-bar",0)(2,"ion-tab-button",1),n(3,"img",2),e(4,"ion-label"),o(5,"Home"),t()(),e(6,"ion-tab-button",3),n(7,"img",4),e(8,"ion-label"),o(9,"Search"),t()(),e(10,"ion-tab-button",5),n(11,"img",6),e(12,"ion-label"),o(13,"Map"),t()(),e(14,"ion-tab-button",7),n(15,"img",8),e(16,"ion-label"),o(17,"Tips"),t()()()())},dependencies:[d,b,c,p,u,s,l],encapsulation:2})}}return a})();export{I as TabsPage};
