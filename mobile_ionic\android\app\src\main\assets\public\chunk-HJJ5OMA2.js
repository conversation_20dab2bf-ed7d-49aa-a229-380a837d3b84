import{C as g,Eb as _,J as n,Jb as x,K as t,L as l,P as o,X as e,ca as m,gc as h,ib as u,m as s,pa as p,wb as f,ya as b}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-2R6CW7ES.js";var C=(()=>{class i{constructor(r){this.router=r}nextPage(){this.router.navigate(["/onboarding-4"])}previousPage(){this.router.navigate(["/onboarding-2"])}skipOnboarding(){localStorage.setItem("onboardingComplete","true"),this.router.navigate(["/tabs/home"])}static{this.\u0275fac=function(a){return new(a||i)(g(b))}}static{this.\u0275cmp=s({type:i,selectors:[["app-onboarding-3"]],standalone:!0,features:[m],decls:25,vars:0,consts:[[1,"onboarding-bg"],[1,"onboarding-wrapper"],[1,"skip-container"],["fill","clear",1,"skip-btn",3,"click"],[1,"illustration-container"],[1,"payment-illustration"],["src","assets/icon/mapMobile.png","alt","Find Safety",1,"payment-icon"],["name","checkmark-circle",1,"check-icon"],[1,"content-container"],[1,"onboarding-title"],[1,"onboarding-description"],[1,"indicators-container"],[1,"indicator"],[1,"indicator","active"],[1,"button-container"],[1,"nav-buttons"],["fill","clear",1,"back-btn",3,"click"],["expand","block",1,"next-btn",3,"click"]],template:function(a,c){a&1&&(n(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"ion-button",3),o("click",function(){return c.skipOnboarding()}),e(4," Skip "),t()(),n(5,"div",4)(6,"div",5),l(7,"img",6)(8,"ion-icon",7),t()(),n(9,"div",8)(10,"h2",9),e(11,"Find Safety at Your Fingertips"),t(),n(12,"p",10),e(13," Locate the nearest safe zone during emergencies. Stay one step ahead by knowing your safest route before disaster strikes. "),t()(),n(14,"div",11),l(15,"div",12)(16,"div",12)(17,"div",13)(18,"div",12),t(),n(19,"div",14)(20,"div",15)(21,"ion-button",16),o("click",function(){return c.previousPage()}),e(22," Back "),t(),n(23,"ion-button",17),o("click",function(){return c.nextPage()}),e(24," Next "),t()()()()())},dependencies:[h,f,_,x,p,u],styles:[".onboarding-bg[_ngcontent-%COMP%]{--background: white}.onboarding-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;padding:1.5rem;position:relative;justify-content:space-between}.skip-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-bottom:2rem}.skip-btn[_ngcontent-%COMP%]{--color: #6c757d;font-size:1rem}.illustration-container[_ngcontent-%COMP%]{flex:0 0 auto;display:flex;align-items:center;justify-content:center;margin:1rem 0}.payment-illustration[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}.payment-icon[_ngcontent-%COMP%]{width:150px;height:150px;object-fit:contain;filter:drop-shadow(0 4px 8px rgba(255,193,7,.2))}.check-icon[_ngcontent-%COMP%]{position:absolute;top:-20px;right:-20px;font-size:60px;color:#007bff;background:#fff;border-radius:50%}.content-container[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.onboarding-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#212529;margin-bottom:1rem}.onboarding-description[_ngcontent-%COMP%]{font-size:1.1rem;color:#6c757d;line-height:1.5;margin:0;padding:0 1rem}.indicators-container[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:.5rem;margin-bottom:2rem}.indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#dee2e6;transition:all .3s ease}.indicator.active[_ngcontent-%COMP%]{background-color:#007bff;width:24px;border-radius:4px}.button-container[_ngcontent-%COMP%]{margin-bottom:2rem}.nav-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;align-items:center}.back-btn[_ngcontent-%COMP%]{--color: #6c757d;font-size:1rem;flex:0 0 auto}.next-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;font-weight:600;font-size:1.1rem;height:50px;flex:1}"]})}}return i})();export{C as Onboarding3Page};
